// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		184DB4442C2C28B500234BEB /* IoTVideo.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2852C2C28B500234BEB /* IoTVideo.h */; settings = {ATTRIBUTES = (Private, ); }; };
		184DB4452C2C28B500234BEB /* IVAudioCapturable.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2862C2C28B500234BEB /* IVAudioCapturable.h */; settings = {ATTRIBUTES = (Private, ); }; };
		184DB4472C2C28B500234BEB /* IVAudioDecodable.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2882C2C28B500234BEB /* IVAudioDecodable.h */; settings = {ATTRIBUTES = (Private, ); }; };
		184DB4482C2C28B500234BEB /* IVAudioDecoder.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2892C2C28B500234BEB /* IVAudioDecoder.h */; settings = {ATTRIBUTES = (Private, ); }; };
		184DB4492C2C28B500234BEB /* IVAudioEncodable.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB28A2C2C28B500234BEB /* IVAudioEncodable.h */; settings = {ATTRIBUTES = (Private, ); }; };
		184DB44A2C2C28B500234BEB /* IVAudioEncoder.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB28B2C2C28B500234BEB /* IVAudioEncoder.h */; settings = {ATTRIBUTES = (Private, ); }; };
		184DB44B2C2C28B500234BEB /* IVAudioRenderable.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB28C2C2C28B500234BEB /* IVAudioRenderable.h */; settings = {ATTRIBUTES = (Private, ); }; };
		184DB44C2C2C28B500234BEB /* IVAudioUnit.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB28D2C2C28B500234BEB /* IVAudioUnit.h */; settings = {ATTRIBUTES = (Private, ); }; };
		184DB44D2C2C28B500234BEB /* IVAVDefine.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB28E2C2C28B500234BEB /* IVAVDefine.h */; settings = {ATTRIBUTES = (Private, ); }; };
		184DB44E2C2C28B500234BEB /* IVAVRecordable.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB28F2C2C28B500234BEB /* IVAVRecordable.h */; settings = {ATTRIBUTES = (Private, ); }; };
		184DB44F2C2C28B500234BEB /* IVAVRecorder.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2902C2C28B500234BEB /* IVAVRecorder.h */; settings = {ATTRIBUTES = (Private, ); }; };
		184DB4502C2C28B500234BEB /* IVAVUtils.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2912C2C28B500234BEB /* IVAVUtils.h */; settings = {ATTRIBUTES = (Private, ); }; };
		184DB4542C2C28B500234BEB /* IVConnection.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2952C2C28B500234BEB /* IVConnection.h */; settings = {ATTRIBUTES = (Private, ); }; };
		184DB4552C2C28B500234BEB /* IVConstant.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2962C2C28B500234BEB /* IVConstant.h */; settings = {ATTRIBUTES = (Private, ); }; };
		184DB4572C2C28B500234BEB /* IVDeviceMgr.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2982C2C28B500234BEB /* IVDeviceMgr.h */; settings = {ATTRIBUTES = (Private, ); }; };
		184DB4582C2C28B500234BEB /* IVError.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2992C2C28B500234BEB /* IVError.h */; settings = {ATTRIBUTES = (Private, ); }; };
		184DB4592C2C28B500234BEB /* IVFileDownloader.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB29A2C2C28B500234BEB /* IVFileDownloader.h */; settings = {ATTRIBUTES = (Private, ); }; };
		184DB45A2C2C28B500234BEB /* IVLanNetConfig.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB29B2C2C28B500234BEB /* IVLanNetConfig.h */; settings = {ATTRIBUTES = (Private, ); }; };
		184DB45B2C2C28B500234BEB /* IVLivePlayer.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB29C2C2C28B500234BEB /* IVLivePlayer.h */; settings = {ATTRIBUTES = (Private, ); }; };
		184DB45C2C2C28B500234BEB /* IVLog.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB29D2C2C28B500234BEB /* IVLog.h */; settings = {ATTRIBUTES = (Private, ); }; };
		184DB45E2C2C28B500234BEB /* IVMacros.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB29F2C2C28B500234BEB /* IVMacros.h */; settings = {ATTRIBUTES = (Private, ); }; };
		184DB45F2C2C28B500234BEB /* IVMessageMgr.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2A02C2C28B500234BEB /* IVMessageMgr.h */; settings = {ATTRIBUTES = (Private, ); }; };
		184DB4602C2C28B500234BEB /* IVMonitorPlayer.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2A12C2C28B500234BEB /* IVMonitorPlayer.h */; settings = {ATTRIBUTES = (Private, ); }; };
		184DB4622C2C28B500234BEB /* IVNetConfig.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2A32C2C28B500234BEB /* IVNetConfig.h */; settings = {ATTRIBUTES = (Private, ); }; };
		184DB4632C2C28B500234BEB /* IVNetwork_p2p.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2A42C2C28B500234BEB /* IVNetwork_p2p.h */; settings = {ATTRIBUTES = (Private, ); }; };
		184DB4642C2C28B500234BEB /* IVNetworkHelper.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2A52C2C28B500234BEB /* IVNetworkHelper.h */; settings = {ATTRIBUTES = (Private, ); }; };
		184DB4652C2C28B500234BEB /* IVNetworkSign.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2A62C2C28B500234BEB /* IVNetworkSign.h */; settings = {ATTRIBUTES = (Private, ); }; };
		184DB4662C2C28B500234BEB /* IVPlaybackPlayer.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2A72C2C28B500234BEB /* IVPlaybackPlayer.h */; settings = {ATTRIBUTES = (Private, ); }; };
		184DB4672C2C28B500234BEB /* IVPlayer.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2A82C2C28B500234BEB /* IVPlayer.h */; settings = {ATTRIBUTES = (Private, ); }; };
		184DB4682C2C28B500234BEB /* IVQRCodeHelper.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2A92C2C28B500234BEB /* IVQRCodeHelper.h */; settings = {ATTRIBUTES = (Private, ); }; };
		184DB4692C2C28B500234BEB /* IVQRCodeNetConfig.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2AA2C2C28B500234BEB /* IVQRCodeNetConfig.h */; settings = {ATTRIBUTES = (Private, ); }; };
		184DB46A2C2C28B500234BEB /* IVReachability.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2AB2C2C28B500234BEB /* IVReachability.h */; settings = {ATTRIBUTES = (Private, ); }; };
		184DB46C2C2C28B500234BEB /* IVTimer.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2AD2C2C28B500234BEB /* IVTimer.h */; settings = {ATTRIBUTES = (Private, ); }; };
		184DB46D2C2C28B500234BEB /* IVTransmission.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2AE2C2C28B500234BEB /* IVTransmission.h */; settings = {ATTRIBUTES = (Private, ); }; };
		184DB46F2C2C28B500234BEB /* IVUtils.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2B02C2C28B500234BEB /* IVUtils.h */; settings = {ATTRIBUTES = (Private, ); }; };
		184DB4702C2C28B500234BEB /* IVVideoCapturable.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2B12C2C28B500234BEB /* IVVideoCapturable.h */; settings = {ATTRIBUTES = (Private, ); }; };
		184DB4712C2C28B500234BEB /* IVVideoCapture.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2B22C2C28B500234BEB /* IVVideoCapture.h */; settings = {ATTRIBUTES = (Private, ); }; };
		184DB4722C2C28B500234BEB /* IVVideoDecodable.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2B32C2C28B500234BEB /* IVVideoDecodable.h */; settings = {ATTRIBUTES = (Private, ); }; };
		184DB4732C2C28B500234BEB /* IVVideoDecoder.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2B42C2C28B500234BEB /* IVVideoDecoder.h */; settings = {ATTRIBUTES = (Private, ); }; };
		184DB4742C2C28B500234BEB /* IVVideoEncodable.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2B52C2C28B500234BEB /* IVVideoEncodable.h */; settings = {ATTRIBUTES = (Private, ); }; };
		184DB4752C2C28B500234BEB /* IVVideoEncoder.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2B62C2C28B500234BEB /* IVVideoEncoder.h */; settings = {ATTRIBUTES = (Private, ); }; };
		184DB4772C2C28B500234BEB /* IVVideoRender.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2B82C2C28B500234BEB /* IVVideoRender.h */; settings = {ATTRIBUTES = (Private, ); }; };
		184DB4782C2C28B500234BEB /* IVVideoRenderable.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2B92C2C28B500234BEB /* IVVideoRenderable.h */; settings = {ATTRIBUTES = (Private, ); }; };
		184DB4792C2C28B500234BEB /* ac3_parser.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2BB2C2C28B500234BEB /* ac3_parser.h */; };
		184DB47A2C2C28B500234BEB /* adts_parser.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2BC2C2C28B500234BEB /* adts_parser.h */; };
		184DB47B2C2C28B500234BEB /* avcodec.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2BD2C2C28B500234BEB /* avcodec.h */; };
		184DB47C2C2C28B500234BEB /* avdct.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2BE2C2C28B500234BEB /* avdct.h */; };
		184DB47D2C2C28B500234BEB /* avfft.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2BF2C2C28B500234BEB /* avfft.h */; };
		184DB47E2C2C28B500234BEB /* d3d11va.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2C02C2C28B500234BEB /* d3d11va.h */; };
		184DB47F2C2C28B500234BEB /* dirac.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2C12C2C28B500234BEB /* dirac.h */; };
		184DB4802C2C28B500234BEB /* dv_profile.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2C22C2C28B500234BEB /* dv_profile.h */; };
		184DB4812C2C28B500234BEB /* dxva2.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2C32C2C28B500234BEB /* dxva2.h */; };
		184DB4822C2C28B500234BEB /* jni.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2C42C2C28B500234BEB /* jni.h */; };
		184DB4832C2C28B500234BEB /* mediacodec.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2C52C2C28B500234BEB /* mediacodec.h */; };
		184DB4842C2C28B500234BEB /* qsv.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2C62C2C28B500234BEB /* qsv.h */; };
		184DB4852C2C28B500234BEB /* vaapi.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2C72C2C28B500234BEB /* vaapi.h */; };
		184DB4862C2C28B500234BEB /* vda.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2C82C2C28B500234BEB /* vda.h */; };
		184DB4872C2C28B500234BEB /* vdpau.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2C92C2C28B500234BEB /* vdpau.h */; };
		184DB4882C2C28B500234BEB /* version.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2CA2C2C28B500234BEB /* version.h */; };
		184DB4892C2C28B500234BEB /* videotoolbox.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2CB2C2C28B500234BEB /* videotoolbox.h */; };
		184DB48A2C2C28B500234BEB /* vorbis_parser.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2CC2C2C28B500234BEB /* vorbis_parser.h */; };
		184DB48B2C2C28B500234BEB /* xvmc.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2CD2C2C28B500234BEB /* xvmc.h */; };
		184DB48C2C2C28B500234BEB /* avfilter.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2CF2C2C28B500234BEB /* avfilter.h */; };
		184DB48D2C2C28B500234BEB /* avfiltergraph.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2D02C2C28B500234BEB /* avfiltergraph.h */; };
		184DB48E2C2C28B500234BEB /* buffersink.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2D12C2C28B500234BEB /* buffersink.h */; };
		184DB48F2C2C28B500234BEB /* buffersrc.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2D22C2C28B500234BEB /* buffersrc.h */; };
		184DB4902C2C28B500234BEB /* version.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2D32C2C28B500234BEB /* version.h */; };
		184DB4912C2C28B500234BEB /* avc.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2D52C2C28B500234BEB /* avc.h */; };
		184DB4922C2C28B500234BEB /* avformat.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2D62C2C28B500234BEB /* avformat.h */; };
		184DB4932C2C28B500234BEB /* avio_internal.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2D72C2C28B500234BEB /* avio_internal.h */; };
		184DB4942C2C28B500234BEB /* avio.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2D82C2C28B500234BEB /* avio.h */; };
		184DB4952C2C28B500234BEB /* flv.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2D92C2C28B500234BEB /* flv.h */; };
		184DB4962C2C28B500234BEB /* id3v2.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2DA2C2C28B500234BEB /* id3v2.h */; };
		184DB4972C2C28B500234BEB /* internal.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2DB2C2C28B500234BEB /* internal.h */; };
		184DB4982C2C28B500234BEB /* metadata.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2DC2C2C28B500234BEB /* metadata.h */; };
		184DB4992C2C28B500234BEB /* os_support.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2DD2C2C28B500234BEB /* os_support.h */; };
		184DB49A2C2C28B500234BEB /* url.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2DE2C2C28B500234BEB /* url.h */; };
		184DB49B2C2C28B500234BEB /* version.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2DF2C2C28B500234BEB /* version.h */; };
		184DB49C2C2C28B500234BEB /* avconfig.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2E12C2C28B500234BEB /* avconfig.h */; };
		184DB49D2C2C28B500234BEB /* ffversion.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2E22C2C28B500234BEB /* ffversion.h */; };
		184DB49E2C2C28B500234BEB /* avconfig.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2E42C2C28B500234BEB /* avconfig.h */; };
		184DB49F2C2C28B500234BEB /* ffversion.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2E52C2C28B500234BEB /* ffversion.h */; };
		184DB4A02C2C28B500234BEB /* avconfig.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2E72C2C28B500234BEB /* avconfig.h */; };
		184DB4A12C2C28B500234BEB /* ffversion.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2E82C2C28B500234BEB /* ffversion.h */; };
		184DB4A22C2C28B500234BEB /* avconfig.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2EA2C2C28B500234BEB /* avconfig.h */; };
		184DB4A32C2C28B500234BEB /* ffversion.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2EB2C2C28B500234BEB /* ffversion.h */; };
		184DB4A42C2C28B500234BEB /* adler32.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2ED2C2C28B500234BEB /* adler32.h */; };
		184DB4A52C2C28B500234BEB /* aes_ctr.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2EE2C2C28B500234BEB /* aes_ctr.h */; };
		184DB4A62C2C28B500234BEB /* aes.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2EF2C2C28B500234BEB /* aes.h */; };
		184DB4A72C2C28B500234BEB /* application.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2F02C2C28B500234BEB /* application.h */; };
		184DB4A82C2C28B500234BEB /* attributes.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2F12C2C28B500234BEB /* attributes.h */; };
		184DB4A92C2C28B500234BEB /* audio_fifo.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2F22C2C28B500234BEB /* audio_fifo.h */; };
		184DB4AA2C2C28B500234BEB /* avassert.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2F32C2C28B500234BEB /* avassert.h */; };
		184DB4AB2C2C28B500234BEB /* avconfig.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2F42C2C28B500234BEB /* avconfig.h */; };
		184DB4AC2C2C28B500234BEB /* avstring.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2F52C2C28B500234BEB /* avstring.h */; };
		184DB4AD2C2C28B500234BEB /* avutil.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2F62C2C28B500234BEB /* avutil.h */; };
		184DB4AE2C2C28B500234BEB /* base64.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2F72C2C28B500234BEB /* base64.h */; };
		184DB4AF2C2C28B500234BEB /* blowfish.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2F82C2C28B500234BEB /* blowfish.h */; };
		184DB4B02C2C28B500234BEB /* bprint.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2F92C2C28B500234BEB /* bprint.h */; };
		184DB4B12C2C28B500234BEB /* bswap.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2FA2C2C28B500234BEB /* bswap.h */; };
		184DB4B22C2C28B500234BEB /* buffer.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2FB2C2C28B500234BEB /* buffer.h */; };
		184DB4B32C2C28B500234BEB /* camellia.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2FC2C2C28B500234BEB /* camellia.h */; };
		184DB4B42C2C28B500234BEB /* cast5.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2FD2C2C28B500234BEB /* cast5.h */; };
		184DB4B52C2C28B500234BEB /* channel_layout.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2FE2C2C28B500234BEB /* channel_layout.h */; };
		184DB4B62C2C28B500234BEB /* common.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB2FF2C2C28B500234BEB /* common.h */; };
		184DB4B72C2C28B500234BEB /* cpu.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3002C2C28B500234BEB /* cpu.h */; };
		184DB4B82C2C28B500234BEB /* crc.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3012C2C28B500234BEB /* crc.h */; };
		184DB4B92C2C28B500234BEB /* des.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3022C2C28B500234BEB /* des.h */; };
		184DB4BA2C2C28B500234BEB /* dict.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3032C2C28B500234BEB /* dict.h */; };
		184DB4BB2C2C28B500234BEB /* display.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3042C2C28B500234BEB /* display.h */; };
		184DB4BC2C2C28B500234BEB /* dns_cache.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3052C2C28B500234BEB /* dns_cache.h */; };
		184DB4BD2C2C28B500234BEB /* downmix_info.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3062C2C28B500234BEB /* downmix_info.h */; };
		184DB4BE2C2C28B500234BEB /* encryption_info.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3072C2C28B500234BEB /* encryption_info.h */; };
		184DB4BF2C2C28B500234BEB /* error.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3082C2C28B500234BEB /* error.h */; };
		184DB4C02C2C28B500234BEB /* eval.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3092C2C28B500234BEB /* eval.h */; };
		184DB4C12C2C28B500234BEB /* ffversion.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB30A2C2C28B500234BEB /* ffversion.h */; };
		184DB4C22C2C28B500234BEB /* fifo.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB30B2C2C28B500234BEB /* fifo.h */; };
		184DB4C32C2C28B500234BEB /* file.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB30C2C2C28B500234BEB /* file.h */; };
		184DB4C42C2C28B500234BEB /* frame.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB30D2C2C28B500234BEB /* frame.h */; };
		184DB4C52C2C28B500234BEB /* hash.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB30E2C2C28B500234BEB /* hash.h */; };
		184DB4C62C2C28B500234BEB /* hmac.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB30F2C2C28B500234BEB /* hmac.h */; };
		184DB4C72C2C28B500234BEB /* hwcontext_cuda.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3102C2C28B500234BEB /* hwcontext_cuda.h */; };
		184DB4C82C2C28B500234BEB /* hwcontext_d3d11va.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3112C2C28B500234BEB /* hwcontext_d3d11va.h */; };
		184DB4C92C2C28B500234BEB /* hwcontext_drm.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3122C2C28B500234BEB /* hwcontext_drm.h */; };
		184DB4CA2C2C28B500234BEB /* hwcontext_dxva2.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3132C2C28B500234BEB /* hwcontext_dxva2.h */; };
		184DB4CB2C2C28B500234BEB /* hwcontext_mediacodec.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3142C2C28B500234BEB /* hwcontext_mediacodec.h */; };
		184DB4CC2C2C28B500234BEB /* hwcontext_qsv.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3152C2C28B500234BEB /* hwcontext_qsv.h */; };
		184DB4CD2C2C28B500234BEB /* hwcontext_vaapi.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3162C2C28B500234BEB /* hwcontext_vaapi.h */; };
		184DB4CE2C2C28B500234BEB /* hwcontext_vdpau.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3172C2C28B500234BEB /* hwcontext_vdpau.h */; };
		184DB4CF2C2C28B500234BEB /* hwcontext_videotoolbox.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3182C2C28B500234BEB /* hwcontext_videotoolbox.h */; };
		184DB4D02C2C28B500234BEB /* hwcontext.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3192C2C28B500234BEB /* hwcontext.h */; };
		184DB4D12C2C28B500234BEB /* imgutils.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB31A2C2C28B500234BEB /* imgutils.h */; };
		184DB4D22C2C28B500234BEB /* intfloat.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB31B2C2C28B500234BEB /* intfloat.h */; };
		184DB4D32C2C28B500234BEB /* intreadwrite.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB31C2C2C28B500234BEB /* intreadwrite.h */; };
		184DB4D42C2C28B500234BEB /* lfg.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB31D2C2C28B500234BEB /* lfg.h */; };
		184DB4D52C2C28B500234BEB /* log.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB31E2C2C28B500234BEB /* log.h */; };
		184DB4D62C2C28B500234BEB /* macros.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB31F2C2C28B500234BEB /* macros.h */; };
		184DB4D72C2C28B500234BEB /* mastering_display_metadata.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3202C2C28B500234BEB /* mastering_display_metadata.h */; };
		184DB4D82C2C28B500234BEB /* mathematics.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3212C2C28B500234BEB /* mathematics.h */; };
		184DB4D92C2C28B500234BEB /* md5.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3222C2C28B500234BEB /* md5.h */; };
		184DB4DA2C2C28B500234BEB /* mem.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3232C2C28B500234BEB /* mem.h */; };
		184DB4DB2C2C28B500234BEB /* motion_vector.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3242C2C28B500234BEB /* motion_vector.h */; };
		184DB4DC2C2C28B500234BEB /* murmur3.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3252C2C28B500234BEB /* murmur3.h */; };
		184DB4DD2C2C28B500234BEB /* opt.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3262C2C28B500234BEB /* opt.h */; };
		184DB4DE2C2C28B500234BEB /* parseutils.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3272C2C28B500234BEB /* parseutils.h */; };
		184DB4DF2C2C28B500234BEB /* pixdesc.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3282C2C28B500234BEB /* pixdesc.h */; };
		184DB4E02C2C28B500234BEB /* pixelutils.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3292C2C28B500234BEB /* pixelutils.h */; };
		184DB4E12C2C28B500234BEB /* pixfmt.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB32A2C2C28B500234BEB /* pixfmt.h */; };
		184DB4E22C2C28B500234BEB /* random_seed.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB32B2C2C28B500234BEB /* random_seed.h */; };
		184DB4E32C2C28B500234BEB /* rational.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB32C2C2C28B500234BEB /* rational.h */; };
		184DB4E42C2C28B500234BEB /* rc4.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB32D2C2C28B500234BEB /* rc4.h */; };
		184DB4E52C2C28B500234BEB /* replaygain.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB32E2C2C28B500234BEB /* replaygain.h */; };
		184DB4E62C2C28B500234BEB /* ripemd.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB32F2C2C28B500234BEB /* ripemd.h */; };
		184DB4E72C2C28B500234BEB /* samplefmt.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3302C2C28B500234BEB /* samplefmt.h */; };
		184DB4E82C2C28B500234BEB /* sha.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3312C2C28B500234BEB /* sha.h */; };
		184DB4E92C2C28B500234BEB /* sha512.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3322C2C28B500234BEB /* sha512.h */; };
		184DB4EA2C2C28B500234BEB /* spherical.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3332C2C28B500234BEB /* spherical.h */; };
		184DB4EB2C2C28B500234BEB /* stereo3d.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3342C2C28B500234BEB /* stereo3d.h */; };
		184DB4EC2C2C28B500234BEB /* tea.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3352C2C28B500234BEB /* tea.h */; };
		184DB4ED2C2C28B500234BEB /* thread.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3362C2C28B500234BEB /* thread.h */; };
		184DB4EE2C2C28B500234BEB /* threadmessage.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3372C2C28B500234BEB /* threadmessage.h */; };
		184DB4EF2C2C28B500234BEB /* time.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3382C2C28B500234BEB /* time.h */; };
		184DB4F02C2C28B500234BEB /* timecode.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3392C2C28B500234BEB /* timecode.h */; };
		184DB4F12C2C28B500234BEB /* timestamp.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB33A2C2C28B500234BEB /* timestamp.h */; };
		184DB4F22C2C28B500234BEB /* tree.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB33B2C2C28B500234BEB /* tree.h */; };
		184DB4F32C2C28B500234BEB /* twofish.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB33C2C2C28B500234BEB /* twofish.h */; };
		184DB4F42C2C28B500234BEB /* version.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB33D2C2C28B500234BEB /* version.h */; };
		184DB4F52C2C28B500234BEB /* xtea.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB33E2C2C28B500234BEB /* xtea.h */; };
		184DB4F62C2C28B500234BEB /* config.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3402C2C28B500234BEB /* config.h */; };
		184DB4F72C2C28B500234BEB /* config.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3422C2C28B500234BEB /* config.h */; };
		184DB4F82C2C28B500234BEB /* config.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3442C2C28B500234BEB /* config.h */; };
		184DB4F92C2C28B500234BEB /* config.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3462C2C28B500234BEB /* config.h */; };
		184DB4FA2C2C28B500234BEB /* config.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3482C2C28B500234BEB /* config.h */; };
		184DB4FB2C2C28B500234BEB /* swresample.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB34A2C2C28B500234BEB /* swresample.h */; };
		184DB4FC2C2C28B500234BEB /* version.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB34B2C2C28B500234BEB /* version.h */; };
		184DB4FD2C2C28B500234BEB /* swscale.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB34D2C2C28B500234BEB /* swscale.h */; };
		184DB4FE2C2C28B500234BEB /* version.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB34E2C2C28B500234BEB /* version.h */; };
		184DB4FF2C2C28B500234BEB /* aes.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3512C2C28B500234BEB /* aes.h */; };
		184DB5002C2C28B500234BEB /* asn1_mac.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3522C2C28B500234BEB /* asn1_mac.h */; };
		184DB5012C2C28B500234BEB /* asn1.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3532C2C28B500234BEB /* asn1.h */; };
		184DB5022C2C28B500234BEB /* asn1t.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3542C2C28B500234BEB /* asn1t.h */; };
		184DB5032C2C28B500234BEB /* bio.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3552C2C28B500234BEB /* bio.h */; };
		184DB5042C2C28B500234BEB /* blowfish.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3562C2C28B500234BEB /* blowfish.h */; };
		184DB5052C2C28B500234BEB /* bn.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3572C2C28B500234BEB /* bn.h */; };
		184DB5062C2C28B500234BEB /* buffer.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3582C2C28B500234BEB /* buffer.h */; };
		184DB5072C2C28B500234BEB /* camellia.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3592C2C28B500234BEB /* camellia.h */; };
		184DB5082C2C28B500234BEB /* cast.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB35A2C2C28B500234BEB /* cast.h */; };
		184DB5092C2C28B500234BEB /* cmac.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB35B2C2C28B500234BEB /* cmac.h */; };
		184DB50A2C2C28B500234BEB /* cms.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB35C2C2C28B500234BEB /* cms.h */; };
		184DB50B2C2C28B500234BEB /* comp.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB35D2C2C28B500234BEB /* comp.h */; };
		184DB50C2C2C28B500234BEB /* conf_api.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB35E2C2C28B500234BEB /* conf_api.h */; };
		184DB50D2C2C28B500234BEB /* conf.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB35F2C2C28B500234BEB /* conf.h */; };
		184DB50E2C2C28B500234BEB /* crypto.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3602C2C28B500234BEB /* crypto.h */; };
		184DB50F2C2C28B500234BEB /* des_old.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3612C2C28B500234BEB /* des_old.h */; };
		184DB5102C2C28B500234BEB /* des.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3622C2C28B500234BEB /* des.h */; };
		184DB5112C2C28B500234BEB /* dh.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3632C2C28B500234BEB /* dh.h */; };
		184DB5122C2C28B500234BEB /* dsa.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3642C2C28B500234BEB /* dsa.h */; };
		184DB5132C2C28B500234BEB /* dso.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3652C2C28B500234BEB /* dso.h */; };
		184DB5142C2C28B500234BEB /* dtls1.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3662C2C28B500234BEB /* dtls1.h */; };
		184DB5152C2C28B500234BEB /* e_os2.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3672C2C28B500234BEB /* e_os2.h */; };
		184DB5162C2C28B500234BEB /* ebcdic.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3682C2C28B500234BEB /* ebcdic.h */; };
		184DB5172C2C28B500234BEB /* ec.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3692C2C28B500234BEB /* ec.h */; };
		184DB5182C2C28B500234BEB /* ecdh.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB36A2C2C28B500234BEB /* ecdh.h */; };
		184DB5192C2C28B500234BEB /* ecdsa.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB36B2C2C28B500234BEB /* ecdsa.h */; };
		184DB51A2C2C28B500234BEB /* engine.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB36C2C2C28B500234BEB /* engine.h */; };
		184DB51B2C2C28B500234BEB /* err.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB36D2C2C28B500234BEB /* err.h */; };
		184DB51C2C2C28B500234BEB /* evp.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB36E2C2C28B500234BEB /* evp.h */; };
		184DB51D2C2C28B500234BEB /* hmac.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB36F2C2C28B500234BEB /* hmac.h */; };
		184DB51E2C2C28B500234BEB /* idea.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3702C2C28B500234BEB /* idea.h */; };
		184DB51F2C2C28B500234BEB /* krb5_asn.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3712C2C28B500234BEB /* krb5_asn.h */; };
		184DB5202C2C28B500234BEB /* kssl.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3722C2C28B500234BEB /* kssl.h */; };
		184DB5212C2C28B500234BEB /* lhash.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3732C2C28B500234BEB /* lhash.h */; };
		184DB5222C2C28B500234BEB /* md4.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3742C2C28B500234BEB /* md4.h */; };
		184DB5232C2C28B500234BEB /* md5.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3752C2C28B500234BEB /* md5.h */; };
		184DB5242C2C28B500234BEB /* mdc2.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3762C2C28B500234BEB /* mdc2.h */; };
		184DB5252C2C28B500234BEB /* modes.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3772C2C28B500234BEB /* modes.h */; };
		184DB5262C2C28B500234BEB /* obj_mac.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3782C2C28B500234BEB /* obj_mac.h */; };
		184DB5272C2C28B500234BEB /* objects.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3792C2C28B500234BEB /* objects.h */; };
		184DB5282C2C28B500234BEB /* ocsp.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB37A2C2C28B500234BEB /* ocsp.h */; };
		184DB5292C2C28B500234BEB /* opensslconf.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB37B2C2C28B500234BEB /* opensslconf.h */; };
		184DB52A2C2C28B500234BEB /* opensslv.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB37C2C2C28B500234BEB /* opensslv.h */; };
		184DB52B2C2C28B500234BEB /* ossl_typ.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB37D2C2C28B500234BEB /* ossl_typ.h */; };
		184DB52C2C2C28B500234BEB /* pem.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB37E2C2C28B500234BEB /* pem.h */; };
		184DB52D2C2C28B500234BEB /* pem2.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB37F2C2C28B500234BEB /* pem2.h */; };
		184DB52E2C2C28B500234BEB /* pkcs7.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3802C2C28B500234BEB /* pkcs7.h */; };
		184DB52F2C2C28B500234BEB /* pkcs12.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3812C2C28B500234BEB /* pkcs12.h */; };
		184DB5302C2C28B500234BEB /* pqueue.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3822C2C28B500234BEB /* pqueue.h */; };
		184DB5312C2C28B500234BEB /* rand.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3832C2C28B500234BEB /* rand.h */; };
		184DB5322C2C28B500234BEB /* rc2.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3842C2C28B500234BEB /* rc2.h */; };
		184DB5332C2C28B500234BEB /* rc4.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3852C2C28B500234BEB /* rc4.h */; };
		184DB5342C2C28B500234BEB /* ripemd.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3862C2C28B500234BEB /* ripemd.h */; };
		184DB5352C2C28B500234BEB /* rsa.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3872C2C28B500234BEB /* rsa.h */; };
		184DB5362C2C28B500234BEB /* safestack.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3882C2C28B500234BEB /* safestack.h */; };
		184DB5372C2C28B500234BEB /* seed.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3892C2C28B500234BEB /* seed.h */; };
		184DB5382C2C28B500234BEB /* sha.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB38A2C2C28B500234BEB /* sha.h */; };
		184DB5392C2C28B500234BEB /* srp.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB38B2C2C28B500234BEB /* srp.h */; };
		184DB53A2C2C28B500234BEB /* srtp.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB38C2C2C28B500234BEB /* srtp.h */; };
		184DB53B2C2C28B500234BEB /* ssl.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB38D2C2C28B500234BEB /* ssl.h */; };
		184DB53C2C2C28B500234BEB /* ssl2.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB38E2C2C28B500234BEB /* ssl2.h */; };
		184DB53D2C2C28B500234BEB /* ssl3.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB38F2C2C28B500234BEB /* ssl3.h */; };
		184DB53E2C2C28B500234BEB /* ssl23.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3902C2C28B500234BEB /* ssl23.h */; };
		184DB53F2C2C28B500234BEB /* stack.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3912C2C28B500234BEB /* stack.h */; };
		184DB5402C2C28B500234BEB /* symhacks.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3922C2C28B500234BEB /* symhacks.h */; };
		184DB5412C2C28B500234BEB /* tls1.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3932C2C28B500234BEB /* tls1.h */; };
		184DB5422C2C28B500234BEB /* ts.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3942C2C28B500234BEB /* ts.h */; };
		184DB5432C2C28B500234BEB /* txt_db.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3952C2C28B500234BEB /* txt_db.h */; };
		184DB5442C2C28B500234BEB /* ui_compat.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3962C2C28B500234BEB /* ui_compat.h */; };
		184DB5452C2C28B500234BEB /* ui.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3972C2C28B500234BEB /* ui.h */; };
		184DB5462C2C28B500234BEB /* whrlpool.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3982C2C28B500234BEB /* whrlpool.h */; };
		184DB5472C2C28B500234BEB /* x509_vfy.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3992C2C28B500234BEB /* x509_vfy.h */; };
		184DB5482C2C28B500234BEB /* x509.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB39A2C2C28B500234BEB /* x509.h */; };
		184DB5492C2C28B500234BEB /* x509v3.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB39B2C2C28B500234BEB /* x509v3.h */; };
		184DB54A2C2C28B500234BEB /* libavcodec.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 184DB39E2C2C28B500234BEB /* libavcodec.a */; };
		184DB54B2C2C28B500234BEB /* libavfilter.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 184DB39F2C2C28B500234BEB /* libavfilter.a */; };
		184DB54C2C2C28B500234BEB /* libavformat.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 184DB3A02C2C28B500234BEB /* libavformat.a */; };
		184DB54D2C2C28B500234BEB /* libavutil.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 184DB3A12C2C28B500234BEB /* libavutil.a */; };
		184DB54E2C2C28B500234BEB /* libswresample.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 184DB3A22C2C28B500234BEB /* libswresample.a */; };
		184DB54F2C2C28B500234BEB /* libswscale.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 184DB3A32C2C28B500234BEB /* libswscale.a */; };
		184DB5502C2C28B500234BEB /* libcrypto.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 184DB3A52C2C28B500234BEB /* libcrypto.a */; };
		184DB5512C2C28B500234BEB /* libssl.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 184DB3A62C2C28B500234BEB /* libssl.a */; };
		184DB5522C2C28B500234BEB /* libiot_video_p2p.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 184DB3AA2C2C28B500234BEB /* libiot_video_p2p.a */; };
		184DB5532C2C28B500234BEB /* liblibevent.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 184DB3AB2C2C28B500234BEB /* liblibevent.a */; };
		184DB5542C2C28B500234BEB /* libmbedtls1.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 184DB3AC2C2C28B500234BEB /* libmbedtls1.a */; };
		184DB5562C2C28B600234BEB /* IVVAS.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3AF2C2C28B500234BEB /* IVVAS.h */; settings = {ATTRIBUTES = (Private, ); }; };
		184DB5572C2C28B600234BEB /* 替换SDK需替换所有文件 in Resources */ = {isa = PBXBuildFile; fileRef = 184DB3B22C2C28B500234BEB /* 替换SDK需替换所有文件 */; };
		184DB5592C2C28B600234BEB /* NIotManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 184DB3B52C2C28B500234BEB /* NIotManager.swift */; };
		184DB55C2C2C28B600234BEB /* NIotNetworking.swift in Sources */ = {isa = PBXBuildFile; fileRef = 184DB3B82C2C28B500234BEB /* NIotNetworking.swift */; };
		184DB55F2C2C28B600234BEB /* Foundation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 184DB3BC2C2C28B500234BEB /* Foundation.swift */; };
		184DB5602C2C28B600234BEB /* Notifications+Names.swift in Sources */ = {isa = PBXBuildFile; fileRef = 184DB3BD2C2C28B500234BEB /* Notifications+Names.swift */; };
		184DB5612C2C28B600234BEB /* String+AES.swift in Sources */ = {isa = PBXBuildFile; fileRef = 184DB3BE2C2C28B500234BEB /* String+AES.swift */; };
		184DB5622C2C28B600234BEB /* String+Extension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 184DB3BF2C2C28B500234BEB /* String+Extension.swift */; };
		184DB5632C2C28B600234BEB /* DistributionNetService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 184DB3C12C2C28B500234BEB /* DistributionNetService.swift */; };
		184DB5642C2C28B600234BEB /* PropertyModel_v1.swift in Sources */ = {isa = PBXBuildFile; fileRef = 184DB3C32C2C28B500234BEB /* PropertyModel_v1.swift */; };
		184DB5652C2C28B600234BEB /* PropertyModel_v2.swift in Sources */ = {isa = PBXBuildFile; fileRef = 184DB3C42C2C28B500234BEB /* PropertyModel_v2.swift */; };
		184DB5662C2C28B600234BEB /* PropertyModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 184DB3C52C2C28B500234BEB /* PropertyModel.swift */; };
		184DB5672C2C28B600234BEB /* PropertyModelPath.swift in Sources */ = {isa = PBXBuildFile; fileRef = 184DB3C62C2C28B500234BEB /* PropertyModelPath.swift */; };
		184DB5682C2C28B600234BEB /* IoTMonitorPlayer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 184DB3C82C2C28B500234BEB /* IoTMonitorPlayer.swift */; };
		184DB5692C2C28B600234BEB /* IVMonitorPlayer+IoT.swift in Sources */ = {isa = PBXBuildFile; fileRef = 184DB3C92C2C28B500234BEB /* IVMonitorPlayer+IoT.swift */; };
		184DB56A2C2C28B600234BEB /* IoTFileDownloader.swift in Sources */ = {isa = PBXBuildFile; fileRef = 184DB3CB2C2C28B500234BEB /* IoTFileDownloader.swift */; };
		184DB56B2C2C28B600234BEB /* IoTPlaybackPlayer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 184DB3CC2C2C28B500234BEB /* IoTPlaybackPlayer.swift */; };
		184DB56C2C2C28B600234BEB /* IVPlaybackPlayer_v2.swift in Sources */ = {isa = PBXBuildFile; fileRef = 184DB3CD2C2C28B500234BEB /* IVPlaybackPlayer_v2.swift */; };
		184DB56D2C2C28B600234BEB /* IVPlaybackPlayer+IoT.swift in Sources */ = {isa = PBXBuildFile; fileRef = 184DB3CE2C2C28B500234BEB /* IVPlaybackPlayer+IoT.swift */; };
		184DB56E2C2C28B600234BEB /* IoTExploreOrVideoDeviceModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 184DB3D02C2C28B500234BEB /* IoTExploreOrVideoDeviceModel.swift */; };
		184DB56F2C2C28B600234BEB /* IoTConnection.swift in Sources */ = {isa = PBXBuildFile; fileRef = 184DB3D22C2C28B500234BEB /* IoTConnection.swift */; };
		184DB5702C2C28B600234BEB /* IoTPlayer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 184DB3D32C2C28B500234BEB /* IoTPlayer.swift */; };
		184DB5712C2C28B600234BEB /* IVPlayer_v2.swift in Sources */ = {isa = PBXBuildFile; fileRef = 184DB3D42C2C28B500234BEB /* IVPlayer_v2.swift */; };
		184DB5722C2C28B600234BEB /* IVPlayer+IoT.swift in Sources */ = {isa = PBXBuildFile; fileRef = 184DB3D52C2C28B500234BEB /* IVPlayer+IoT.swift */; };
		184DB5732C2C28B600234BEB /* IoTPlaybackPage.swift in Sources */ = {isa = PBXBuildFile; fileRef = 184DB3D72C2C28B500234BEB /* IoTPlaybackPage.swift */; };
		184DB5742C2C28B600234BEB /* IoTPlaybackService_v1.swift in Sources */ = {isa = PBXBuildFile; fileRef = 184DB3D82C2C28B500234BEB /* IoTPlaybackService_v1.swift */; };
		184DB5752C2C28B600234BEB /* IoTPlaybackService_v2.swift in Sources */ = {isa = PBXBuildFile; fileRef = 184DB3D92C2C28B500234BEB /* IoTPlaybackService_v2.swift */; };
		184DB5762C2C28B600234BEB /* IoTPlaybackService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 184DB3DA2C2C28B500234BEB /* IoTPlaybackService.swift */; };
		184DB5772C2C28B600234BEB /* IoTService_v1.swift in Sources */ = {isa = PBXBuildFile; fileRef = 184DB3DB2C2C28B500234BEB /* IoTService_v1.swift */; };
		184DB5782C2C28B600234BEB /* IoTService_v2.swift in Sources */ = {isa = PBXBuildFile; fileRef = 184DB3DC2C2C28B500234BEB /* IoTService_v2.swift */; };
		184DB5792C2C28B600234BEB /* IoTService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 184DB3DD2C2C28B500234BEB /* IoTService.swift */; };
		184DB57A2C2C28B600234BEB /* NIotSignalMessager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 184DB3DE2C2C28B500234BEB /* NIotSignalMessager.swift */; };
		184DB57B2C2C28B600234BEB /* NIotPlaybackItem.swift in Sources */ = {isa = PBXBuildFile; fileRef = 184DB3E02C2C28B500234BEB /* NIotPlaybackItem.swift */; };
		184DB57C2C2C28B600234BEB /* NVDeviceModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 184DB3E12C2C28B500234BEB /* NVDeviceModel.swift */; };
		184DB57D2C2C28B600234BEB /* EWKeychain.swift in Sources */ = {isa = PBXBuildFile; fileRef = 184DB3E32C2C28B500234BEB /* EWKeychain.swift */; };
		184DB57E2C2C28B600234BEB /* NVAPI.swift in Sources */ = {isa = PBXBuildFile; fileRef = 184DB3E42C2C28B500234BEB /* NVAPI.swift */; };
		184DB57F2C2C28B600234BEB /* NVM3U8NetworkManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 184DB3E52C2C28B500234BEB /* NVM3U8NetworkManager.swift */; };
		184DB5802C2C28B600234BEB /* NVNetworkManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 184DB3E62C2C28B500234BEB /* NVNetworkManager.swift */; };
		184DB5812C2C28B600234BEB /* Response.swift in Sources */ = {isa = PBXBuildFile; fileRef = 184DB3E72C2C28B500234BEB /* Response.swift */; };
		184DB5822C2C28B600234BEB /* NVEnvironment.swift in Sources */ = {isa = PBXBuildFile; fileRef = 184DB3E92C2C28B500234BEB /* NVEnvironment.swift */; };
		184DB5832C2C28B600234BEB /* NVJson.swift in Sources */ = {isa = PBXBuildFile; fileRef = 184DB3EA2C2C28B500234BEB /* NVJson.swift */; };
		184DB5842C2C28B600234BEB /* NVKeychain.swift in Sources */ = {isa = PBXBuildFile; fileRef = 184DB3EB2C2C28B500234BEB /* NVKeychain.swift */; };
		184DB5852C2C28B600234BEB /* NVLog.swift in Sources */ = {isa = PBXBuildFile; fileRef = 184DB3EC2C2C28B500234BEB /* NVLog.swift */; };
		184DB5862C2C28B600234BEB /* NVTencentModelCommand.swift in Sources */ = {isa = PBXBuildFile; fileRef = 184DB3ED2C2C28B500234BEB /* NVTencentModelCommand.swift */; };
		184DB5872C2C28B600234BEB /* NVUtils.swift in Sources */ = {isa = PBXBuildFile; fileRef = 184DB3EE2C2C28B500234BEB /* NVUtils.swift */; };
		184DB5882C2C28B600234BEB /* 打包注意.md in Resources */ = {isa = PBXBuildFile; fileRef = 184DB3F12C2C28B500234BEB /* 打包注意.md */; };
		184DB5892C2C28B600234BEB /* BluetoothCentralManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3F22C2C28B500234BEB /* BluetoothCentralManager.h */; };
		184DB58A2C2C28B600234BEB /* CMPageContentView.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3F32C2C28B500234BEB /* CMPageContentView.h */; };
		184DB58B2C2C28B600234BEB /* CMPageTitleConfig.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3F42C2C28B500234BEB /* CMPageTitleConfig.h */; };
		184DB58C2C2C28B600234BEB /* CMPageTitleContentView.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3F52C2C28B500234BEB /* CMPageTitleContentView.h */; };
		184DB58D2C2C28B600234BEB /* CMPageTitleView.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3F62C2C28B500234BEB /* CMPageTitleView.h */; };
		184DB58E2C2C28B600234BEB /* CMPageTitleViewMacro.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3F72C2C28B500234BEB /* CMPageTitleViewMacro.h */; };
		184DB58F2C2C28B600234BEB /* ESP_ByteUtil.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3F82C2C28B500234BEB /* ESP_ByteUtil.h */; };
		184DB5902C2C28B600234BEB /* ESP_CRC8.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3F92C2C28B500234BEB /* ESP_CRC8.h */; };
		184DB5912C2C28B600234BEB /* ESP_NetUtil.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3FA2C2C28B500234BEB /* ESP_NetUtil.h */; };
		184DB5922C2C28B600234BEB /* ESP_WifiUtil.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3FB2C2C28B500234BEB /* ESP_WifiUtil.h */; };
		184DB5932C2C28B600234BEB /* ESPAES.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3FC2C2C28B500234BEB /* ESPAES.h */; };
		184DB5942C2C28B600234BEB /* ESPDataCode.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3FD2C2C28B500234BEB /* ESPDataCode.h */; };
		184DB5952C2C28B600234BEB /* ESPDatumCode.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3FE2C2C28B500234BEB /* ESPDatumCode.h */; };
		184DB5962C2C28B600234BEB /* ESPGuideCode.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB3FF2C2C28B500234BEB /* ESPGuideCode.h */; };
		184DB5972C2C28B600234BEB /* ESPTools.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB4002C2C28B500234BEB /* ESPTools.h */; };
		184DB5982C2C28B600234BEB /* ESPTouchDelegate.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB4012C2C28B500234BEB /* ESPTouchDelegate.h */; };
		184DB5992C2C28B600234BEB /* ESPTouchGenerator.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB4022C2C28B500234BEB /* ESPTouchGenerator.h */; };
		184DB59A2C2C28B600234BEB /* ESPTouchResult.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB4032C2C28B500234BEB /* ESPTouchResult.h */; };
		184DB59B2C2C28B600234BEB /* ESPTouchTask.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB4042C2C28B500234BEB /* ESPTouchTask.h */; };
		184DB59C2C2C28B600234BEB /* ESPTouchTaskParameter.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB4052C2C28B500234BEB /* ESPTouchTaskParameter.h */; };
		184DB59D2C2C28B600234BEB /* ESPUDPSocketClient.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB4062C2C28B500234BEB /* ESPUDPSocketClient.h */; };
		184DB59E2C2C28B600234BEB /* ESPUDPSocketServer.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB4072C2C28B500234BEB /* ESPUDPSocketServer.h */; };
		184DB59F2C2C28B600234BEB /* ESPVersionMacro.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB4082C2C28B500234BEB /* ESPVersionMacro.h */; };
		184DB5A02C2C28B600234BEB /* HXYNotice.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB4092C2C28B500234BEB /* HXYNotice.h */; };
		184DB5A12C2C28B600234BEB /* MBProgressHUD+XDP.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB40A2C2C28B500234BEB /* MBProgressHUD+XDP.h */; };
		184DB5A22C2C28B600234BEB /* NSObject+additions.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB40B2C2C28B500234BEB /* NSObject+additions.h */; };
		184DB5A32C2C28B600234BEB /* NSString+Extension.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB40C2C2C28B500234BEB /* NSString+Extension.h */; };
		184DB5A42C2C28B600234BEB /* route.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB40D2C2C28B500234BEB /* route.h */; };
		184DB5A52C2C28B600234BEB /* TCSocket.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB40E2C2C28B500234BEB /* TCSocket.h */; };
		184DB5A62C2C28B600234BEB /* TIoTCodeAddress.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB40F2C2C28B500234BEB /* TIoTCodeAddress.h */; };
		184DB5A72C2C28B600234BEB /* TIoTCoreAccountSet.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB4102C2C28B500234BEB /* TIoTCoreAccountSet.h */; };
		184DB5A82C2C28B600234BEB /* TIoTCoreAddDevice.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB4112C2C28B500234BEB /* TIoTCoreAddDevice.h */; };
		184DB5A92C2C28B600234BEB /* TIoTCoreAPISets.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB4122C2C28B500234BEB /* TIoTCoreAPISets.h */; };
		184DB5AA2C2C28B600234BEB /* TIoTCoreAppEnvironment.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB4132C2C28B500234BEB /* TIoTCoreAppEnvironment.h */; };
		184DB5AB2C2C28B600234BEB /* TIoTCoreDeviceCenter.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB4142C2C28B500234BEB /* TIoTCoreDeviceCenter.h */; };
		184DB5AC2C2C28B600234BEB /* TIoTCoreDeviceSet.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB4152C2C28B500234BEB /* TIoTCoreDeviceSet.h */; };
		184DB5AD2C2C28B600234BEB /* TIoTCoreFamilySet.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB4162C2C28B500234BEB /* TIoTCoreFamilySet.h */; };
		184DB5AE2C2C28B600234BEB /* TIoTCoreFoundation.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB4172C2C28B500234BEB /* TIoTCoreFoundation.h */; };
		184DB5AF2C2C28B600234BEB /* TIoTCoreHudLoadingView.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB4182C2C28B500234BEB /* TIoTCoreHudLoadingView.h */; };
		184DB5B02C2C28B600234BEB /* TIoTCoreLogReport.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB4192C2C28B500234BEB /* TIoTCoreLogReport.h */; };
		184DB5B12C2C28B600234BEB /* TIoTCoreMessageSet.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB41A2C2C28B500234BEB /* TIoTCoreMessageSet.h */; };
		184DB5B22C2C28B600234BEB /* TIoTCoreObject.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB41B2C2C28B500234BEB /* TIoTCoreObject.h */; };
		184DB5B32C2C28B600234BEB /* TIoTCoreParts.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB41C2C2C28B500234BEB /* TIoTCoreParts.h */; };
		184DB5B42C2C28B600234BEB /* TIoTCoreQMacros.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB41D2C2C28B500234BEB /* TIoTCoreQMacros.h */; };
		184DB5B52C2C28B600234BEB /* TIoTCoreRequestAction.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB41E2C2C28B500234BEB /* TIoTCoreRequestAction.h */; };
		184DB5B62C2C28B600234BEB /* TIoTCoreRequestBuilder.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB41F2C2C28B500234BEB /* TIoTCoreRequestBuilder.h */; };
		184DB5B72C2C28B600234BEB /* TIoTCoreRequestClient.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB4202C2C28B500234BEB /* TIoTCoreRequestClient.h */; };
		184DB5B82C2C28B600234BEB /* TIoTCoreRequestObj.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB4212C2C28B500234BEB /* TIoTCoreRequestObj.h */; };
		184DB5B92C2C28B600234BEB /* TIoTCoreRequestObject.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB4222C2C28B500234BEB /* TIoTCoreRequestObject.h */; };
		184DB5BA2C2C28B600234BEB /* TIoTCoreServices.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB4232C2C28B500234BEB /* TIoTCoreServices.h */; };
		184DB5BB2C2C28B600234BEB /* TIoTCoreSocketCover.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB4242C2C28B500234BEB /* TIoTCoreSocketCover.h */; };
		184DB5BC2C2C28B600234BEB /* TIoTCoreSocketManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB4252C2C28B500234BEB /* TIoTCoreSocketManager.h */; };
		184DB5BD2C2C28B600234BEB /* TIoTCoreUserManage.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB4262C2C28B500234BEB /* TIoTCoreUserManage.h */; };
		184DB5BE2C2C28B600234BEB /* TIoTCoreUtil.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB4272C2C28B500234BEB /* TIoTCoreUtil.h */; };
		184DB5BF2C2C28B600234BEB /* TIoTCoreWebSocket.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB4282C2C28B500234BEB /* TIoTCoreWebSocket.h */; };
		184DB5C02C2C28B600234BEB /* TIoTCoreWMacros.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB4292C2C28B500234BEB /* TIoTCoreWMacros.h */; };
		184DB5C12C2C28B600234BEB /* TIoTDataTracking.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB42A2C2C28B500234BEB /* TIoTDataTracking.h */; };
		184DB5C22C2C28B600234BEB /* TIoTGetgateway.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB42B2C2C28B500234BEB /* TIoTGetgateway.h */; };
		184DB5C32C2C28B600234BEB /* TIoTLinkKit-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB42C2C2C28B500234BEB /* TIoTLinkKit-umbrella.h */; };
		184DB5C42C2C28B600234BEB /* TIoTPrintLogFileManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB42D2C2C28B500234BEB /* TIoTPrintLogFileManager.h */; };
		184DB5C52C2C28B600234BEB /* TIoTPrintLogFormatter.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB42E2C2C28B500234BEB /* TIoTPrintLogFormatter.h */; };
		184DB5C62C2C28B600234BEB /* TIoTPrintLogManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB42F2C2C28B500234BEB /* TIoTPrintLogManager.h */; };
		184DB5C72C2C28B600234BEB /* TIoTRouter.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB4302C2C28B500234BEB /* TIoTRouter.h */; };
		184DB5C82C2C28B600234BEB /* TIotSoftApUdpSocketUtil.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB4312C2C28B500234BEB /* TIotSoftApUdpSocketUtil.h */; };
		184DB5C92C2C28B600234BEB /* TIOTTRTCModel.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB4322C2C28B500234BEB /* TIOTTRTCModel.h */; };
		184DB5CA2C2C28B600234BEB /* TIoTTRTCSessionManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB4332C2C28B500234BEB /* TIoTTRTCSessionManager.h */; };
		184DB5CB2C2C28B600234BEB /* TIoTVideoDistributionNetModel.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB4342C2C28B500234BEB /* TIoTVideoDistributionNetModel.h */; };
		184DB5CC2C2C28B600234BEB /* TRTCCalling.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB4352C2C28B500234BEB /* TRTCCalling.h */; };
		184DB5CD2C2C28B600234BEB /* TRTCCalling+Signal.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB4362C2C28B500234BEB /* TRTCCalling+Signal.h */; };
		184DB5CE2C2C28B600234BEB /* TRTCCallingDelegate.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB4372C2C28B500234BEB /* TRTCCallingDelegate.h */; };
		184DB5CF2C2C28B600234BEB /* TRTCCallingHeader.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB4382C2C28B500234BEB /* TRTCCallingHeader.h */; };
		184DB5D02C2C28B600234BEB /* TRTCCallingModel.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB4392C2C28B500234BEB /* TRTCCallingModel.h */; };
		184DB5D12C2C28B600234BEB /* TRTCCallingUtils.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB43A2C2C28B500234BEB /* TRTCCallingUtils.h */; };
		184DB5D22C2C28B600234BEB /* UIColor+Color.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB43B2C2C28B500234BEB /* UIColor+Color.h */; };
		184DB5D32C2C28B600234BEB /* UIDevice+Until.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB43C2C2C28B500234BEB /* UIDevice+Until.h */; };
		184DB5D42C2C28B600234BEB /* UIFont+TIoTFont.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB43D2C2C28B500234BEB /* UIFont+TIoTFont.h */; };
		184DB5D52C2C28B600234BEB /* UIView+CMCommon.h in Headers */ = {isa = PBXBuildFile; fileRef = 184DB43E2C2C28B500234BEB /* UIView+CMCommon.h */; };
		184DB5D62C2C28B600234BEB /* TIoTLinkKit in Frameworks */ = {isa = PBXBuildFile; fileRef = 184DB4402C2C28B500234BEB /* TIoTLinkKit */; };
		184DB5EF2C2D604500234BEB /* NIotMonitorPlayer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 184DB5EB2C2D604500234BEB /* NIotMonitorPlayer.swift */; };
		184DB5F02C2D604500234BEB /* NIotConnection.swift in Sources */ = {isa = PBXBuildFile; fileRef = 184DB5EC2C2D604500234BEB /* NIotConnection.swift */; };
		184DB5F12C2D604500234BEB /* NIotPlayer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 184DB5ED2C2D604500234BEB /* NIotPlayer.swift */; };
		184DB5F22C2D604500234BEB /* NIotPlaybackPlayer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 184DB5EE2C2D604500234BEB /* NIotPlaybackPlayer.swift */; };
		187BA4D72C2BEC5D00AC4A65 /* NiViewIoT.docc in Sources */ = {isa = PBXBuildFile; fileRef = 187BA4D62C2BEC5D00AC4A65 /* NiViewIoT.docc */; };
		187BA4D82C2BEC5D00AC4A65 /* NiViewIoT.h in Headers */ = {isa = PBXBuildFile; fileRef = 187BA4D52C2BEC5D00AC4A65 /* NiViewIoT.h */; settings = {ATTRIBUTES = (Public, ); }; };
		18AB14482D967F9500B98B31 /* XP2PDelegateForwarder.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18AB14472D967F9500B98B31 /* XP2PDelegateForwarder.swift */; };
		18ECA7052C89B4AC00319EE9 /* IoTVideo in Frameworks */ = {isa = PBXBuildFile; fileRef = 18ECA7042C89B4AC00319EE9 /* IoTVideo */; };
		18ECA7072C89B4F600319EE9 /* IVVAS in Frameworks */ = {isa = PBXBuildFile; fileRef = 18ECA7062C89B4F600319EE9 /* IVVAS */; };
		18FA8D552C5CD6D100DA5EC5 /* NIotOTAManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18FA8D532C5CD6D000DA5EC5 /* NIotOTAManager.swift */; };
		18FA8D562C5CD6D100DA5EC5 /* NIotConnectionPreparer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18FA8D542C5CD6D000DA5EC5 /* NIotConnectionPreparer.swift */; };
		18FC86A32C2EC9E100AA1F14 /* MQTTService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18FC86A22C2EC9E100AA1F14 /* MQTTService.swift */; };
		18FC86A52C3680D900AA1F14 /* NIotFileDownloader.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18FC86A42C3680D900AA1F14 /* NIotFileDownloader.swift */; };
		1E6A98DCB146327A81879F81 /* Pods_NiViewIoT.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 469254C27C7C6F9C50C95899 /* Pods_NiViewIoT.framework */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		0DE67FDB82E207A0991E25C5 /* Pods-NiViewIoT.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-NiViewIoT.debug.xcconfig"; path = "Target Support Files/Pods-NiViewIoT/Pods-NiViewIoT.debug.xcconfig"; sourceTree = "<group>"; };
		184DB2852C2C28B500234BEB /* IoTVideo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IoTVideo.h; sourceTree = "<group>"; };
		184DB2862C2C28B500234BEB /* IVAudioCapturable.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IVAudioCapturable.h; sourceTree = "<group>"; };
		184DB2882C2C28B500234BEB /* IVAudioDecodable.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IVAudioDecodable.h; sourceTree = "<group>"; };
		184DB2892C2C28B500234BEB /* IVAudioDecoder.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IVAudioDecoder.h; sourceTree = "<group>"; };
		184DB28A2C2C28B500234BEB /* IVAudioEncodable.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IVAudioEncodable.h; sourceTree = "<group>"; };
		184DB28B2C2C28B500234BEB /* IVAudioEncoder.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IVAudioEncoder.h; sourceTree = "<group>"; };
		184DB28C2C2C28B500234BEB /* IVAudioRenderable.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IVAudioRenderable.h; sourceTree = "<group>"; };
		184DB28D2C2C28B500234BEB /* IVAudioUnit.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IVAudioUnit.h; sourceTree = "<group>"; };
		184DB28E2C2C28B500234BEB /* IVAVDefine.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IVAVDefine.h; sourceTree = "<group>"; };
		184DB28F2C2C28B500234BEB /* IVAVRecordable.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IVAVRecordable.h; sourceTree = "<group>"; };
		184DB2902C2C28B500234BEB /* IVAVRecorder.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IVAVRecorder.h; sourceTree = "<group>"; };
		184DB2912C2C28B500234BEB /* IVAVUtils.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IVAVUtils.h; sourceTree = "<group>"; };
		184DB2952C2C28B500234BEB /* IVConnection.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IVConnection.h; sourceTree = "<group>"; };
		184DB2962C2C28B500234BEB /* IVConstant.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IVConstant.h; sourceTree = "<group>"; };
		184DB2982C2C28B500234BEB /* IVDeviceMgr.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IVDeviceMgr.h; sourceTree = "<group>"; };
		184DB2992C2C28B500234BEB /* IVError.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IVError.h; sourceTree = "<group>"; };
		184DB29A2C2C28B500234BEB /* IVFileDownloader.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IVFileDownloader.h; sourceTree = "<group>"; };
		184DB29B2C2C28B500234BEB /* IVLanNetConfig.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IVLanNetConfig.h; sourceTree = "<group>"; };
		184DB29C2C2C28B500234BEB /* IVLivePlayer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IVLivePlayer.h; sourceTree = "<group>"; };
		184DB29D2C2C28B500234BEB /* IVLog.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IVLog.h; sourceTree = "<group>"; };
		184DB29F2C2C28B500234BEB /* IVMacros.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IVMacros.h; sourceTree = "<group>"; };
		184DB2A02C2C28B500234BEB /* IVMessageMgr.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IVMessageMgr.h; sourceTree = "<group>"; };
		184DB2A12C2C28B500234BEB /* IVMonitorPlayer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IVMonitorPlayer.h; sourceTree = "<group>"; };
		184DB2A32C2C28B500234BEB /* IVNetConfig.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IVNetConfig.h; sourceTree = "<group>"; };
		184DB2A42C2C28B500234BEB /* IVNetwork_p2p.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IVNetwork_p2p.h; sourceTree = "<group>"; };
		184DB2A52C2C28B500234BEB /* IVNetworkHelper.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IVNetworkHelper.h; sourceTree = "<group>"; };
		184DB2A62C2C28B500234BEB /* IVNetworkSign.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IVNetworkSign.h; sourceTree = "<group>"; };
		184DB2A72C2C28B500234BEB /* IVPlaybackPlayer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IVPlaybackPlayer.h; sourceTree = "<group>"; };
		184DB2A82C2C28B500234BEB /* IVPlayer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IVPlayer.h; sourceTree = "<group>"; };
		184DB2A92C2C28B500234BEB /* IVQRCodeHelper.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IVQRCodeHelper.h; sourceTree = "<group>"; };
		184DB2AA2C2C28B500234BEB /* IVQRCodeNetConfig.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IVQRCodeNetConfig.h; sourceTree = "<group>"; };
		184DB2AB2C2C28B500234BEB /* IVReachability.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IVReachability.h; sourceTree = "<group>"; };
		184DB2AD2C2C28B500234BEB /* IVTimer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IVTimer.h; sourceTree = "<group>"; };
		184DB2AE2C2C28B500234BEB /* IVTransmission.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IVTransmission.h; sourceTree = "<group>"; };
		184DB2B02C2C28B500234BEB /* IVUtils.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IVUtils.h; sourceTree = "<group>"; };
		184DB2B12C2C28B500234BEB /* IVVideoCapturable.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IVVideoCapturable.h; sourceTree = "<group>"; };
		184DB2B22C2C28B500234BEB /* IVVideoCapture.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IVVideoCapture.h; sourceTree = "<group>"; };
		184DB2B32C2C28B500234BEB /* IVVideoDecodable.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IVVideoDecodable.h; sourceTree = "<group>"; };
		184DB2B42C2C28B500234BEB /* IVVideoDecoder.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IVVideoDecoder.h; sourceTree = "<group>"; };
		184DB2B52C2C28B500234BEB /* IVVideoEncodable.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IVVideoEncodable.h; sourceTree = "<group>"; };
		184DB2B62C2C28B500234BEB /* IVVideoEncoder.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IVVideoEncoder.h; sourceTree = "<group>"; };
		184DB2B82C2C28B500234BEB /* IVVideoRender.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IVVideoRender.h; sourceTree = "<group>"; };
		184DB2B92C2C28B500234BEB /* IVVideoRenderable.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IVVideoRenderable.h; sourceTree = "<group>"; };
		184DB2BB2C2C28B500234BEB /* ac3_parser.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ac3_parser.h; sourceTree = "<group>"; };
		184DB2BC2C2C28B500234BEB /* adts_parser.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = adts_parser.h; sourceTree = "<group>"; };
		184DB2BD2C2C28B500234BEB /* avcodec.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = avcodec.h; sourceTree = "<group>"; };
		184DB2BE2C2C28B500234BEB /* avdct.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = avdct.h; sourceTree = "<group>"; };
		184DB2BF2C2C28B500234BEB /* avfft.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = avfft.h; sourceTree = "<group>"; };
		184DB2C02C2C28B500234BEB /* d3d11va.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = d3d11va.h; sourceTree = "<group>"; };
		184DB2C12C2C28B500234BEB /* dirac.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = dirac.h; sourceTree = "<group>"; };
		184DB2C22C2C28B500234BEB /* dv_profile.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = dv_profile.h; sourceTree = "<group>"; };
		184DB2C32C2C28B500234BEB /* dxva2.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = dxva2.h; sourceTree = "<group>"; };
		184DB2C42C2C28B500234BEB /* jni.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = jni.h; sourceTree = "<group>"; };
		184DB2C52C2C28B500234BEB /* mediacodec.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = mediacodec.h; sourceTree = "<group>"; };
		184DB2C62C2C28B500234BEB /* qsv.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = qsv.h; sourceTree = "<group>"; };
		184DB2C72C2C28B500234BEB /* vaapi.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = vaapi.h; sourceTree = "<group>"; };
		184DB2C82C2C28B500234BEB /* vda.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = vda.h; sourceTree = "<group>"; };
		184DB2C92C2C28B500234BEB /* vdpau.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = vdpau.h; sourceTree = "<group>"; };
		184DB2CA2C2C28B500234BEB /* version.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = version.h; sourceTree = "<group>"; };
		184DB2CB2C2C28B500234BEB /* videotoolbox.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = videotoolbox.h; sourceTree = "<group>"; };
		184DB2CC2C2C28B500234BEB /* vorbis_parser.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = vorbis_parser.h; sourceTree = "<group>"; };
		184DB2CD2C2C28B500234BEB /* xvmc.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = xvmc.h; sourceTree = "<group>"; };
		184DB2CF2C2C28B500234BEB /* avfilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = avfilter.h; sourceTree = "<group>"; };
		184DB2D02C2C28B500234BEB /* avfiltergraph.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = avfiltergraph.h; sourceTree = "<group>"; };
		184DB2D12C2C28B500234BEB /* buffersink.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = buffersink.h; sourceTree = "<group>"; };
		184DB2D22C2C28B500234BEB /* buffersrc.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = buffersrc.h; sourceTree = "<group>"; };
		184DB2D32C2C28B500234BEB /* version.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = version.h; sourceTree = "<group>"; };
		184DB2D52C2C28B500234BEB /* avc.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = avc.h; sourceTree = "<group>"; };
		184DB2D62C2C28B500234BEB /* avformat.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = avformat.h; sourceTree = "<group>"; };
		184DB2D72C2C28B500234BEB /* avio_internal.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = avio_internal.h; sourceTree = "<group>"; };
		184DB2D82C2C28B500234BEB /* avio.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = avio.h; sourceTree = "<group>"; };
		184DB2D92C2C28B500234BEB /* flv.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = flv.h; sourceTree = "<group>"; };
		184DB2DA2C2C28B500234BEB /* id3v2.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = id3v2.h; sourceTree = "<group>"; };
		184DB2DB2C2C28B500234BEB /* internal.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = internal.h; sourceTree = "<group>"; };
		184DB2DC2C2C28B500234BEB /* metadata.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = metadata.h; sourceTree = "<group>"; };
		184DB2DD2C2C28B500234BEB /* os_support.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = os_support.h; sourceTree = "<group>"; };
		184DB2DE2C2C28B500234BEB /* url.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = url.h; sourceTree = "<group>"; };
		184DB2DF2C2C28B500234BEB /* version.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = version.h; sourceTree = "<group>"; };
		184DB2E12C2C28B500234BEB /* avconfig.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = avconfig.h; sourceTree = "<group>"; };
		184DB2E22C2C28B500234BEB /* ffversion.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ffversion.h; sourceTree = "<group>"; };
		184DB2E42C2C28B500234BEB /* avconfig.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = avconfig.h; sourceTree = "<group>"; };
		184DB2E52C2C28B500234BEB /* ffversion.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ffversion.h; sourceTree = "<group>"; };
		184DB2E72C2C28B500234BEB /* avconfig.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = avconfig.h; sourceTree = "<group>"; };
		184DB2E82C2C28B500234BEB /* ffversion.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ffversion.h; sourceTree = "<group>"; };
		184DB2EA2C2C28B500234BEB /* avconfig.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = avconfig.h; sourceTree = "<group>"; };
		184DB2EB2C2C28B500234BEB /* ffversion.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ffversion.h; sourceTree = "<group>"; };
		184DB2ED2C2C28B500234BEB /* adler32.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = adler32.h; sourceTree = "<group>"; };
		184DB2EE2C2C28B500234BEB /* aes_ctr.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = aes_ctr.h; sourceTree = "<group>"; };
		184DB2EF2C2C28B500234BEB /* aes.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = aes.h; sourceTree = "<group>"; };
		184DB2F02C2C28B500234BEB /* application.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = application.h; sourceTree = "<group>"; };
		184DB2F12C2C28B500234BEB /* attributes.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = attributes.h; sourceTree = "<group>"; };
		184DB2F22C2C28B500234BEB /* audio_fifo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = audio_fifo.h; sourceTree = "<group>"; };
		184DB2F32C2C28B500234BEB /* avassert.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = avassert.h; sourceTree = "<group>"; };
		184DB2F42C2C28B500234BEB /* avconfig.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = avconfig.h; sourceTree = "<group>"; };
		184DB2F52C2C28B500234BEB /* avstring.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = avstring.h; sourceTree = "<group>"; };
		184DB2F62C2C28B500234BEB /* avutil.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = avutil.h; sourceTree = "<group>"; };
		184DB2F72C2C28B500234BEB /* base64.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = base64.h; sourceTree = "<group>"; };
		184DB2F82C2C28B500234BEB /* blowfish.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = blowfish.h; sourceTree = "<group>"; };
		184DB2F92C2C28B500234BEB /* bprint.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = bprint.h; sourceTree = "<group>"; };
		184DB2FA2C2C28B500234BEB /* bswap.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = bswap.h; sourceTree = "<group>"; };
		184DB2FB2C2C28B500234BEB /* buffer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = buffer.h; sourceTree = "<group>"; };
		184DB2FC2C2C28B500234BEB /* camellia.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = camellia.h; sourceTree = "<group>"; };
		184DB2FD2C2C28B500234BEB /* cast5.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = cast5.h; sourceTree = "<group>"; };
		184DB2FE2C2C28B500234BEB /* channel_layout.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = channel_layout.h; sourceTree = "<group>"; };
		184DB2FF2C2C28B500234BEB /* common.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = common.h; sourceTree = "<group>"; };
		184DB3002C2C28B500234BEB /* cpu.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = cpu.h; sourceTree = "<group>"; };
		184DB3012C2C28B500234BEB /* crc.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = crc.h; sourceTree = "<group>"; };
		184DB3022C2C28B500234BEB /* des.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = des.h; sourceTree = "<group>"; };
		184DB3032C2C28B500234BEB /* dict.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = dict.h; sourceTree = "<group>"; };
		184DB3042C2C28B500234BEB /* display.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = display.h; sourceTree = "<group>"; };
		184DB3052C2C28B500234BEB /* dns_cache.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = dns_cache.h; sourceTree = "<group>"; };
		184DB3062C2C28B500234BEB /* downmix_info.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = downmix_info.h; sourceTree = "<group>"; };
		184DB3072C2C28B500234BEB /* encryption_info.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = encryption_info.h; sourceTree = "<group>"; };
		184DB3082C2C28B500234BEB /* error.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = error.h; sourceTree = "<group>"; };
		184DB3092C2C28B500234BEB /* eval.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = eval.h; sourceTree = "<group>"; };
		184DB30A2C2C28B500234BEB /* ffversion.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ffversion.h; sourceTree = "<group>"; };
		184DB30B2C2C28B500234BEB /* fifo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = fifo.h; sourceTree = "<group>"; };
		184DB30C2C2C28B500234BEB /* file.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = file.h; sourceTree = "<group>"; };
		184DB30D2C2C28B500234BEB /* frame.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = frame.h; sourceTree = "<group>"; };
		184DB30E2C2C28B500234BEB /* hash.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = hash.h; sourceTree = "<group>"; };
		184DB30F2C2C28B500234BEB /* hmac.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = hmac.h; sourceTree = "<group>"; };
		184DB3102C2C28B500234BEB /* hwcontext_cuda.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = hwcontext_cuda.h; sourceTree = "<group>"; };
		184DB3112C2C28B500234BEB /* hwcontext_d3d11va.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = hwcontext_d3d11va.h; sourceTree = "<group>"; };
		184DB3122C2C28B500234BEB /* hwcontext_drm.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = hwcontext_drm.h; sourceTree = "<group>"; };
		184DB3132C2C28B500234BEB /* hwcontext_dxva2.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = hwcontext_dxva2.h; sourceTree = "<group>"; };
		184DB3142C2C28B500234BEB /* hwcontext_mediacodec.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = hwcontext_mediacodec.h; sourceTree = "<group>"; };
		184DB3152C2C28B500234BEB /* hwcontext_qsv.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = hwcontext_qsv.h; sourceTree = "<group>"; };
		184DB3162C2C28B500234BEB /* hwcontext_vaapi.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = hwcontext_vaapi.h; sourceTree = "<group>"; };
		184DB3172C2C28B500234BEB /* hwcontext_vdpau.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = hwcontext_vdpau.h; sourceTree = "<group>"; };
		184DB3182C2C28B500234BEB /* hwcontext_videotoolbox.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = hwcontext_videotoolbox.h; sourceTree = "<group>"; };
		184DB3192C2C28B500234BEB /* hwcontext.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = hwcontext.h; sourceTree = "<group>"; };
		184DB31A2C2C28B500234BEB /* imgutils.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = imgutils.h; sourceTree = "<group>"; };
		184DB31B2C2C28B500234BEB /* intfloat.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = intfloat.h; sourceTree = "<group>"; };
		184DB31C2C2C28B500234BEB /* intreadwrite.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = intreadwrite.h; sourceTree = "<group>"; };
		184DB31D2C2C28B500234BEB /* lfg.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = lfg.h; sourceTree = "<group>"; };
		184DB31E2C2C28B500234BEB /* log.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = log.h; sourceTree = "<group>"; };
		184DB31F2C2C28B500234BEB /* macros.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = macros.h; sourceTree = "<group>"; };
		184DB3202C2C28B500234BEB /* mastering_display_metadata.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = mastering_display_metadata.h; sourceTree = "<group>"; };
		184DB3212C2C28B500234BEB /* mathematics.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = mathematics.h; sourceTree = "<group>"; };
		184DB3222C2C28B500234BEB /* md5.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = md5.h; sourceTree = "<group>"; };
		184DB3232C2C28B500234BEB /* mem.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = mem.h; sourceTree = "<group>"; };
		184DB3242C2C28B500234BEB /* motion_vector.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = motion_vector.h; sourceTree = "<group>"; };
		184DB3252C2C28B500234BEB /* murmur3.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = murmur3.h; sourceTree = "<group>"; };
		184DB3262C2C28B500234BEB /* opt.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = opt.h; sourceTree = "<group>"; };
		184DB3272C2C28B500234BEB /* parseutils.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = parseutils.h; sourceTree = "<group>"; };
		184DB3282C2C28B500234BEB /* pixdesc.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = pixdesc.h; sourceTree = "<group>"; };
		184DB3292C2C28B500234BEB /* pixelutils.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = pixelutils.h; sourceTree = "<group>"; };
		184DB32A2C2C28B500234BEB /* pixfmt.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = pixfmt.h; sourceTree = "<group>"; };
		184DB32B2C2C28B500234BEB /* random_seed.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = random_seed.h; sourceTree = "<group>"; };
		184DB32C2C2C28B500234BEB /* rational.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = rational.h; sourceTree = "<group>"; };
		184DB32D2C2C28B500234BEB /* rc4.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = rc4.h; sourceTree = "<group>"; };
		184DB32E2C2C28B500234BEB /* replaygain.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = replaygain.h; sourceTree = "<group>"; };
		184DB32F2C2C28B500234BEB /* ripemd.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ripemd.h; sourceTree = "<group>"; };
		184DB3302C2C28B500234BEB /* samplefmt.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = samplefmt.h; sourceTree = "<group>"; };
		184DB3312C2C28B500234BEB /* sha.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = sha.h; sourceTree = "<group>"; };
		184DB3322C2C28B500234BEB /* sha512.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = sha512.h; sourceTree = "<group>"; };
		184DB3332C2C28B500234BEB /* spherical.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = spherical.h; sourceTree = "<group>"; };
		184DB3342C2C28B500234BEB /* stereo3d.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = stereo3d.h; sourceTree = "<group>"; };
		184DB3352C2C28B500234BEB /* tea.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = tea.h; sourceTree = "<group>"; };
		184DB3362C2C28B500234BEB /* thread.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = thread.h; sourceTree = "<group>"; };
		184DB3372C2C28B500234BEB /* threadmessage.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = threadmessage.h; sourceTree = "<group>"; };
		184DB3382C2C28B500234BEB /* time.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = time.h; sourceTree = "<group>"; };
		184DB3392C2C28B500234BEB /* timecode.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = timecode.h; sourceTree = "<group>"; };
		184DB33A2C2C28B500234BEB /* timestamp.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = timestamp.h; sourceTree = "<group>"; };
		184DB33B2C2C28B500234BEB /* tree.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = tree.h; sourceTree = "<group>"; };
		184DB33C2C2C28B500234BEB /* twofish.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = twofish.h; sourceTree = "<group>"; };
		184DB33D2C2C28B500234BEB /* version.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = version.h; sourceTree = "<group>"; };
		184DB33E2C2C28B500234BEB /* xtea.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = xtea.h; sourceTree = "<group>"; };
		184DB3402C2C28B500234BEB /* config.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = config.h; sourceTree = "<group>"; };
		184DB3422C2C28B500234BEB /* config.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = config.h; sourceTree = "<group>"; };
		184DB3442C2C28B500234BEB /* config.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = config.h; sourceTree = "<group>"; };
		184DB3462C2C28B500234BEB /* config.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = config.h; sourceTree = "<group>"; };
		184DB3482C2C28B500234BEB /* config.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = config.h; sourceTree = "<group>"; };
		184DB34A2C2C28B500234BEB /* swresample.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = swresample.h; sourceTree = "<group>"; };
		184DB34B2C2C28B500234BEB /* version.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = version.h; sourceTree = "<group>"; };
		184DB34D2C2C28B500234BEB /* swscale.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = swscale.h; sourceTree = "<group>"; };
		184DB34E2C2C28B500234BEB /* version.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = version.h; sourceTree = "<group>"; };
		184DB3512C2C28B500234BEB /* aes.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = aes.h; sourceTree = "<group>"; };
		184DB3522C2C28B500234BEB /* asn1_mac.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = asn1_mac.h; sourceTree = "<group>"; };
		184DB3532C2C28B500234BEB /* asn1.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = asn1.h; sourceTree = "<group>"; };
		184DB3542C2C28B500234BEB /* asn1t.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = asn1t.h; sourceTree = "<group>"; };
		184DB3552C2C28B500234BEB /* bio.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = bio.h; sourceTree = "<group>"; };
		184DB3562C2C28B500234BEB /* blowfish.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = blowfish.h; sourceTree = "<group>"; };
		184DB3572C2C28B500234BEB /* bn.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = bn.h; sourceTree = "<group>"; };
		184DB3582C2C28B500234BEB /* buffer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = buffer.h; sourceTree = "<group>"; };
		184DB3592C2C28B500234BEB /* camellia.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = camellia.h; sourceTree = "<group>"; };
		184DB35A2C2C28B500234BEB /* cast.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = cast.h; sourceTree = "<group>"; };
		184DB35B2C2C28B500234BEB /* cmac.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = cmac.h; sourceTree = "<group>"; };
		184DB35C2C2C28B500234BEB /* cms.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = cms.h; sourceTree = "<group>"; };
		184DB35D2C2C28B500234BEB /* comp.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = comp.h; sourceTree = "<group>"; };
		184DB35E2C2C28B500234BEB /* conf_api.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = conf_api.h; sourceTree = "<group>"; };
		184DB35F2C2C28B500234BEB /* conf.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = conf.h; sourceTree = "<group>"; };
		184DB3602C2C28B500234BEB /* crypto.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = crypto.h; sourceTree = "<group>"; };
		184DB3612C2C28B500234BEB /* des_old.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = des_old.h; sourceTree = "<group>"; };
		184DB3622C2C28B500234BEB /* des.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = des.h; sourceTree = "<group>"; };
		184DB3632C2C28B500234BEB /* dh.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = dh.h; sourceTree = "<group>"; };
		184DB3642C2C28B500234BEB /* dsa.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = dsa.h; sourceTree = "<group>"; };
		184DB3652C2C28B500234BEB /* dso.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = dso.h; sourceTree = "<group>"; };
		184DB3662C2C28B500234BEB /* dtls1.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = dtls1.h; sourceTree = "<group>"; };
		184DB3672C2C28B500234BEB /* e_os2.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = e_os2.h; sourceTree = "<group>"; };
		184DB3682C2C28B500234BEB /* ebcdic.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ebcdic.h; sourceTree = "<group>"; };
		184DB3692C2C28B500234BEB /* ec.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ec.h; sourceTree = "<group>"; };
		184DB36A2C2C28B500234BEB /* ecdh.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ecdh.h; sourceTree = "<group>"; };
		184DB36B2C2C28B500234BEB /* ecdsa.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ecdsa.h; sourceTree = "<group>"; };
		184DB36C2C2C28B500234BEB /* engine.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = engine.h; sourceTree = "<group>"; };
		184DB36D2C2C28B500234BEB /* err.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = err.h; sourceTree = "<group>"; };
		184DB36E2C2C28B500234BEB /* evp.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = evp.h; sourceTree = "<group>"; };
		184DB36F2C2C28B500234BEB /* hmac.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = hmac.h; sourceTree = "<group>"; };
		184DB3702C2C28B500234BEB /* idea.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = idea.h; sourceTree = "<group>"; };
		184DB3712C2C28B500234BEB /* krb5_asn.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = krb5_asn.h; sourceTree = "<group>"; };
		184DB3722C2C28B500234BEB /* kssl.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = kssl.h; sourceTree = "<group>"; };
		184DB3732C2C28B500234BEB /* lhash.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = lhash.h; sourceTree = "<group>"; };
		184DB3742C2C28B500234BEB /* md4.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = md4.h; sourceTree = "<group>"; };
		184DB3752C2C28B500234BEB /* md5.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = md5.h; sourceTree = "<group>"; };
		184DB3762C2C28B500234BEB /* mdc2.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = mdc2.h; sourceTree = "<group>"; };
		184DB3772C2C28B500234BEB /* modes.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = modes.h; sourceTree = "<group>"; };
		184DB3782C2C28B500234BEB /* obj_mac.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = obj_mac.h; sourceTree = "<group>"; };
		184DB3792C2C28B500234BEB /* objects.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = objects.h; sourceTree = "<group>"; };
		184DB37A2C2C28B500234BEB /* ocsp.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ocsp.h; sourceTree = "<group>"; };
		184DB37B2C2C28B500234BEB /* opensslconf.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = opensslconf.h; sourceTree = "<group>"; };
		184DB37C2C2C28B500234BEB /* opensslv.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = opensslv.h; sourceTree = "<group>"; };
		184DB37D2C2C28B500234BEB /* ossl_typ.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ossl_typ.h; sourceTree = "<group>"; };
		184DB37E2C2C28B500234BEB /* pem.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = pem.h; sourceTree = "<group>"; };
		184DB37F2C2C28B500234BEB /* pem2.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = pem2.h; sourceTree = "<group>"; };
		184DB3802C2C28B500234BEB /* pkcs7.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = pkcs7.h; sourceTree = "<group>"; };
		184DB3812C2C28B500234BEB /* pkcs12.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = pkcs12.h; sourceTree = "<group>"; };
		184DB3822C2C28B500234BEB /* pqueue.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = pqueue.h; sourceTree = "<group>"; };
		184DB3832C2C28B500234BEB /* rand.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = rand.h; sourceTree = "<group>"; };
		184DB3842C2C28B500234BEB /* rc2.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = rc2.h; sourceTree = "<group>"; };
		184DB3852C2C28B500234BEB /* rc4.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = rc4.h; sourceTree = "<group>"; };
		184DB3862C2C28B500234BEB /* ripemd.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ripemd.h; sourceTree = "<group>"; };
		184DB3872C2C28B500234BEB /* rsa.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = rsa.h; sourceTree = "<group>"; };
		184DB3882C2C28B500234BEB /* safestack.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = safestack.h; sourceTree = "<group>"; };
		184DB3892C2C28B500234BEB /* seed.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = seed.h; sourceTree = "<group>"; };
		184DB38A2C2C28B500234BEB /* sha.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = sha.h; sourceTree = "<group>"; };
		184DB38B2C2C28B500234BEB /* srp.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = srp.h; sourceTree = "<group>"; };
		184DB38C2C2C28B500234BEB /* srtp.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = srtp.h; sourceTree = "<group>"; };
		184DB38D2C2C28B500234BEB /* ssl.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ssl.h; sourceTree = "<group>"; };
		184DB38E2C2C28B500234BEB /* ssl2.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ssl2.h; sourceTree = "<group>"; };
		184DB38F2C2C28B500234BEB /* ssl3.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ssl3.h; sourceTree = "<group>"; };
		184DB3902C2C28B500234BEB /* ssl23.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ssl23.h; sourceTree = "<group>"; };
		184DB3912C2C28B500234BEB /* stack.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = stack.h; sourceTree = "<group>"; };
		184DB3922C2C28B500234BEB /* symhacks.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = symhacks.h; sourceTree = "<group>"; };
		184DB3932C2C28B500234BEB /* tls1.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = tls1.h; sourceTree = "<group>"; };
		184DB3942C2C28B500234BEB /* ts.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ts.h; sourceTree = "<group>"; };
		184DB3952C2C28B500234BEB /* txt_db.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = txt_db.h; sourceTree = "<group>"; };
		184DB3962C2C28B500234BEB /* ui_compat.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ui_compat.h; sourceTree = "<group>"; };
		184DB3972C2C28B500234BEB /* ui.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ui.h; sourceTree = "<group>"; };
		184DB3982C2C28B500234BEB /* whrlpool.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = whrlpool.h; sourceTree = "<group>"; };
		184DB3992C2C28B500234BEB /* x509_vfy.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = x509_vfy.h; sourceTree = "<group>"; };
		184DB39A2C2C28B500234BEB /* x509.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = x509.h; sourceTree = "<group>"; };
		184DB39B2C2C28B500234BEB /* x509v3.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = x509v3.h; sourceTree = "<group>"; };
		184DB39E2C2C28B500234BEB /* libavcodec.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; path = libavcodec.a; sourceTree = "<group>"; };
		184DB39F2C2C28B500234BEB /* libavfilter.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; path = libavfilter.a; sourceTree = "<group>"; };
		184DB3A02C2C28B500234BEB /* libavformat.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; path = libavformat.a; sourceTree = "<group>"; };
		184DB3A12C2C28B500234BEB /* libavutil.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; path = libavutil.a; sourceTree = "<group>"; };
		184DB3A22C2C28B500234BEB /* libswresample.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; path = libswresample.a; sourceTree = "<group>"; };
		184DB3A32C2C28B500234BEB /* libswscale.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; path = libswscale.a; sourceTree = "<group>"; };
		184DB3A52C2C28B500234BEB /* libcrypto.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; path = libcrypto.a; sourceTree = "<group>"; };
		184DB3A62C2C28B500234BEB /* libssl.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; path = libssl.a; sourceTree = "<group>"; };
		184DB3AA2C2C28B500234BEB /* libiot_video_p2p.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; path = libiot_video_p2p.a; sourceTree = "<group>"; };
		184DB3AB2C2C28B500234BEB /* liblibevent.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; path = liblibevent.a; sourceTree = "<group>"; };
		184DB3AC2C2C28B500234BEB /* libmbedtls1.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; path = libmbedtls1.a; sourceTree = "<group>"; };
		184DB3AF2C2C28B500234BEB /* IVVAS.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IVVAS.h; sourceTree = "<group>"; };
		184DB3B22C2C28B500234BEB /* 替换SDK需替换所有文件 */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = "替换SDK需替换所有文件"; sourceTree = "<group>"; };
		184DB3B52C2C28B500234BEB /* NIotManager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NIotManager.swift; sourceTree = "<group>"; };
		184DB3B82C2C28B500234BEB /* NIotNetworking.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NIotNetworking.swift; sourceTree = "<group>"; };
		184DB3BC2C2C28B500234BEB /* Foundation.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Foundation.swift; sourceTree = "<group>"; };
		184DB3BD2C2C28B500234BEB /* Notifications+Names.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "Notifications+Names.swift"; sourceTree = "<group>"; };
		184DB3BE2C2C28B500234BEB /* String+AES.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "String+AES.swift"; sourceTree = "<group>"; };
		184DB3BF2C2C28B500234BEB /* String+Extension.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "String+Extension.swift"; sourceTree = "<group>"; };
		184DB3C12C2C28B500234BEB /* DistributionNetService.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = DistributionNetService.swift; sourceTree = "<group>"; };
		184DB3C32C2C28B500234BEB /* PropertyModel_v1.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PropertyModel_v1.swift; sourceTree = "<group>"; };
		184DB3C42C2C28B500234BEB /* PropertyModel_v2.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PropertyModel_v2.swift; sourceTree = "<group>"; };
		184DB3C52C2C28B500234BEB /* PropertyModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PropertyModel.swift; sourceTree = "<group>"; };
		184DB3C62C2C28B500234BEB /* PropertyModelPath.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PropertyModelPath.swift; sourceTree = "<group>"; };
		184DB3C82C2C28B500234BEB /* IoTMonitorPlayer.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = IoTMonitorPlayer.swift; sourceTree = "<group>"; };
		184DB3C92C2C28B500234BEB /* IVMonitorPlayer+IoT.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "IVMonitorPlayer+IoT.swift"; sourceTree = "<group>"; };
		184DB3CB2C2C28B500234BEB /* IoTFileDownloader.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = IoTFileDownloader.swift; sourceTree = "<group>"; };
		184DB3CC2C2C28B500234BEB /* IoTPlaybackPlayer.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = IoTPlaybackPlayer.swift; sourceTree = "<group>"; };
		184DB3CD2C2C28B500234BEB /* IVPlaybackPlayer_v2.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = IVPlaybackPlayer_v2.swift; sourceTree = "<group>"; };
		184DB3CE2C2C28B500234BEB /* IVPlaybackPlayer+IoT.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "IVPlaybackPlayer+IoT.swift"; sourceTree = "<group>"; };
		184DB3D02C2C28B500234BEB /* IoTExploreOrVideoDeviceModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = IoTExploreOrVideoDeviceModel.swift; sourceTree = "<group>"; };
		184DB3D22C2C28B500234BEB /* IoTConnection.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = IoTConnection.swift; sourceTree = "<group>"; };
		184DB3D32C2C28B500234BEB /* IoTPlayer.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = IoTPlayer.swift; sourceTree = "<group>"; };
		184DB3D42C2C28B500234BEB /* IVPlayer_v2.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = IVPlayer_v2.swift; sourceTree = "<group>"; };
		184DB3D52C2C28B500234BEB /* IVPlayer+IoT.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "IVPlayer+IoT.swift"; sourceTree = "<group>"; };
		184DB3D72C2C28B500234BEB /* IoTPlaybackPage.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = IoTPlaybackPage.swift; sourceTree = "<group>"; };
		184DB3D82C2C28B500234BEB /* IoTPlaybackService_v1.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = IoTPlaybackService_v1.swift; sourceTree = "<group>"; };
		184DB3D92C2C28B500234BEB /* IoTPlaybackService_v2.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = IoTPlaybackService_v2.swift; sourceTree = "<group>"; };
		184DB3DA2C2C28B500234BEB /* IoTPlaybackService.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = IoTPlaybackService.swift; sourceTree = "<group>"; };
		184DB3DB2C2C28B500234BEB /* IoTService_v1.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = IoTService_v1.swift; sourceTree = "<group>"; };
		184DB3DC2C2C28B500234BEB /* IoTService_v2.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = IoTService_v2.swift; sourceTree = "<group>"; };
		184DB3DD2C2C28B500234BEB /* IoTService.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = IoTService.swift; sourceTree = "<group>"; };
		184DB3DE2C2C28B500234BEB /* NIotSignalMessager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NIotSignalMessager.swift; sourceTree = "<group>"; };
		184DB3E02C2C28B500234BEB /* NIotPlaybackItem.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NIotPlaybackItem.swift; sourceTree = "<group>"; };
		184DB3E12C2C28B500234BEB /* NVDeviceModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NVDeviceModel.swift; sourceTree = "<group>"; };
		184DB3E32C2C28B500234BEB /* EWKeychain.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = EWKeychain.swift; sourceTree = "<group>"; };
		184DB3E42C2C28B500234BEB /* NVAPI.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NVAPI.swift; sourceTree = "<group>"; };
		184DB3E52C2C28B500234BEB /* NVM3U8NetworkManager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NVM3U8NetworkManager.swift; sourceTree = "<group>"; };
		184DB3E62C2C28B500234BEB /* NVNetworkManager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NVNetworkManager.swift; sourceTree = "<group>"; };
		184DB3E72C2C28B500234BEB /* Response.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Response.swift; sourceTree = "<group>"; };
		184DB3E92C2C28B500234BEB /* NVEnvironment.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NVEnvironment.swift; sourceTree = "<group>"; };
		184DB3EA2C2C28B500234BEB /* NVJson.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NVJson.swift; sourceTree = "<group>"; };
		184DB3EB2C2C28B500234BEB /* NVKeychain.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NVKeychain.swift; sourceTree = "<group>"; };
		184DB3EC2C2C28B500234BEB /* NVLog.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NVLog.swift; sourceTree = "<group>"; };
		184DB3ED2C2C28B500234BEB /* NVTencentModelCommand.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NVTencentModelCommand.swift; sourceTree = "<group>"; };
		184DB3EE2C2C28B500234BEB /* NVUtils.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NVUtils.swift; sourceTree = "<group>"; };
		184DB3F12C2C28B500234BEB /* 打包注意.md */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = net.daringfireball.markdown; path = "打包注意.md"; sourceTree = "<group>"; };
		184DB3F22C2C28B500234BEB /* BluetoothCentralManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BluetoothCentralManager.h; sourceTree = "<group>"; };
		184DB3F32C2C28B500234BEB /* CMPageContentView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CMPageContentView.h; sourceTree = "<group>"; };
		184DB3F42C2C28B500234BEB /* CMPageTitleConfig.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CMPageTitleConfig.h; sourceTree = "<group>"; };
		184DB3F52C2C28B500234BEB /* CMPageTitleContentView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CMPageTitleContentView.h; sourceTree = "<group>"; };
		184DB3F62C2C28B500234BEB /* CMPageTitleView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CMPageTitleView.h; sourceTree = "<group>"; };
		184DB3F72C2C28B500234BEB /* CMPageTitleViewMacro.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CMPageTitleViewMacro.h; sourceTree = "<group>"; };
		184DB3F82C2C28B500234BEB /* ESP_ByteUtil.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ESP_ByteUtil.h; sourceTree = "<group>"; };
		184DB3F92C2C28B500234BEB /* ESP_CRC8.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ESP_CRC8.h; sourceTree = "<group>"; };
		184DB3FA2C2C28B500234BEB /* ESP_NetUtil.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ESP_NetUtil.h; sourceTree = "<group>"; };
		184DB3FB2C2C28B500234BEB /* ESP_WifiUtil.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ESP_WifiUtil.h; sourceTree = "<group>"; };
		184DB3FC2C2C28B500234BEB /* ESPAES.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ESPAES.h; sourceTree = "<group>"; };
		184DB3FD2C2C28B500234BEB /* ESPDataCode.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ESPDataCode.h; sourceTree = "<group>"; };
		184DB3FE2C2C28B500234BEB /* ESPDatumCode.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ESPDatumCode.h; sourceTree = "<group>"; };
		184DB3FF2C2C28B500234BEB /* ESPGuideCode.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ESPGuideCode.h; sourceTree = "<group>"; };
		184DB4002C2C28B500234BEB /* ESPTools.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ESPTools.h; sourceTree = "<group>"; };
		184DB4012C2C28B500234BEB /* ESPTouchDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ESPTouchDelegate.h; sourceTree = "<group>"; };
		184DB4022C2C28B500234BEB /* ESPTouchGenerator.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ESPTouchGenerator.h; sourceTree = "<group>"; };
		184DB4032C2C28B500234BEB /* ESPTouchResult.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ESPTouchResult.h; sourceTree = "<group>"; };
		184DB4042C2C28B500234BEB /* ESPTouchTask.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ESPTouchTask.h; sourceTree = "<group>"; };
		184DB4052C2C28B500234BEB /* ESPTouchTaskParameter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ESPTouchTaskParameter.h; sourceTree = "<group>"; };
		184DB4062C2C28B500234BEB /* ESPUDPSocketClient.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ESPUDPSocketClient.h; sourceTree = "<group>"; };
		184DB4072C2C28B500234BEB /* ESPUDPSocketServer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ESPUDPSocketServer.h; sourceTree = "<group>"; };
		184DB4082C2C28B500234BEB /* ESPVersionMacro.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ESPVersionMacro.h; sourceTree = "<group>"; };
		184DB4092C2C28B500234BEB /* HXYNotice.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HXYNotice.h; sourceTree = "<group>"; };
		184DB40A2C2C28B500234BEB /* MBProgressHUD+XDP.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "MBProgressHUD+XDP.h"; sourceTree = "<group>"; };
		184DB40B2C2C28B500234BEB /* NSObject+additions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSObject+additions.h"; sourceTree = "<group>"; };
		184DB40C2C2C28B500234BEB /* NSString+Extension.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSString+Extension.h"; sourceTree = "<group>"; };
		184DB40D2C2C28B500234BEB /* route.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = route.h; sourceTree = "<group>"; };
		184DB40E2C2C28B500234BEB /* TCSocket.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TCSocket.h; sourceTree = "<group>"; };
		184DB40F2C2C28B500234BEB /* TIoTCodeAddress.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TIoTCodeAddress.h; sourceTree = "<group>"; };
		184DB4102C2C28B500234BEB /* TIoTCoreAccountSet.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TIoTCoreAccountSet.h; sourceTree = "<group>"; };
		184DB4112C2C28B500234BEB /* TIoTCoreAddDevice.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TIoTCoreAddDevice.h; sourceTree = "<group>"; };
		184DB4122C2C28B500234BEB /* TIoTCoreAPISets.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TIoTCoreAPISets.h; sourceTree = "<group>"; };
		184DB4132C2C28B500234BEB /* TIoTCoreAppEnvironment.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TIoTCoreAppEnvironment.h; sourceTree = "<group>"; };
		184DB4142C2C28B500234BEB /* TIoTCoreDeviceCenter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TIoTCoreDeviceCenter.h; sourceTree = "<group>"; };
		184DB4152C2C28B500234BEB /* TIoTCoreDeviceSet.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TIoTCoreDeviceSet.h; sourceTree = "<group>"; };
		184DB4162C2C28B500234BEB /* TIoTCoreFamilySet.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TIoTCoreFamilySet.h; sourceTree = "<group>"; };
		184DB4172C2C28B500234BEB /* TIoTCoreFoundation.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TIoTCoreFoundation.h; sourceTree = "<group>"; };
		184DB4182C2C28B500234BEB /* TIoTCoreHudLoadingView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TIoTCoreHudLoadingView.h; sourceTree = "<group>"; };
		184DB4192C2C28B500234BEB /* TIoTCoreLogReport.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TIoTCoreLogReport.h; sourceTree = "<group>"; };
		184DB41A2C2C28B500234BEB /* TIoTCoreMessageSet.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TIoTCoreMessageSet.h; sourceTree = "<group>"; };
		184DB41B2C2C28B500234BEB /* TIoTCoreObject.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TIoTCoreObject.h; sourceTree = "<group>"; };
		184DB41C2C2C28B500234BEB /* TIoTCoreParts.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TIoTCoreParts.h; sourceTree = "<group>"; };
		184DB41D2C2C28B500234BEB /* TIoTCoreQMacros.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TIoTCoreQMacros.h; sourceTree = "<group>"; };
		184DB41E2C2C28B500234BEB /* TIoTCoreRequestAction.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TIoTCoreRequestAction.h; sourceTree = "<group>"; };
		184DB41F2C2C28B500234BEB /* TIoTCoreRequestBuilder.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TIoTCoreRequestBuilder.h; sourceTree = "<group>"; };
		184DB4202C2C28B500234BEB /* TIoTCoreRequestClient.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TIoTCoreRequestClient.h; sourceTree = "<group>"; };
		184DB4212C2C28B500234BEB /* TIoTCoreRequestObj.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TIoTCoreRequestObj.h; sourceTree = "<group>"; };
		184DB4222C2C28B500234BEB /* TIoTCoreRequestObject.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TIoTCoreRequestObject.h; sourceTree = "<group>"; };
		184DB4232C2C28B500234BEB /* TIoTCoreServices.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TIoTCoreServices.h; sourceTree = "<group>"; };
		184DB4242C2C28B500234BEB /* TIoTCoreSocketCover.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TIoTCoreSocketCover.h; sourceTree = "<group>"; };
		184DB4252C2C28B500234BEB /* TIoTCoreSocketManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TIoTCoreSocketManager.h; sourceTree = "<group>"; };
		184DB4262C2C28B500234BEB /* TIoTCoreUserManage.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TIoTCoreUserManage.h; sourceTree = "<group>"; };
		184DB4272C2C28B500234BEB /* TIoTCoreUtil.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TIoTCoreUtil.h; sourceTree = "<group>"; };
		184DB4282C2C28B500234BEB /* TIoTCoreWebSocket.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TIoTCoreWebSocket.h; sourceTree = "<group>"; };
		184DB4292C2C28B500234BEB /* TIoTCoreWMacros.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TIoTCoreWMacros.h; sourceTree = "<group>"; };
		184DB42A2C2C28B500234BEB /* TIoTDataTracking.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TIoTDataTracking.h; sourceTree = "<group>"; };
		184DB42B2C2C28B500234BEB /* TIoTGetgateway.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TIoTGetgateway.h; sourceTree = "<group>"; };
		184DB42C2C2C28B500234BEB /* TIoTLinkKit-umbrella.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "TIoTLinkKit-umbrella.h"; sourceTree = "<group>"; };
		184DB42D2C2C28B500234BEB /* TIoTPrintLogFileManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TIoTPrintLogFileManager.h; sourceTree = "<group>"; };
		184DB42E2C2C28B500234BEB /* TIoTPrintLogFormatter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TIoTPrintLogFormatter.h; sourceTree = "<group>"; };
		184DB42F2C2C28B500234BEB /* TIoTPrintLogManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TIoTPrintLogManager.h; sourceTree = "<group>"; };
		184DB4302C2C28B500234BEB /* TIoTRouter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TIoTRouter.h; sourceTree = "<group>"; };
		184DB4312C2C28B500234BEB /* TIotSoftApUdpSocketUtil.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TIotSoftApUdpSocketUtil.h; sourceTree = "<group>"; };
		184DB4322C2C28B500234BEB /* TIOTTRTCModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TIOTTRTCModel.h; sourceTree = "<group>"; };
		184DB4332C2C28B500234BEB /* TIoTTRTCSessionManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TIoTTRTCSessionManager.h; sourceTree = "<group>"; };
		184DB4342C2C28B500234BEB /* TIoTVideoDistributionNetModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TIoTVideoDistributionNetModel.h; sourceTree = "<group>"; };
		184DB4352C2C28B500234BEB /* TRTCCalling.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TRTCCalling.h; sourceTree = "<group>"; };
		184DB4362C2C28B500234BEB /* TRTCCalling+Signal.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "TRTCCalling+Signal.h"; sourceTree = "<group>"; };
		184DB4372C2C28B500234BEB /* TRTCCallingDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TRTCCallingDelegate.h; sourceTree = "<group>"; };
		184DB4382C2C28B500234BEB /* TRTCCallingHeader.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TRTCCallingHeader.h; sourceTree = "<group>"; };
		184DB4392C2C28B500234BEB /* TRTCCallingModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TRTCCallingModel.h; sourceTree = "<group>"; };
		184DB43A2C2C28B500234BEB /* TRTCCallingUtils.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TRTCCallingUtils.h; sourceTree = "<group>"; };
		184DB43B2C2C28B500234BEB /* UIColor+Color.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIColor+Color.h"; sourceTree = "<group>"; };
		184DB43C2C2C28B500234BEB /* UIDevice+Until.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIDevice+Until.h"; sourceTree = "<group>"; };
		184DB43D2C2C28B500234BEB /* UIFont+TIoTFont.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIFont+TIoTFont.h"; sourceTree = "<group>"; };
		184DB43E2C2C28B500234BEB /* UIView+CMCommon.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIView+CMCommon.h"; sourceTree = "<group>"; };
		184DB4402C2C28B500234BEB /* TIoTLinkKit */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; path = TIoTLinkKit; sourceTree = "<group>"; };
		184DB4422C2C28B500234BEB /* NiViewIoT.modulemap */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = "sourcecode.module-map"; path = NiViewIoT.modulemap; sourceTree = "<group>"; };
		184DB5EB2C2D604500234BEB /* NIotMonitorPlayer.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NIotMonitorPlayer.swift; sourceTree = "<group>"; };
		184DB5EC2C2D604500234BEB /* NIotConnection.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NIotConnection.swift; sourceTree = "<group>"; };
		184DB5ED2C2D604500234BEB /* NIotPlayer.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NIotPlayer.swift; sourceTree = "<group>"; };
		184DB5EE2C2D604500234BEB /* NIotPlaybackPlayer.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NIotPlaybackPlayer.swift; sourceTree = "<group>"; };
		187BA4D22C2BEC5D00AC4A65 /* NiViewIoT.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = NiViewIoT.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		187BA4D52C2BEC5D00AC4A65 /* NiViewIoT.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = NiViewIoT.h; sourceTree = "<group>"; };
		187BA4D62C2BEC5D00AC4A65 /* NiViewIoT.docc */ = {isa = PBXFileReference; lastKnownFileType = folder.documentationcatalog; path = NiViewIoT.docc; sourceTree = "<group>"; };
		18AB14472D967F9500B98B31 /* XP2PDelegateForwarder.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XP2PDelegateForwarder.swift; sourceTree = "<group>"; };
		18ECA7042C89B4AC00319EE9 /* IoTVideo */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; path = IoTVideo; sourceTree = "<group>"; };
		18ECA7062C89B4F600319EE9 /* IVVAS */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; path = IVVAS; sourceTree = "<group>"; };
		18FA8D532C5CD6D000DA5EC5 /* NIotOTAManager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NIotOTAManager.swift; sourceTree = "<group>"; };
		18FA8D542C5CD6D000DA5EC5 /* NIotConnectionPreparer.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NIotConnectionPreparer.swift; sourceTree = "<group>"; };
		18FC86A22C2EC9E100AA1F14 /* MQTTService.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MQTTService.swift; sourceTree = "<group>"; };
		18FC86A42C3680D900AA1F14 /* NIotFileDownloader.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NIotFileDownloader.swift; sourceTree = "<group>"; };
		469254C27C7C6F9C50C95899 /* Pods_NiViewIoT.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_NiViewIoT.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		599FF40C7949EDBEE6151396 /* Pods-NiViewIoT.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-NiViewIoT.release.xcconfig"; path = "Target Support Files/Pods-NiViewIoT/Pods-NiViewIoT.release.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		187BA4CF2C2BEC5D00AC4A65 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				18ECA7052C89B4AC00319EE9 /* IoTVideo in Frameworks */,
				184DB5542C2C28B500234BEB /* libmbedtls1.a in Frameworks */,
				184DB5522C2C28B500234BEB /* libiot_video_p2p.a in Frameworks */,
				184DB5512C2C28B500234BEB /* libssl.a in Frameworks */,
				184DB54D2C2C28B500234BEB /* libavutil.a in Frameworks */,
				184DB5D62C2C28B600234BEB /* TIoTLinkKit in Frameworks */,
				184DB54B2C2C28B500234BEB /* libavfilter.a in Frameworks */,
				184DB5532C2C28B500234BEB /* liblibevent.a in Frameworks */,
				1E6A98DCB146327A81879F81 /* Pods_NiViewIoT.framework in Frameworks */,
				18ECA7072C89B4F600319EE9 /* IVVAS in Frameworks */,
				184DB54F2C2C28B500234BEB /* libswscale.a in Frameworks */,
				184DB54C2C2C28B500234BEB /* libavformat.a in Frameworks */,
				184DB54E2C2C28B500234BEB /* libswresample.a in Frameworks */,
				184DB54A2C2C28B500234BEB /* libavcodec.a in Frameworks */,
				184DB5502C2C28B500234BEB /* libcrypto.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		0817359AA7EDD734220B4A0F /* Pods */ = {
			isa = PBXGroup;
			children = (
				0DE67FDB82E207A0991E25C5 /* Pods-NiViewIoT.debug.xcconfig */,
				599FF40C7949EDBEE6151396 /* Pods-NiViewIoT.release.xcconfig */,
			);
			name = Pods;
			path = ../Pods;
			sourceTree = "<group>";
		};
		184DB2BA2C2C28B500234BEB /* IoTLib */ = {
			isa = PBXGroup;
			children = (
				18ECA7042C89B4AC00319EE9 /* IoTVideo */,
				184DB2852C2C28B500234BEB /* IoTVideo.h */,
				184DB2862C2C28B500234BEB /* IVAudioCapturable.h */,
				184DB2882C2C28B500234BEB /* IVAudioDecodable.h */,
				184DB2892C2C28B500234BEB /* IVAudioDecoder.h */,
				184DB28A2C2C28B500234BEB /* IVAudioEncodable.h */,
				184DB28B2C2C28B500234BEB /* IVAudioEncoder.h */,
				184DB28C2C2C28B500234BEB /* IVAudioRenderable.h */,
				184DB28D2C2C28B500234BEB /* IVAudioUnit.h */,
				184DB28E2C2C28B500234BEB /* IVAVDefine.h */,
				184DB28F2C2C28B500234BEB /* IVAVRecordable.h */,
				184DB2902C2C28B500234BEB /* IVAVRecorder.h */,
				184DB2912C2C28B500234BEB /* IVAVUtils.h */,
				184DB2952C2C28B500234BEB /* IVConnection.h */,
				184DB2962C2C28B500234BEB /* IVConstant.h */,
				184DB2982C2C28B500234BEB /* IVDeviceMgr.h */,
				184DB2992C2C28B500234BEB /* IVError.h */,
				184DB29A2C2C28B500234BEB /* IVFileDownloader.h */,
				184DB29B2C2C28B500234BEB /* IVLanNetConfig.h */,
				184DB29C2C2C28B500234BEB /* IVLivePlayer.h */,
				184DB29D2C2C28B500234BEB /* IVLog.h */,
				184DB29F2C2C28B500234BEB /* IVMacros.h */,
				184DB2A02C2C28B500234BEB /* IVMessageMgr.h */,
				184DB2A12C2C28B500234BEB /* IVMonitorPlayer.h */,
				184DB2A32C2C28B500234BEB /* IVNetConfig.h */,
				184DB2A42C2C28B500234BEB /* IVNetwork_p2p.h */,
				184DB2A52C2C28B500234BEB /* IVNetworkHelper.h */,
				184DB2A62C2C28B500234BEB /* IVNetworkSign.h */,
				184DB2A72C2C28B500234BEB /* IVPlaybackPlayer.h */,
				184DB2A82C2C28B500234BEB /* IVPlayer.h */,
				184DB2A92C2C28B500234BEB /* IVQRCodeHelper.h */,
				184DB2AA2C2C28B500234BEB /* IVQRCodeNetConfig.h */,
				184DB2AB2C2C28B500234BEB /* IVReachability.h */,
				184DB2AD2C2C28B500234BEB /* IVTimer.h */,
				184DB2AE2C2C28B500234BEB /* IVTransmission.h */,
				184DB2B02C2C28B500234BEB /* IVUtils.h */,
				184DB2B12C2C28B500234BEB /* IVVideoCapturable.h */,
				184DB2B22C2C28B500234BEB /* IVVideoCapture.h */,
				184DB2B32C2C28B500234BEB /* IVVideoDecodable.h */,
				184DB2B42C2C28B500234BEB /* IVVideoDecoder.h */,
				184DB2B52C2C28B500234BEB /* IVVideoEncodable.h */,
				184DB2B62C2C28B500234BEB /* IVVideoEncoder.h */,
				184DB2B82C2C28B500234BEB /* IVVideoRender.h */,
				184DB2B92C2C28B500234BEB /* IVVideoRenderable.h */,
			);
			path = IoTLib;
			sourceTree = "<group>";
		};
		184DB2CE2C2C28B500234BEB /* libavcodec */ = {
			isa = PBXGroup;
			children = (
				184DB2BB2C2C28B500234BEB /* ac3_parser.h */,
				184DB2BC2C2C28B500234BEB /* adts_parser.h */,
				184DB2BD2C2C28B500234BEB /* avcodec.h */,
				184DB2BE2C2C28B500234BEB /* avdct.h */,
				184DB2BF2C2C28B500234BEB /* avfft.h */,
				184DB2C02C2C28B500234BEB /* d3d11va.h */,
				184DB2C12C2C28B500234BEB /* dirac.h */,
				184DB2C22C2C28B500234BEB /* dv_profile.h */,
				184DB2C32C2C28B500234BEB /* dxva2.h */,
				184DB2C42C2C28B500234BEB /* jni.h */,
				184DB2C52C2C28B500234BEB /* mediacodec.h */,
				184DB2C62C2C28B500234BEB /* qsv.h */,
				184DB2C72C2C28B500234BEB /* vaapi.h */,
				184DB2C82C2C28B500234BEB /* vda.h */,
				184DB2C92C2C28B500234BEB /* vdpau.h */,
				184DB2CA2C2C28B500234BEB /* version.h */,
				184DB2CB2C2C28B500234BEB /* videotoolbox.h */,
				184DB2CC2C2C28B500234BEB /* vorbis_parser.h */,
				184DB2CD2C2C28B500234BEB /* xvmc.h */,
			);
			path = libavcodec;
			sourceTree = "<group>";
		};
		184DB2D42C2C28B500234BEB /* libavfilter */ = {
			isa = PBXGroup;
			children = (
				184DB2CF2C2C28B500234BEB /* avfilter.h */,
				184DB2D02C2C28B500234BEB /* avfiltergraph.h */,
				184DB2D12C2C28B500234BEB /* buffersink.h */,
				184DB2D22C2C28B500234BEB /* buffersrc.h */,
				184DB2D32C2C28B500234BEB /* version.h */,
			);
			path = libavfilter;
			sourceTree = "<group>";
		};
		184DB2E02C2C28B500234BEB /* libavformat */ = {
			isa = PBXGroup;
			children = (
				184DB2D52C2C28B500234BEB /* avc.h */,
				184DB2D62C2C28B500234BEB /* avformat.h */,
				184DB2D72C2C28B500234BEB /* avio_internal.h */,
				184DB2D82C2C28B500234BEB /* avio.h */,
				184DB2D92C2C28B500234BEB /* flv.h */,
				184DB2DA2C2C28B500234BEB /* id3v2.h */,
				184DB2DB2C2C28B500234BEB /* internal.h */,
				184DB2DC2C2C28B500234BEB /* metadata.h */,
				184DB2DD2C2C28B500234BEB /* os_support.h */,
				184DB2DE2C2C28B500234BEB /* url.h */,
				184DB2DF2C2C28B500234BEB /* version.h */,
			);
			path = libavformat;
			sourceTree = "<group>";
		};
		184DB2E32C2C28B500234BEB /* arm64 */ = {
			isa = PBXGroup;
			children = (
				184DB2E12C2C28B500234BEB /* avconfig.h */,
				184DB2E22C2C28B500234BEB /* ffversion.h */,
			);
			path = arm64;
			sourceTree = "<group>";
		};
		184DB2E62C2C28B500234BEB /* armv7 */ = {
			isa = PBXGroup;
			children = (
				184DB2E42C2C28B500234BEB /* avconfig.h */,
				184DB2E52C2C28B500234BEB /* ffversion.h */,
			);
			path = armv7;
			sourceTree = "<group>";
		};
		184DB2E92C2C28B500234BEB /* i386 */ = {
			isa = PBXGroup;
			children = (
				184DB2E72C2C28B500234BEB /* avconfig.h */,
				184DB2E82C2C28B500234BEB /* ffversion.h */,
			);
			path = i386;
			sourceTree = "<group>";
		};
		184DB2EC2C2C28B500234BEB /* x86_64 */ = {
			isa = PBXGroup;
			children = (
				184DB2EA2C2C28B500234BEB /* avconfig.h */,
				184DB2EB2C2C28B500234BEB /* ffversion.h */,
			);
			path = x86_64;
			sourceTree = "<group>";
		};
		184DB33F2C2C28B500234BEB /* libavutil */ = {
			isa = PBXGroup;
			children = (
				184DB2E32C2C28B500234BEB /* arm64 */,
				184DB2E62C2C28B500234BEB /* armv7 */,
				184DB2E92C2C28B500234BEB /* i386 */,
				184DB2EC2C2C28B500234BEB /* x86_64 */,
				184DB2ED2C2C28B500234BEB /* adler32.h */,
				184DB2EE2C2C28B500234BEB /* aes_ctr.h */,
				184DB2EF2C2C28B500234BEB /* aes.h */,
				184DB2F02C2C28B500234BEB /* application.h */,
				184DB2F12C2C28B500234BEB /* attributes.h */,
				184DB2F22C2C28B500234BEB /* audio_fifo.h */,
				184DB2F32C2C28B500234BEB /* avassert.h */,
				184DB2F42C2C28B500234BEB /* avconfig.h */,
				184DB2F52C2C28B500234BEB /* avstring.h */,
				184DB2F62C2C28B500234BEB /* avutil.h */,
				184DB2F72C2C28B500234BEB /* base64.h */,
				184DB2F82C2C28B500234BEB /* blowfish.h */,
				184DB2F92C2C28B500234BEB /* bprint.h */,
				184DB2FA2C2C28B500234BEB /* bswap.h */,
				184DB2FB2C2C28B500234BEB /* buffer.h */,
				184DB2FC2C2C28B500234BEB /* camellia.h */,
				184DB2FD2C2C28B500234BEB /* cast5.h */,
				184DB2FE2C2C28B500234BEB /* channel_layout.h */,
				184DB2FF2C2C28B500234BEB /* common.h */,
				184DB3002C2C28B500234BEB /* cpu.h */,
				184DB3012C2C28B500234BEB /* crc.h */,
				184DB3022C2C28B500234BEB /* des.h */,
				184DB3032C2C28B500234BEB /* dict.h */,
				184DB3042C2C28B500234BEB /* display.h */,
				184DB3052C2C28B500234BEB /* dns_cache.h */,
				184DB3062C2C28B500234BEB /* downmix_info.h */,
				184DB3072C2C28B500234BEB /* encryption_info.h */,
				184DB3082C2C28B500234BEB /* error.h */,
				184DB3092C2C28B500234BEB /* eval.h */,
				184DB30A2C2C28B500234BEB /* ffversion.h */,
				184DB30B2C2C28B500234BEB /* fifo.h */,
				184DB30C2C2C28B500234BEB /* file.h */,
				184DB30D2C2C28B500234BEB /* frame.h */,
				184DB30E2C2C28B500234BEB /* hash.h */,
				184DB30F2C2C28B500234BEB /* hmac.h */,
				184DB3102C2C28B500234BEB /* hwcontext_cuda.h */,
				184DB3112C2C28B500234BEB /* hwcontext_d3d11va.h */,
				184DB3122C2C28B500234BEB /* hwcontext_drm.h */,
				184DB3132C2C28B500234BEB /* hwcontext_dxva2.h */,
				184DB3142C2C28B500234BEB /* hwcontext_mediacodec.h */,
				184DB3152C2C28B500234BEB /* hwcontext_qsv.h */,
				184DB3162C2C28B500234BEB /* hwcontext_vaapi.h */,
				184DB3172C2C28B500234BEB /* hwcontext_vdpau.h */,
				184DB3182C2C28B500234BEB /* hwcontext_videotoolbox.h */,
				184DB3192C2C28B500234BEB /* hwcontext.h */,
				184DB31A2C2C28B500234BEB /* imgutils.h */,
				184DB31B2C2C28B500234BEB /* intfloat.h */,
				184DB31C2C2C28B500234BEB /* intreadwrite.h */,
				184DB31D2C2C28B500234BEB /* lfg.h */,
				184DB31E2C2C28B500234BEB /* log.h */,
				184DB31F2C2C28B500234BEB /* macros.h */,
				184DB3202C2C28B500234BEB /* mastering_display_metadata.h */,
				184DB3212C2C28B500234BEB /* mathematics.h */,
				184DB3222C2C28B500234BEB /* md5.h */,
				184DB3232C2C28B500234BEB /* mem.h */,
				184DB3242C2C28B500234BEB /* motion_vector.h */,
				184DB3252C2C28B500234BEB /* murmur3.h */,
				184DB3262C2C28B500234BEB /* opt.h */,
				184DB3272C2C28B500234BEB /* parseutils.h */,
				184DB3282C2C28B500234BEB /* pixdesc.h */,
				184DB3292C2C28B500234BEB /* pixelutils.h */,
				184DB32A2C2C28B500234BEB /* pixfmt.h */,
				184DB32B2C2C28B500234BEB /* random_seed.h */,
				184DB32C2C2C28B500234BEB /* rational.h */,
				184DB32D2C2C28B500234BEB /* rc4.h */,
				184DB32E2C2C28B500234BEB /* replaygain.h */,
				184DB32F2C2C28B500234BEB /* ripemd.h */,
				184DB3302C2C28B500234BEB /* samplefmt.h */,
				184DB3312C2C28B500234BEB /* sha.h */,
				184DB3322C2C28B500234BEB /* sha512.h */,
				184DB3332C2C28B500234BEB /* spherical.h */,
				184DB3342C2C28B500234BEB /* stereo3d.h */,
				184DB3352C2C28B500234BEB /* tea.h */,
				184DB3362C2C28B500234BEB /* thread.h */,
				184DB3372C2C28B500234BEB /* threadmessage.h */,
				184DB3382C2C28B500234BEB /* time.h */,
				184DB3392C2C28B500234BEB /* timecode.h */,
				184DB33A2C2C28B500234BEB /* timestamp.h */,
				184DB33B2C2C28B500234BEB /* tree.h */,
				184DB33C2C2C28B500234BEB /* twofish.h */,
				184DB33D2C2C28B500234BEB /* version.h */,
				184DB33E2C2C28B500234BEB /* xtea.h */,
			);
			path = libavutil;
			sourceTree = "<group>";
		};
		184DB3412C2C28B500234BEB /* arm64 */ = {
			isa = PBXGroup;
			children = (
				184DB3402C2C28B500234BEB /* config.h */,
			);
			path = arm64;
			sourceTree = "<group>";
		};
		184DB3432C2C28B500234BEB /* armv7 */ = {
			isa = PBXGroup;
			children = (
				184DB3422C2C28B500234BEB /* config.h */,
			);
			path = armv7;
			sourceTree = "<group>";
		};
		184DB3452C2C28B500234BEB /* i386 */ = {
			isa = PBXGroup;
			children = (
				184DB3442C2C28B500234BEB /* config.h */,
			);
			path = i386;
			sourceTree = "<group>";
		};
		184DB3472C2C28B500234BEB /* x86_64 */ = {
			isa = PBXGroup;
			children = (
				184DB3462C2C28B500234BEB /* config.h */,
			);
			path = x86_64;
			sourceTree = "<group>";
		};
		184DB3492C2C28B500234BEB /* libffmpeg */ = {
			isa = PBXGroup;
			children = (
				184DB3412C2C28B500234BEB /* arm64 */,
				184DB3432C2C28B500234BEB /* armv7 */,
				184DB3452C2C28B500234BEB /* i386 */,
				184DB3472C2C28B500234BEB /* x86_64 */,
				184DB3482C2C28B500234BEB /* config.h */,
			);
			path = libffmpeg;
			sourceTree = "<group>";
		};
		184DB34C2C2C28B500234BEB /* libswresample */ = {
			isa = PBXGroup;
			children = (
				184DB34A2C2C28B500234BEB /* swresample.h */,
				184DB34B2C2C28B500234BEB /* version.h */,
			);
			path = libswresample;
			sourceTree = "<group>";
		};
		184DB34F2C2C28B500234BEB /* libswscale */ = {
			isa = PBXGroup;
			children = (
				184DB34D2C2C28B500234BEB /* swscale.h */,
				184DB34E2C2C28B500234BEB /* version.h */,
			);
			path = libswscale;
			sourceTree = "<group>";
		};
		184DB3502C2C28B500234BEB /* ffmpeg */ = {
			isa = PBXGroup;
			children = (
				184DB2CE2C2C28B500234BEB /* libavcodec */,
				184DB2D42C2C28B500234BEB /* libavfilter */,
				184DB2E02C2C28B500234BEB /* libavformat */,
				184DB33F2C2C28B500234BEB /* libavutil */,
				184DB3492C2C28B500234BEB /* libffmpeg */,
				184DB34C2C2C28B500234BEB /* libswresample */,
				184DB34F2C2C28B500234BEB /* libswscale */,
			);
			path = ffmpeg;
			sourceTree = "<group>";
		};
		184DB39C2C2C28B500234BEB /* openssl */ = {
			isa = PBXGroup;
			children = (
				184DB3512C2C28B500234BEB /* aes.h */,
				184DB3522C2C28B500234BEB /* asn1_mac.h */,
				184DB3532C2C28B500234BEB /* asn1.h */,
				184DB3542C2C28B500234BEB /* asn1t.h */,
				184DB3552C2C28B500234BEB /* bio.h */,
				184DB3562C2C28B500234BEB /* blowfish.h */,
				184DB3572C2C28B500234BEB /* bn.h */,
				184DB3582C2C28B500234BEB /* buffer.h */,
				184DB3592C2C28B500234BEB /* camellia.h */,
				184DB35A2C2C28B500234BEB /* cast.h */,
				184DB35B2C2C28B500234BEB /* cmac.h */,
				184DB35C2C2C28B500234BEB /* cms.h */,
				184DB35D2C2C28B500234BEB /* comp.h */,
				184DB35E2C2C28B500234BEB /* conf_api.h */,
				184DB35F2C2C28B500234BEB /* conf.h */,
				184DB3602C2C28B500234BEB /* crypto.h */,
				184DB3612C2C28B500234BEB /* des_old.h */,
				184DB3622C2C28B500234BEB /* des.h */,
				184DB3632C2C28B500234BEB /* dh.h */,
				184DB3642C2C28B500234BEB /* dsa.h */,
				184DB3652C2C28B500234BEB /* dso.h */,
				184DB3662C2C28B500234BEB /* dtls1.h */,
				184DB3672C2C28B500234BEB /* e_os2.h */,
				184DB3682C2C28B500234BEB /* ebcdic.h */,
				184DB3692C2C28B500234BEB /* ec.h */,
				184DB36A2C2C28B500234BEB /* ecdh.h */,
				184DB36B2C2C28B500234BEB /* ecdsa.h */,
				184DB36C2C2C28B500234BEB /* engine.h */,
				184DB36D2C2C28B500234BEB /* err.h */,
				184DB36E2C2C28B500234BEB /* evp.h */,
				184DB36F2C2C28B500234BEB /* hmac.h */,
				184DB3702C2C28B500234BEB /* idea.h */,
				184DB3712C2C28B500234BEB /* krb5_asn.h */,
				184DB3722C2C28B500234BEB /* kssl.h */,
				184DB3732C2C28B500234BEB /* lhash.h */,
				184DB3742C2C28B500234BEB /* md4.h */,
				184DB3752C2C28B500234BEB /* md5.h */,
				184DB3762C2C28B500234BEB /* mdc2.h */,
				184DB3772C2C28B500234BEB /* modes.h */,
				184DB3782C2C28B500234BEB /* obj_mac.h */,
				184DB3792C2C28B500234BEB /* objects.h */,
				184DB37A2C2C28B500234BEB /* ocsp.h */,
				184DB37B2C2C28B500234BEB /* opensslconf.h */,
				184DB37C2C2C28B500234BEB /* opensslv.h */,
				184DB37D2C2C28B500234BEB /* ossl_typ.h */,
				184DB37E2C2C28B500234BEB /* pem.h */,
				184DB37F2C2C28B500234BEB /* pem2.h */,
				184DB3802C2C28B500234BEB /* pkcs7.h */,
				184DB3812C2C28B500234BEB /* pkcs12.h */,
				184DB3822C2C28B500234BEB /* pqueue.h */,
				184DB3832C2C28B500234BEB /* rand.h */,
				184DB3842C2C28B500234BEB /* rc2.h */,
				184DB3852C2C28B500234BEB /* rc4.h */,
				184DB3862C2C28B500234BEB /* ripemd.h */,
				184DB3872C2C28B500234BEB /* rsa.h */,
				184DB3882C2C28B500234BEB /* safestack.h */,
				184DB3892C2C28B500234BEB /* seed.h */,
				184DB38A2C2C28B500234BEB /* sha.h */,
				184DB38B2C2C28B500234BEB /* srp.h */,
				184DB38C2C2C28B500234BEB /* srtp.h */,
				184DB38D2C2C28B500234BEB /* ssl.h */,
				184DB38E2C2C28B500234BEB /* ssl2.h */,
				184DB38F2C2C28B500234BEB /* ssl3.h */,
				184DB3902C2C28B500234BEB /* ssl23.h */,
				184DB3912C2C28B500234BEB /* stack.h */,
				184DB3922C2C28B500234BEB /* symhacks.h */,
				184DB3932C2C28B500234BEB /* tls1.h */,
				184DB3942C2C28B500234BEB /* ts.h */,
				184DB3952C2C28B500234BEB /* txt_db.h */,
				184DB3962C2C28B500234BEB /* ui_compat.h */,
				184DB3972C2C28B500234BEB /* ui.h */,
				184DB3982C2C28B500234BEB /* whrlpool.h */,
				184DB3992C2C28B500234BEB /* x509_vfy.h */,
				184DB39A2C2C28B500234BEB /* x509.h */,
				184DB39B2C2C28B500234BEB /* x509v3.h */,
			);
			path = openssl;
			sourceTree = "<group>";
		};
		184DB39D2C2C28B500234BEB /* include */ = {
			isa = PBXGroup;
			children = (
				184DB3502C2C28B500234BEB /* ffmpeg */,
				184DB39C2C2C28B500234BEB /* openssl */,
			);
			path = include;
			sourceTree = "<group>";
		};
		184DB3A42C2C28B500234BEB /* ffmpeg */ = {
			isa = PBXGroup;
			children = (
				184DB39E2C2C28B500234BEB /* libavcodec.a */,
				184DB39F2C2C28B500234BEB /* libavfilter.a */,
				184DB3A02C2C28B500234BEB /* libavformat.a */,
				184DB3A12C2C28B500234BEB /* libavutil.a */,
				184DB3A22C2C28B500234BEB /* libswresample.a */,
				184DB3A32C2C28B500234BEB /* libswscale.a */,
			);
			path = ffmpeg;
			sourceTree = "<group>";
		};
		184DB3A72C2C28B500234BEB /* openssl */ = {
			isa = PBXGroup;
			children = (
				184DB3A52C2C28B500234BEB /* libcrypto.a */,
				184DB3A62C2C28B500234BEB /* libssl.a */,
			);
			path = openssl;
			sourceTree = "<group>";
		};
		184DB3A82C2C28B500234BEB /* lib */ = {
			isa = PBXGroup;
			children = (
				184DB3A42C2C28B500234BEB /* ffmpeg */,
				184DB3A72C2C28B500234BEB /* openssl */,
			);
			path = lib;
			sourceTree = "<group>";
		};
		184DB3A92C2C28B500234BEB /* IVFFmpeg */ = {
			isa = PBXGroup;
			children = (
				184DB39D2C2C28B500234BEB /* include */,
				184DB3A82C2C28B500234BEB /* lib */,
			);
			path = IVFFmpeg;
			sourceTree = "<group>";
		};
		184DB3AD2C2C28B500234BEB /* IVP2P */ = {
			isa = PBXGroup;
			children = (
				184DB3AA2C2C28B500234BEB /* libiot_video_p2p.a */,
				184DB3AB2C2C28B500234BEB /* liblibevent.a */,
				184DB3AC2C2C28B500234BEB /* libmbedtls1.a */,
			);
			path = IVP2P;
			sourceTree = "<group>";
		};
		184DB3B12C2C28B500234BEB /* IVVASLib */ = {
			isa = PBXGroup;
			children = (
				18ECA7062C89B4F600319EE9 /* IVVAS */,
				184DB3AF2C2C28B500234BEB /* IVVAS.h */,
			);
			path = IVVASLib;
			sourceTree = "<group>";
		};
		184DB3B32C2C28B500234BEB /* IotVideo */ = {
			isa = PBXGroup;
			children = (
				184DB2BA2C2C28B500234BEB /* IoTLib */,
				184DB3A92C2C28B500234BEB /* IVFFmpeg */,
				184DB3AD2C2C28B500234BEB /* IVP2P */,
				184DB3B12C2C28B500234BEB /* IVVASLib */,
				184DB3B22C2C28B500234BEB /* 替换SDK需替换所有文件 */,
			);
			path = IotVideo;
			sourceTree = "<group>";
		};
		184DB3BB2C2C28B500234BEB /* Api */ = {
			isa = PBXGroup;
			children = (
				18FA8D542C5CD6D000DA5EC5 /* NIotConnectionPreparer.swift */,
				18FA8D532C5CD6D000DA5EC5 /* NIotOTAManager.swift */,
				184DB3B52C2C28B500234BEB /* NIotManager.swift */,
				184DB3B82C2C28B500234BEB /* NIotNetworking.swift */,
				184DB5EC2C2D604500234BEB /* NIotConnection.swift */,
				184DB5EB2C2D604500234BEB /* NIotMonitorPlayer.swift */,
				184DB5EE2C2D604500234BEB /* NIotPlaybackPlayer.swift */,
				18FC86A42C3680D900AA1F14 /* NIotFileDownloader.swift */,
				184DB5ED2C2D604500234BEB /* NIotPlayer.swift */,
				184DB3DE2C2C28B500234BEB /* NIotSignalMessager.swift */,
			);
			path = Api;
			sourceTree = "<group>";
		};
		184DB3C02C2C28B500234BEB /* Extension */ = {
			isa = PBXGroup;
			children = (
				184DB3BC2C2C28B500234BEB /* Foundation.swift */,
				184DB3BD2C2C28B500234BEB /* Notifications+Names.swift */,
				184DB3BE2C2C28B500234BEB /* String+AES.swift */,
				184DB3BF2C2C28B500234BEB /* String+Extension.swift */,
			);
			path = Extension;
			sourceTree = "<group>";
		};
		184DB3C22C2C28B500234BEB /* DistributionNet（配网） */ = {
			isa = PBXGroup;
			children = (
				184DB3C12C2C28B500234BEB /* DistributionNetService.swift */,
			);
			path = "DistributionNet（配网）";
			sourceTree = "<group>";
		};
		184DB3C72C2C28B500234BEB /* Model（物模型） */ = {
			isa = PBXGroup;
			children = (
				184DB3C32C2C28B500234BEB /* PropertyModel_v1.swift */,
				184DB3C42C2C28B500234BEB /* PropertyModel_v2.swift */,
				184DB3C52C2C28B500234BEB /* PropertyModel.swift */,
				184DB3C62C2C28B500234BEB /* PropertyModelPath.swift */,
			);
			path = "Model（物模型）";
			sourceTree = "<group>";
		};
		184DB3CA2C2C28B500234BEB /* MonitorPlayer */ = {
			isa = PBXGroup;
			children = (
				184DB3C82C2C28B500234BEB /* IoTMonitorPlayer.swift */,
				184DB3C92C2C28B500234BEB /* IVMonitorPlayer+IoT.swift */,
			);
			path = MonitorPlayer;
			sourceTree = "<group>";
		};
		184DB3CF2C2C28B500234BEB /* PlaybackPlayer */ = {
			isa = PBXGroup;
			children = (
				184DB3CB2C2C28B500234BEB /* IoTFileDownloader.swift */,
				184DB3CC2C2C28B500234BEB /* IoTPlaybackPlayer.swift */,
				184DB3CD2C2C28B500234BEB /* IVPlaybackPlayer_v2.swift */,
				184DB3CE2C2C28B500234BEB /* IVPlaybackPlayer+IoT.swift */,
			);
			path = PlaybackPlayer;
			sourceTree = "<group>";
		};
		184DB3D12C2C28B500234BEB /* IVPlayer_v2 */ = {
			isa = PBXGroup;
			children = (
				184DB3D02C2C28B500234BEB /* IoTExploreOrVideoDeviceModel.swift */,
			);
			path = IVPlayer_v2;
			sourceTree = "<group>";
		};
		184DB3D62C2C28B500234BEB /* Player */ = {
			isa = PBXGroup;
			children = (
				184DB3D12C2C28B500234BEB /* IVPlayer_v2 */,
				184DB3D22C2C28B500234BEB /* IoTConnection.swift */,
				184DB3D32C2C28B500234BEB /* IoTPlayer.swift */,
				184DB3D42C2C28B500234BEB /* IVPlayer_v2.swift */,
				184DB3D52C2C28B500234BEB /* IVPlayer+IoT.swift */,
			);
			path = Player;
			sourceTree = "<group>";
		};
		184DB3DF2C2C28B500234BEB /* IoT */ = {
			isa = PBXGroup;
			children = (
				184DB3C22C2C28B500234BEB /* DistributionNet（配网） */,
				184DB3C72C2C28B500234BEB /* Model（物模型） */,
				184DB3CA2C2C28B500234BEB /* MonitorPlayer */,
				184DB3CF2C2C28B500234BEB /* PlaybackPlayer */,
				184DB3D62C2C28B500234BEB /* Player */,
				184DB3D72C2C28B500234BEB /* IoTPlaybackPage.swift */,
				184DB3D82C2C28B500234BEB /* IoTPlaybackService_v1.swift */,
				184DB3D92C2C28B500234BEB /* IoTPlaybackService_v2.swift */,
				184DB3DA2C2C28B500234BEB /* IoTPlaybackService.swift */,
				184DB3DB2C2C28B500234BEB /* IoTService_v1.swift */,
				184DB3DC2C2C28B500234BEB /* IoTService_v2.swift */,
				184DB3DD2C2C28B500234BEB /* IoTService.swift */,
			);
			path = IoT;
			sourceTree = "<group>";
		};
		184DB3E22C2C28B500234BEB /* Model */ = {
			isa = PBXGroup;
			children = (
				184DB3E02C2C28B500234BEB /* NIotPlaybackItem.swift */,
				184DB3E12C2C28B500234BEB /* NVDeviceModel.swift */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		184DB3E82C2C28B500234BEB /* Networking */ = {
			isa = PBXGroup;
			children = (
				184DB3E32C2C28B500234BEB /* EWKeychain.swift */,
				18FC86A22C2EC9E100AA1F14 /* MQTTService.swift */,
				184DB3E42C2C28B500234BEB /* NVAPI.swift */,
				184DB3E52C2C28B500234BEB /* NVM3U8NetworkManager.swift */,
				184DB3E62C2C28B500234BEB /* NVNetworkManager.swift */,
				184DB3E72C2C28B500234BEB /* Response.swift */,
			);
			path = Networking;
			sourceTree = "<group>";
		};
		184DB3EF2C2C28B500234BEB /* Utils */ = {
			isa = PBXGroup;
			children = (
				18AB14472D967F9500B98B31 /* XP2PDelegateForwarder.swift */,
				184DB3E92C2C28B500234BEB /* NVEnvironment.swift */,
				184DB3EA2C2C28B500234BEB /* NVJson.swift */,
				184DB3EB2C2C28B500234BEB /* NVKeychain.swift */,
				184DB3EC2C2C28B500234BEB /* NVLog.swift */,
				184DB3ED2C2C28B500234BEB /* NVTencentModelCommand.swift */,
				184DB3EE2C2C28B500234BEB /* NVUtils.swift */,
			);
			path = Utils;
			sourceTree = "<group>";
		};
		184DB3F02C2C28B500234BEB /* Classes */ = {
			isa = PBXGroup;
			children = (
				184DB3BB2C2C28B500234BEB /* Api */,
				184DB3C02C2C28B500234BEB /* Extension */,
				184DB3DF2C2C28B500234BEB /* IoT */,
				184DB3E22C2C28B500234BEB /* Model */,
				184DB3E82C2C28B500234BEB /* Networking */,
				184DB3EF2C2C28B500234BEB /* Utils */,
			);
			path = Classes;
			sourceTree = "<group>";
		};
		184DB43F2C2C28B500234BEB /* Headers */ = {
			isa = PBXGroup;
			children = (
				184DB3F22C2C28B500234BEB /* BluetoothCentralManager.h */,
				184DB3F32C2C28B500234BEB /* CMPageContentView.h */,
				184DB3F42C2C28B500234BEB /* CMPageTitleConfig.h */,
				184DB3F52C2C28B500234BEB /* CMPageTitleContentView.h */,
				184DB3F62C2C28B500234BEB /* CMPageTitleView.h */,
				184DB3F72C2C28B500234BEB /* CMPageTitleViewMacro.h */,
				184DB3F82C2C28B500234BEB /* ESP_ByteUtil.h */,
				184DB3F92C2C28B500234BEB /* ESP_CRC8.h */,
				184DB3FA2C2C28B500234BEB /* ESP_NetUtil.h */,
				184DB3FB2C2C28B500234BEB /* ESP_WifiUtil.h */,
				184DB3FC2C2C28B500234BEB /* ESPAES.h */,
				184DB3FD2C2C28B500234BEB /* ESPDataCode.h */,
				184DB3FE2C2C28B500234BEB /* ESPDatumCode.h */,
				184DB3FF2C2C28B500234BEB /* ESPGuideCode.h */,
				184DB4002C2C28B500234BEB /* ESPTools.h */,
				184DB4012C2C28B500234BEB /* ESPTouchDelegate.h */,
				184DB4022C2C28B500234BEB /* ESPTouchGenerator.h */,
				184DB4032C2C28B500234BEB /* ESPTouchResult.h */,
				184DB4042C2C28B500234BEB /* ESPTouchTask.h */,
				184DB4052C2C28B500234BEB /* ESPTouchTaskParameter.h */,
				184DB4062C2C28B500234BEB /* ESPUDPSocketClient.h */,
				184DB4072C2C28B500234BEB /* ESPUDPSocketServer.h */,
				184DB4082C2C28B500234BEB /* ESPVersionMacro.h */,
				184DB4092C2C28B500234BEB /* HXYNotice.h */,
				184DB40A2C2C28B500234BEB /* MBProgressHUD+XDP.h */,
				184DB40B2C2C28B500234BEB /* NSObject+additions.h */,
				184DB40C2C2C28B500234BEB /* NSString+Extension.h */,
				184DB40D2C2C28B500234BEB /* route.h */,
				184DB40E2C2C28B500234BEB /* TCSocket.h */,
				184DB40F2C2C28B500234BEB /* TIoTCodeAddress.h */,
				184DB4102C2C28B500234BEB /* TIoTCoreAccountSet.h */,
				184DB4112C2C28B500234BEB /* TIoTCoreAddDevice.h */,
				184DB4122C2C28B500234BEB /* TIoTCoreAPISets.h */,
				184DB4132C2C28B500234BEB /* TIoTCoreAppEnvironment.h */,
				184DB4142C2C28B500234BEB /* TIoTCoreDeviceCenter.h */,
				184DB4152C2C28B500234BEB /* TIoTCoreDeviceSet.h */,
				184DB4162C2C28B500234BEB /* TIoTCoreFamilySet.h */,
				184DB4172C2C28B500234BEB /* TIoTCoreFoundation.h */,
				184DB4182C2C28B500234BEB /* TIoTCoreHudLoadingView.h */,
				184DB4192C2C28B500234BEB /* TIoTCoreLogReport.h */,
				184DB41A2C2C28B500234BEB /* TIoTCoreMessageSet.h */,
				184DB41B2C2C28B500234BEB /* TIoTCoreObject.h */,
				184DB41C2C2C28B500234BEB /* TIoTCoreParts.h */,
				184DB41D2C2C28B500234BEB /* TIoTCoreQMacros.h */,
				184DB41E2C2C28B500234BEB /* TIoTCoreRequestAction.h */,
				184DB41F2C2C28B500234BEB /* TIoTCoreRequestBuilder.h */,
				184DB4202C2C28B500234BEB /* TIoTCoreRequestClient.h */,
				184DB4212C2C28B500234BEB /* TIoTCoreRequestObj.h */,
				184DB4222C2C28B500234BEB /* TIoTCoreRequestObject.h */,
				184DB4232C2C28B500234BEB /* TIoTCoreServices.h */,
				184DB4242C2C28B500234BEB /* TIoTCoreSocketCover.h */,
				184DB4252C2C28B500234BEB /* TIoTCoreSocketManager.h */,
				184DB4262C2C28B500234BEB /* TIoTCoreUserManage.h */,
				184DB4272C2C28B500234BEB /* TIoTCoreUtil.h */,
				184DB4282C2C28B500234BEB /* TIoTCoreWebSocket.h */,
				184DB4292C2C28B500234BEB /* TIoTCoreWMacros.h */,
				184DB42A2C2C28B500234BEB /* TIoTDataTracking.h */,
				184DB42B2C2C28B500234BEB /* TIoTGetgateway.h */,
				184DB42C2C2C28B500234BEB /* TIoTLinkKit-umbrella.h */,
				184DB42D2C2C28B500234BEB /* TIoTPrintLogFileManager.h */,
				184DB42E2C2C28B500234BEB /* TIoTPrintLogFormatter.h */,
				184DB42F2C2C28B500234BEB /* TIoTPrintLogManager.h */,
				184DB4302C2C28B500234BEB /* TIoTRouter.h */,
				184DB4312C2C28B500234BEB /* TIotSoftApUdpSocketUtil.h */,
				184DB4322C2C28B500234BEB /* TIOTTRTCModel.h */,
				184DB4332C2C28B500234BEB /* TIoTTRTCSessionManager.h */,
				184DB4342C2C28B500234BEB /* TIoTVideoDistributionNetModel.h */,
				184DB4352C2C28B500234BEB /* TRTCCalling.h */,
				184DB4362C2C28B500234BEB /* TRTCCalling+Signal.h */,
				184DB4372C2C28B500234BEB /* TRTCCallingDelegate.h */,
				184DB4382C2C28B500234BEB /* TRTCCallingHeader.h */,
				184DB4392C2C28B500234BEB /* TRTCCallingModel.h */,
				184DB43A2C2C28B500234BEB /* TRTCCallingUtils.h */,
				184DB43B2C2C28B500234BEB /* UIColor+Color.h */,
				184DB43C2C2C28B500234BEB /* UIDevice+Until.h */,
				184DB43D2C2C28B500234BEB /* UIFont+TIoTFont.h */,
				184DB43E2C2C28B500234BEB /* UIView+CMCommon.h */,
			);
			path = Headers;
			sourceTree = "<group>";
		};
		184DB4412C2C28B500234BEB /* TIoTLinkKit */ = {
			isa = PBXGroup;
			children = (
				184DB43F2C2C28B500234BEB /* Headers */,
				184DB4402C2C28B500234BEB /* TIoTLinkKit */,
			);
			path = TIoTLinkKit;
			sourceTree = "<group>";
		};
		187BA4C82C2BEC5D00AC4A65 = {
			isa = PBXGroup;
			children = (
				187BA4D42C2BEC5D00AC4A65 /* NiViewIoT */,
				187BA4D32C2BEC5D00AC4A65 /* Products */,
				0817359AA7EDD734220B4A0F /* Pods */,
				206C4803E128995439E1C3BF /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		187BA4D32C2BEC5D00AC4A65 /* Products */ = {
			isa = PBXGroup;
			children = (
				187BA4D22C2BEC5D00AC4A65 /* NiViewIoT.framework */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		187BA4D42C2BEC5D00AC4A65 /* NiViewIoT */ = {
			isa = PBXGroup;
			children = (
				184DB4422C2C28B500234BEB /* NiViewIoT.modulemap */,
				184DB3F12C2C28B500234BEB /* 打包注意.md */,
				184DB3F02C2C28B500234BEB /* Classes */,
				184DB3B32C2C28B500234BEB /* IotVideo */,
				184DB4412C2C28B500234BEB /* TIoTLinkKit */,
				187BA4D52C2BEC5D00AC4A65 /* NiViewIoT.h */,
				187BA4D62C2BEC5D00AC4A65 /* NiViewIoT.docc */,
			);
			path = NiViewIoT;
			sourceTree = "<group>";
		};
		206C4803E128995439E1C3BF /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				469254C27C7C6F9C50C95899 /* Pods_NiViewIoT.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		187BA4CD2C2BEC5D00AC4A65 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				184DB45C2C2C28B500234BEB /* IVLog.h in Headers */,
				184DB4FE2C2C28B500234BEB /* version.h in Headers */,
				184DB49E2C2C28B500234BEB /* avconfig.h in Headers */,
				184DB5462C2C28B500234BEB /* whrlpool.h in Headers */,
				184DB5142C2C28B500234BEB /* dtls1.h in Headers */,
				184DB5082C2C28B500234BEB /* cast.h in Headers */,
				184DB5282C2C28B500234BEB /* ocsp.h in Headers */,
				184DB58F2C2C28B600234BEB /* ESP_ByteUtil.h in Headers */,
				184DB5A42C2C28B600234BEB /* route.h in Headers */,
				184DB4EE2C2C28B500234BEB /* threadmessage.h in Headers */,
				184DB4DA2C2C28B500234BEB /* mem.h in Headers */,
				184DB5172C2C28B500234BEB /* ec.h in Headers */,
				184DB4FA2C2C28B500234BEB /* config.h in Headers */,
				184DB4A12C2C28B500234BEB /* ffversion.h in Headers */,
				184DB4CF2C2C28B500234BEB /* hwcontext_videotoolbox.h in Headers */,
				184DB4B02C2C28B500234BEB /* bprint.h in Headers */,
				184DB4A72C2C28B500234BEB /* application.h in Headers */,
				184DB5C12C2C28B600234BEB /* TIoTDataTracking.h in Headers */,
				184DB5CC2C2C28B600234BEB /* TRTCCalling.h in Headers */,
				184DB4FB2C2C28B500234BEB /* swresample.h in Headers */,
				184DB5B42C2C28B600234BEB /* TIoTCoreQMacros.h in Headers */,
				184DB4902C2C28B500234BEB /* version.h in Headers */,
				184DB4832C2C28B500234BEB /* mediacodec.h in Headers */,
				184DB4AC2C2C28B500234BEB /* avstring.h in Headers */,
				184DB5B72C2C28B600234BEB /* TIoTCoreRequestClient.h in Headers */,
				184DB46C2C2C28B500234BEB /* IVTimer.h in Headers */,
				184DB5D52C2C28B600234BEB /* UIView+CMCommon.h in Headers */,
				184DB5D12C2C28B600234BEB /* TRTCCallingUtils.h in Headers */,
				184DB5CF2C2C28B600234BEB /* TRTCCallingHeader.h in Headers */,
				184DB4572C2C28B500234BEB /* IVDeviceMgr.h in Headers */,
				184DB47E2C2C28B500234BEB /* d3d11va.h in Headers */,
				184DB4A62C2C28B500234BEB /* aes.h in Headers */,
				184DB4B12C2C28B500234BEB /* bswap.h in Headers */,
				184DB4772C2C28B500234BEB /* IVVideoRender.h in Headers */,
				184DB5012C2C28B500234BEB /* asn1.h in Headers */,
				184DB4A32C2C28B500234BEB /* ffversion.h in Headers */,
				184DB5292C2C28B500234BEB /* opensslconf.h in Headers */,
				184DB5382C2C28B500234BEB /* sha.h in Headers */,
				184DB5D32C2C28B600234BEB /* UIDevice+Until.h in Headers */,
				184DB5A32C2C28B600234BEB /* NSString+Extension.h in Headers */,
				184DB48D2C2C28B500234BEB /* avfiltergraph.h in Headers */,
				184DB5B32C2C28B600234BEB /* TIoTCoreParts.h in Headers */,
				184DB4EC2C2C28B500234BEB /* tea.h in Headers */,
				184DB44D2C2C28B500234BEB /* IVAVDefine.h in Headers */,
				184DB4CE2C2C28B500234BEB /* hwcontext_vdpau.h in Headers */,
				184DB5242C2C28B500234BEB /* mdc2.h in Headers */,
				184DB49B2C2C28B500234BEB /* version.h in Headers */,
				184DB5B52C2C28B600234BEB /* TIoTCoreRequestAction.h in Headers */,
				184DB4A22C2C28B500234BEB /* avconfig.h in Headers */,
				184DB4C42C2C28B500234BEB /* frame.h in Headers */,
				184DB58A2C2C28B600234BEB /* CMPageContentView.h in Headers */,
				184DB58B2C2C28B600234BEB /* CMPageTitleConfig.h in Headers */,
				184DB4BB2C2C28B500234BEB /* display.h in Headers */,
				184DB52E2C2C28B500234BEB /* pkcs7.h in Headers */,
				184DB4952C2C28B500234BEB /* flv.h in Headers */,
				184DB47F2C2C28B500234BEB /* dirac.h in Headers */,
				184DB4932C2C28B500234BEB /* avio_internal.h in Headers */,
				184DB5112C2C28B500234BEB /* dh.h in Headers */,
				184DB59D2C2C28B600234BEB /* ESPUDPSocketClient.h in Headers */,
				184DB51C2C2C28B500234BEB /* evp.h in Headers */,
				184DB4C12C2C28B500234BEB /* ffversion.h in Headers */,
				184DB4B32C2C28B500234BEB /* camellia.h in Headers */,
				184DB59C2C2C28B600234BEB /* ESPTouchTaskParameter.h in Headers */,
				184DB5942C2C28B600234BEB /* ESPDataCode.h in Headers */,
				184DB5CB2C2C28B600234BEB /* TIoTVideoDistributionNetModel.h in Headers */,
				184DB45F2C2C28B500234BEB /* IVMessageMgr.h in Headers */,
				184DB4A82C2C28B500234BEB /* attributes.h in Headers */,
				184DB4BE2C2C28B500234BEB /* encryption_info.h in Headers */,
				184DB4942C2C28B500234BEB /* avio.h in Headers */,
				184DB5352C2C28B500234BEB /* rsa.h in Headers */,
				184DB4E22C2C28B500234BEB /* random_seed.h in Headers */,
				184DB52C2C2C28B500234BEB /* pem.h in Headers */,
				184DB5362C2C28B500234BEB /* safestack.h in Headers */,
				184DB4482C2C28B500234BEB /* IVAudioDecoder.h in Headers */,
				184DB5BC2C2C28B600234BEB /* TIoTCoreSocketManager.h in Headers */,
				184DB5492C2C28B500234BEB /* x509v3.h in Headers */,
				184DB4922C2C28B500234BEB /* avformat.h in Headers */,
				184DB48A2C2C28B500234BEB /* vorbis_parser.h in Headers */,
				184DB5302C2C28B500234BEB /* pqueue.h in Headers */,
				184DB44E2C2C28B500234BEB /* IVAVRecordable.h in Headers */,
				184DB49A2C2C28B500234BEB /* url.h in Headers */,
				184DB4682C2C28B500234BEB /* IVQRCodeHelper.h in Headers */,
				184DB51E2C2C28B500234BEB /* idea.h in Headers */,
				184DB4B72C2C28B500234BEB /* cpu.h in Headers */,
				184DB4452C2C28B500234BEB /* IVAudioCapturable.h in Headers */,
				184DB4F82C2C28B500234BEB /* config.h in Headers */,
				184DB4E52C2C28B500234BEB /* replaygain.h in Headers */,
				184DB5392C2C28B500234BEB /* srp.h in Headers */,
				184DB5322C2C28B500234BEB /* rc2.h in Headers */,
				184DB4E72C2C28B500234BEB /* samplefmt.h in Headers */,
				184DB5C22C2C28B600234BEB /* TIoTGetgateway.h in Headers */,
				184DB5152C2C28B500234BEB /* e_os2.h in Headers */,
				184DB51D2C2C28B500234BEB /* hmac.h in Headers */,
				184DB51A2C2C28B500234BEB /* engine.h in Headers */,
				184DB5C82C2C28B600234BEB /* TIotSoftApUdpSocketUtil.h in Headers */,
				184DB59E2C2C28B600234BEB /* ESPUDPSocketServer.h in Headers */,
				184DB4C82C2C28B500234BEB /* hwcontext_d3d11va.h in Headers */,
				184DB5042C2C28B500234BEB /* blowfish.h in Headers */,
				184DB4CD2C2C28B500234BEB /* hwcontext_vaapi.h in Headers */,
				184DB5912C2C28B600234BEB /* ESP_NetUtil.h in Headers */,
				184DB5B82C2C28B600234BEB /* TIoTCoreRequestObj.h in Headers */,
				184DB4CC2C2C28B500234BEB /* hwcontext_qsv.h in Headers */,
				184DB53D2C2C28B500234BEB /* ssl3.h in Headers */,
				184DB4E92C2C28B500234BEB /* sha512.h in Headers */,
				184DB4ED2C2C28B500234BEB /* thread.h in Headers */,
				184DB5372C2C28B500234BEB /* seed.h in Headers */,
				184DB5B02C2C28B600234BEB /* TIoTCoreLogReport.h in Headers */,
				184DB5442C2C28B500234BEB /* ui_compat.h in Headers */,
				184DB4692C2C28B500234BEB /* IVQRCodeNetConfig.h in Headers */,
				184DB4BA2C2C28B500234BEB /* dict.h in Headers */,
				184DB5A82C2C28B600234BEB /* TIoTCoreAddDevice.h in Headers */,
				184DB5132C2C28B500234BEB /* dso.h in Headers */,
				184DB5A52C2C28B600234BEB /* TCSocket.h in Headers */,
				184DB4A92C2C28B500234BEB /* audio_fifo.h in Headers */,
				184DB5B12C2C28B600234BEB /* TIoTCoreMessageSet.h in Headers */,
				184DB5C92C2C28B600234BEB /* TIOTTRTCModel.h in Headers */,
				184DB48F2C2C28B500234BEB /* buffersrc.h in Headers */,
				184DB5AE2C2C28B600234BEB /* TIoTCoreFoundation.h in Headers */,
				184DB47B2C2C28B500234BEB /* avcodec.h in Headers */,
				184DB4912C2C28B500234BEB /* avc.h in Headers */,
				184DB5CE2C2C28B600234BEB /* TRTCCallingDelegate.h in Headers */,
				184DB5922C2C28B600234BEB /* ESP_WifiUtil.h in Headers */,
				184DB49C2C2C28B500234BEB /* avconfig.h in Headers */,
				184DB5A92C2C28B600234BEB /* TIoTCoreAPISets.h in Headers */,
				184DB51F2C2C28B500234BEB /* krb5_asn.h in Headers */,
				184DB5232C2C28B500234BEB /* md5.h in Headers */,
				184DB50E2C2C28B500234BEB /* crypto.h in Headers */,
				184DB4872C2C28B500234BEB /* vdpau.h in Headers */,
				184DB4A52C2C28B500234BEB /* aes_ctr.h in Headers */,
				184DB4582C2C28B500234BEB /* IVError.h in Headers */,
				184DB4722C2C28B500234BEB /* IVVideoDecodable.h in Headers */,
				184DB4E62C2C28B500234BEB /* ripemd.h in Headers */,
				184DB4B92C2C28B500234BEB /* des.h in Headers */,
				184DB5892C2C28B600234BEB /* BluetoothCentralManager.h in Headers */,
				184DB4BF2C2C28B500234BEB /* error.h in Headers */,
				187BA4D82C2BEC5D00AC4A65 /* NiViewIoT.h in Headers */,
				184DB4AE2C2C28B500234BEB /* base64.h in Headers */,
				184DB49D2C2C28B500234BEB /* ffversion.h in Headers */,
				184DB52B2C2C28B500234BEB /* ossl_typ.h in Headers */,
				184DB4F52C2C28B500234BEB /* xtea.h in Headers */,
				184DB4AD2C2C28B500234BEB /* avutil.h in Headers */,
				184DB5162C2C28B500234BEB /* ebcdic.h in Headers */,
				184DB4D92C2C28B500234BEB /* md5.h in Headers */,
				184DB5222C2C28B500234BEB /* md4.h in Headers */,
				184DB5C62C2C28B600234BEB /* TIoTPrintLogManager.h in Headers */,
				184DB4D12C2C28B500234BEB /* imgutils.h in Headers */,
				184DB5182C2C28B500234BEB /* ecdh.h in Headers */,
				184DB5AD2C2C28B600234BEB /* TIoTCoreFamilySet.h in Headers */,
				184DB44C2C2C28B500234BEB /* IVAudioUnit.h in Headers */,
				184DB5D42C2C28B600234BEB /* UIFont+TIoTFont.h in Headers */,
				184DB4442C2C28B500234BEB /* IoTVideo.h in Headers */,
				184DB47D2C2C28B500234BEB /* avfft.h in Headers */,
				184DB46D2C2C28B500234BEB /* IVTransmission.h in Headers */,
				184DB4C22C2C28B500234BEB /* fifo.h in Headers */,
				184DB5BA2C2C28B600234BEB /* TIoTCoreServices.h in Headers */,
				184DB4822C2C28B500234BEB /* jni.h in Headers */,
				184DB5002C2C28B500234BEB /* asn1_mac.h in Headers */,
				184DB46F2C2C28B500234BEB /* IVUtils.h in Headers */,
				184DB5562C2C28B600234BEB /* IVVAS.h in Headers */,
				184DB5952C2C28B600234BEB /* ESPDatumCode.h in Headers */,
				184DB4782C2C28B500234BEB /* IVVideoRenderable.h in Headers */,
				184DB45A2C2C28B500234BEB /* IVLanNetConfig.h in Headers */,
				184DB48C2C2C28B500234BEB /* avfilter.h in Headers */,
				184DB48E2C2C28B500234BEB /* buffersink.h in Headers */,
				184DB5902C2C28B600234BEB /* ESP_CRC8.h in Headers */,
				184DB53B2C2C28B500234BEB /* ssl.h in Headers */,
				184DB4D62C2C28B500234BEB /* macros.h in Headers */,
				184DB4E32C2C28B500234BEB /* rational.h in Headers */,
				184DB4982C2C28B500234BEB /* metadata.h in Headers */,
				184DB4F22C2C28B500234BEB /* tree.h in Headers */,
				184DB53E2C2C28B500234BEB /* ssl23.h in Headers */,
				184DB5102C2C28B500234BEB /* des.h in Headers */,
				184DB4FC2C2C28B500234BEB /* version.h in Headers */,
				184DB5032C2C28B500234BEB /* bio.h in Headers */,
				184DB4842C2C28B500234BEB /* qsv.h in Headers */,
				184DB5972C2C28B600234BEB /* ESPTools.h in Headers */,
				184DB4D22C2C28B500234BEB /* intfloat.h in Headers */,
				184DB50D2C2C28B500234BEB /* conf.h in Headers */,
				184DB4882C2C28B500234BEB /* version.h in Headers */,
				184DB4642C2C28B500234BEB /* IVNetworkHelper.h in Headers */,
				184DB4D82C2C28B500234BEB /* mathematics.h in Headers */,
				184DB5C72C2C28B600234BEB /* TIoTRouter.h in Headers */,
				184DB4502C2C28B500234BEB /* IVAVUtils.h in Headers */,
				184DB4F42C2C28B500234BEB /* version.h in Headers */,
				184DB5122C2C28B500234BEB /* dsa.h in Headers */,
				184DB5422C2C28B500234BEB /* ts.h in Headers */,
				184DB5262C2C28B500234BEB /* obj_mac.h in Headers */,
				184DB4CA2C2C28B500234BEB /* hwcontext_dxva2.h in Headers */,
				184DB5312C2C28B500234BEB /* rand.h in Headers */,
				184DB5472C2C28B500234BEB /* x509_vfy.h in Headers */,
				184DB4B42C2C28B500234BEB /* cast5.h in Headers */,
				184DB4852C2C28B500234BEB /* vaapi.h in Headers */,
				184DB4AF2C2C28B500234BEB /* blowfish.h in Headers */,
				184DB4702C2C28B500234BEB /* IVVideoCapturable.h in Headers */,
				184DB59B2C2C28B600234BEB /* ESPTouchTask.h in Headers */,
				184DB53C2C2C28B500234BEB /* ssl2.h in Headers */,
				184DB5AB2C2C28B600234BEB /* TIoTCoreDeviceCenter.h in Headers */,
				184DB4AA2C2C28B500234BEB /* avassert.h in Headers */,
				184DB5062C2C28B500234BEB /* buffer.h in Headers */,
				184DB5192C2C28B500234BEB /* ecdsa.h in Headers */,
				184DB4E12C2C28B500234BEB /* pixfmt.h in Headers */,
				184DB4672C2C28B500234BEB /* IVPlayer.h in Headers */,
				184DB4752C2C28B500234BEB /* IVVideoEncoder.h in Headers */,
				184DB5A62C2C28B600234BEB /* TIoTCodeAddress.h in Headers */,
				184DB59F2C2C28B600234BEB /* ESPVersionMacro.h in Headers */,
				184DB4F12C2C28B500234BEB /* timestamp.h in Headers */,
				184DB4C52C2C28B500234BEB /* hash.h in Headers */,
				184DB5402C2C28B500234BEB /* symhacks.h in Headers */,
				184DB53A2C2C28B500234BEB /* srtp.h in Headers */,
				184DB4DC2C2C28B500234BEB /* murmur3.h in Headers */,
				184DB48B2C2C28B500234BEB /* xvmc.h in Headers */,
				184DB5C02C2C28B600234BEB /* TIoTCoreWMacros.h in Headers */,
				184DB5A72C2C28B600234BEB /* TIoTCoreAccountSet.h in Headers */,
				184DB4972C2C28B500234BEB /* internal.h in Headers */,
				184DB52F2C2C28B500234BEB /* pkcs12.h in Headers */,
				184DB5AA2C2C28B600234BEB /* TIoTCoreAppEnvironment.h in Headers */,
				184DB5C32C2C28B600234BEB /* TIoTLinkKit-umbrella.h in Headers */,
				184DB5A02C2C28B600234BEB /* HXYNotice.h in Headers */,
				184DB50A2C2C28B500234BEB /* cms.h in Headers */,
				184DB5072C2C28B500234BEB /* camellia.h in Headers */,
				184DB4742C2C28B500234BEB /* IVVideoEncodable.h in Headers */,
				184DB50C2C2C28B500234BEB /* conf_api.h in Headers */,
				184DB51B2C2C28B500234BEB /* err.h in Headers */,
				184DB5052C2C28B500234BEB /* bn.h in Headers */,
				184DB5272C2C28B500234BEB /* objects.h in Headers */,
				184DB59A2C2C28B600234BEB /* ESPTouchResult.h in Headers */,
				184DB4892C2C28B500234BEB /* videotoolbox.h in Headers */,
				184DB5BD2C2C28B600234BEB /* TIoTCoreUserManage.h in Headers */,
				184DB45B2C2C28B500234BEB /* IVLivePlayer.h in Headers */,
				184DB4602C2C28B500234BEB /* IVMonitorPlayer.h in Headers */,
				184DB5C42C2C28B600234BEB /* TIoTPrintLogFileManager.h in Headers */,
				184DB4812C2C28B500234BEB /* dxva2.h in Headers */,
				184DB4F72C2C28B500234BEB /* config.h in Headers */,
				184DB5092C2C28B500234BEB /* cmac.h in Headers */,
				184DB4BC2C2C28B500234BEB /* dns_cache.h in Headers */,
				184DB5CD2C2C28B600234BEB /* TRTCCalling+Signal.h in Headers */,
				184DB5A22C2C28B600234BEB /* NSObject+additions.h in Headers */,
				184DB4732C2C28B500234BEB /* IVVideoDecoder.h in Headers */,
				184DB5CA2C2C28B600234BEB /* TIoTTRTCSessionManager.h in Headers */,
				184DB58C2C2C28B600234BEB /* CMPageTitleContentView.h in Headers */,
				184DB4FD2C2C28B500234BEB /* swscale.h in Headers */,
				184DB5BE2C2C28B600234BEB /* TIoTCoreUtil.h in Headers */,
				184DB4A42C2C28B500234BEB /* adler32.h in Headers */,
				184DB5022C2C28B500234BEB /* asn1t.h in Headers */,
				184DB5962C2C28B600234BEB /* ESPGuideCode.h in Headers */,
				184DB4E82C2C28B500234BEB /* sha.h in Headers */,
				184DB4712C2C28B500234BEB /* IVVideoCapture.h in Headers */,
				184DB5B22C2C28B600234BEB /* TIoTCoreObject.h in Headers */,
				184DB5D22C2C28B600234BEB /* UIColor+Color.h in Headers */,
				184DB4FF2C2C28B500234BEB /* aes.h in Headers */,
				184DB4EF2C2C28B500234BEB /* time.h in Headers */,
				184DB4862C2C28B500234BEB /* vda.h in Headers */,
				184DB4D42C2C28B500234BEB /* lfg.h in Headers */,
				184DB53F2C2C28B500234BEB /* stack.h in Headers */,
				184DB4C02C2C28B500234BEB /* eval.h in Headers */,
				184DB4E02C2C28B500234BEB /* pixelutils.h in Headers */,
				184DB4552C2C28B500234BEB /* IVConstant.h in Headers */,
				184DB4D32C2C28B500234BEB /* intreadwrite.h in Headers */,
				184DB58D2C2C28B600234BEB /* CMPageTitleView.h in Headers */,
				184DB4EA2C2C28B500234BEB /* spherical.h in Headers */,
				184DB4F32C2C28B500234BEB /* twofish.h in Headers */,
				184DB4492C2C28B500234BEB /* IVAudioEncodable.h in Headers */,
				184DB4E42C2C28B500234BEB /* rc4.h in Headers */,
				184DB45E2C2C28B500234BEB /* IVMacros.h in Headers */,
				184DB4B22C2C28B500234BEB /* buffer.h in Headers */,
				184DB47C2C2C28B500234BEB /* avdct.h in Headers */,
				184DB4BD2C2C28B500234BEB /* downmix_info.h in Headers */,
				184DB5212C2C28B500234BEB /* lhash.h in Headers */,
				184DB4DD2C2C28B500234BEB /* opt.h in Headers */,
				184DB4802C2C28B500234BEB /* dv_profile.h in Headers */,
				184DB4792C2C28B500234BEB /* ac3_parser.h in Headers */,
				184DB4592C2C28B500234BEB /* IVFileDownloader.h in Headers */,
				184DB47A2C2C28B500234BEB /* adts_parser.h in Headers */,
				184DB5B92C2C28B600234BEB /* TIoTCoreRequestObject.h in Headers */,
				184DB4B52C2C28B500234BEB /* channel_layout.h in Headers */,
				184DB46A2C2C28B500234BEB /* IVReachability.h in Headers */,
				184DB4C92C2C28B500234BEB /* hwcontext_drm.h in Headers */,
				184DB5BF2C2C28B600234BEB /* TIoTCoreWebSocket.h in Headers */,
				184DB5BB2C2C28B600234BEB /* TIoTCoreSocketCover.h in Headers */,
				184DB4B62C2C28B500234BEB /* common.h in Headers */,
				184DB5C52C2C28B600234BEB /* TIoTPrintLogFormatter.h in Headers */,
				184DB4DE2C2C28B500234BEB /* parseutils.h in Headers */,
				184DB4EB2C2C28B500234BEB /* stereo3d.h in Headers */,
				184DB4C32C2C28B500234BEB /* file.h in Headers */,
				184DB5252C2C28B500234BEB /* modes.h in Headers */,
				184DB5D02C2C28B600234BEB /* TRTCCallingModel.h in Headers */,
				184DB5452C2C28B500234BEB /* ui.h in Headers */,
				184DB5AF2C2C28B600234BEB /* TIoTCoreHudLoadingView.h in Headers */,
				184DB5412C2C28B500234BEB /* tls1.h in Headers */,
				184DB4632C2C28B500234BEB /* IVNetwork_p2p.h in Headers */,
				184DB5A12C2C28B600234BEB /* MBProgressHUD+XDP.h in Headers */,
				184DB4AB2C2C28B500234BEB /* avconfig.h in Headers */,
				184DB50F2C2C28B500234BEB /* des_old.h in Headers */,
				184DB4652C2C28B500234BEB /* IVNetworkSign.h in Headers */,
				184DB50B2C2C28B500234BEB /* comp.h in Headers */,
				184DB5982C2C28B600234BEB /* ESPTouchDelegate.h in Headers */,
				184DB4542C2C28B500234BEB /* IVConnection.h in Headers */,
				184DB44F2C2C28B500234BEB /* IVAVRecorder.h in Headers */,
				184DB5932C2C28B600234BEB /* ESPAES.h in Headers */,
				184DB44B2C2C28B500234BEB /* IVAudioRenderable.h in Headers */,
				184DB4DF2C2C28B500234BEB /* pixdesc.h in Headers */,
				184DB4F02C2C28B500234BEB /* timecode.h in Headers */,
				184DB4C62C2C28B500234BEB /* hmac.h in Headers */,
				184DB4472C2C28B500234BEB /* IVAudioDecodable.h in Headers */,
				184DB4F62C2C28B500234BEB /* config.h in Headers */,
				184DB5202C2C28B500234BEB /* kssl.h in Headers */,
				184DB44A2C2C28B500234BEB /* IVAudioEncoder.h in Headers */,
				184DB5332C2C28B500234BEB /* rc4.h in Headers */,
				184DB4CB2C2C28B500234BEB /* hwcontext_mediacodec.h in Headers */,
				184DB4D72C2C28B500234BEB /* mastering_display_metadata.h in Headers */,
				184DB5992C2C28B600234BEB /* ESPTouchGenerator.h in Headers */,
				184DB4622C2C28B500234BEB /* IVNetConfig.h in Headers */,
				184DB5342C2C28B500234BEB /* ripemd.h in Headers */,
				184DB4992C2C28B500234BEB /* os_support.h in Headers */,
				184DB4C72C2C28B500234BEB /* hwcontext_cuda.h in Headers */,
				184DB4F92C2C28B500234BEB /* config.h in Headers */,
				184DB4962C2C28B500234BEB /* id3v2.h in Headers */,
				184DB52D2C2C28B500234BEB /* pem2.h in Headers */,
				184DB49F2C2C28B500234BEB /* ffversion.h in Headers */,
				184DB5B62C2C28B600234BEB /* TIoTCoreRequestBuilder.h in Headers */,
				184DB4D52C2C28B500234BEB /* log.h in Headers */,
				184DB58E2C2C28B600234BEB /* CMPageTitleViewMacro.h in Headers */,
				184DB4DB2C2C28B500234BEB /* motion_vector.h in Headers */,
				184DB52A2C2C28B500234BEB /* opensslv.h in Headers */,
				184DB4D02C2C28B500234BEB /* hwcontext.h in Headers */,
				184DB4A02C2C28B500234BEB /* avconfig.h in Headers */,
				184DB4B82C2C28B500234BEB /* crc.h in Headers */,
				184DB5482C2C28B500234BEB /* x509.h in Headers */,
				184DB4662C2C28B500234BEB /* IVPlaybackPlayer.h in Headers */,
				184DB5432C2C28B500234BEB /* txt_db.h in Headers */,
				184DB5AC2C2C28B600234BEB /* TIoTCoreDeviceSet.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		187BA4D12C2BEC5D00AC4A65 /* NiViewIoT */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 187BA4DB2C2BEC5D00AC4A65 /* Build configuration list for PBXNativeTarget "NiViewIoT" */;
			buildPhases = (
				F61C894CE00F0A1975573BFC /* [CP] Check Pods Manifest.lock */,
				187BA4CD2C2BEC5D00AC4A65 /* Headers */,
				187BA4CE2C2BEC5D00AC4A65 /* Sources */,
				187BA4CF2C2BEC5D00AC4A65 /* Frameworks */,
				187BA4D02C2BEC5D00AC4A65 /* Resources */,
				80ED44DC89248AB6F7DB7F01 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = NiViewIoT;
			productName = NiViewIoT;
			productReference = 187BA4D22C2BEC5D00AC4A65 /* NiViewIoT.framework */;
			productType = "com.apple.product-type.framework";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		187BA4C92C2BEC5D00AC4A65 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastUpgradeCheck = 1530;
				TargetAttributes = {
					187BA4D12C2BEC5D00AC4A65 = {
						CreatedOnToolsVersion = 15.3;
					};
				};
			};
			buildConfigurationList = 187BA4CC2C2BEC5D00AC4A65 /* Build configuration list for PBXProject "NiViewIoT" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 187BA4C82C2BEC5D00AC4A65;
			productRefGroup = 187BA4D32C2BEC5D00AC4A65 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				187BA4D12C2BEC5D00AC4A65 /* NiViewIoT */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		187BA4D02C2BEC5D00AC4A65 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				184DB5572C2C28B600234BEB /* 替换SDK需替换所有文件 in Resources */,
				184DB5882C2C28B600234BEB /* 打包注意.md in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		80ED44DC89248AB6F7DB7F01 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-NiViewIoT/Pods-NiViewIoT-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-NiViewIoT/Pods-NiViewIoT-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-NiViewIoT/Pods-NiViewIoT-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		F61C894CE00F0A1975573BFC /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-NiViewIoT-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		187BA4CE2C2BEC5D00AC4A65 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				184DB5692C2C28B600234BEB /* IVMonitorPlayer+IoT.swift in Sources */,
				184DB5872C2C28B600234BEB /* NVUtils.swift in Sources */,
				18FA8D562C5CD6D100DA5EC5 /* NIotConnectionPreparer.swift in Sources */,
				184DB5672C2C28B600234BEB /* PropertyModelPath.swift in Sources */,
				184DB55F2C2C28B600234BEB /* Foundation.swift in Sources */,
				184DB5F12C2D604500234BEB /* NIotPlayer.swift in Sources */,
				184DB5602C2C28B600234BEB /* Notifications+Names.swift in Sources */,
				184DB5852C2C28B600234BEB /* NVLog.swift in Sources */,
				184DB56C2C2C28B600234BEB /* IVPlaybackPlayer_v2.swift in Sources */,
				18FC86A52C3680D900AA1F14 /* NIotFileDownloader.swift in Sources */,
				184DB57D2C2C28B600234BEB /* EWKeychain.swift in Sources */,
				184DB5632C2C28B600234BEB /* DistributionNetService.swift in Sources */,
				184DB56B2C2C28B600234BEB /* IoTPlaybackPlayer.swift in Sources */,
				184DB5662C2C28B600234BEB /* PropertyModel.swift in Sources */,
				184DB57F2C2C28B600234BEB /* NVM3U8NetworkManager.swift in Sources */,
				184DB5652C2C28B600234BEB /* PropertyModel_v2.swift in Sources */,
				184DB5712C2C28B600234BEB /* IVPlayer_v2.swift in Sources */,
				184DB57E2C2C28B600234BEB /* NVAPI.swift in Sources */,
				187BA4D72C2BEC5D00AC4A65 /* NiViewIoT.docc in Sources */,
				184DB5F22C2D604500234BEB /* NIotPlaybackPlayer.swift in Sources */,
				184DB56A2C2C28B600234BEB /* IoTFileDownloader.swift in Sources */,
				184DB5622C2C28B600234BEB /* String+Extension.swift in Sources */,
				18FA8D552C5CD6D100DA5EC5 /* NIotOTAManager.swift in Sources */,
				184DB5752C2C28B600234BEB /* IoTPlaybackService_v2.swift in Sources */,
				184DB5EF2C2D604500234BEB /* NIotMonitorPlayer.swift in Sources */,
				184DB5822C2C28B600234BEB /* NVEnvironment.swift in Sources */,
				184DB56F2C2C28B600234BEB /* IoTConnection.swift in Sources */,
				184DB5832C2C28B600234BEB /* NVJson.swift in Sources */,
				18FC86A32C2EC9E100AA1F14 /* MQTTService.swift in Sources */,
				184DB5792C2C28B600234BEB /* IoTService.swift in Sources */,
				184DB5592C2C28B600234BEB /* NIotManager.swift in Sources */,
				184DB5F02C2D604500234BEB /* NIotConnection.swift in Sources */,
				184DB5802C2C28B600234BEB /* NVNetworkManager.swift in Sources */,
				184DB5742C2C28B600234BEB /* IoTPlaybackService_v1.swift in Sources */,
				184DB5812C2C28B600234BEB /* Response.swift in Sources */,
				184DB5732C2C28B600234BEB /* IoTPlaybackPage.swift in Sources */,
				184DB56D2C2C28B600234BEB /* IVPlaybackPlayer+IoT.swift in Sources */,
				184DB57A2C2C28B600234BEB /* NIotSignalMessager.swift in Sources */,
				184DB5612C2C28B600234BEB /* String+AES.swift in Sources */,
				184DB5762C2C28B600234BEB /* IoTPlaybackService.swift in Sources */,
				184DB57B2C2C28B600234BEB /* NIotPlaybackItem.swift in Sources */,
				184DB5722C2C28B600234BEB /* IVPlayer+IoT.swift in Sources */,
				184DB5702C2C28B600234BEB /* IoTPlayer.swift in Sources */,
				184DB55C2C2C28B600234BEB /* NIotNetworking.swift in Sources */,
				184DB5842C2C28B600234BEB /* NVKeychain.swift in Sources */,
				184DB57C2C2C28B600234BEB /* NVDeviceModel.swift in Sources */,
				184DB56E2C2C28B600234BEB /* IoTExploreOrVideoDeviceModel.swift in Sources */,
				184DB5862C2C28B600234BEB /* NVTencentModelCommand.swift in Sources */,
				184DB5682C2C28B600234BEB /* IoTMonitorPlayer.swift in Sources */,
				184DB5772C2C28B600234BEB /* IoTService_v1.swift in Sources */,
				18AB14482D967F9500B98B31 /* XP2PDelegateForwarder.swift in Sources */,
				184DB5782C2C28B600234BEB /* IoTService_v2.swift in Sources */,
				184DB5642C2C28B600234BEB /* PropertyModel_v1.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		187BA4D92C2BEC5D00AC4A65 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.4;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		187BA4DA2C2BEC5D00AC4A65 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.4;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		187BA4DC2C2BEC5D00AC4A65 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 0DE67FDB82E207A0991E25C5 /* Pods-NiViewIoT.debug.xcconfig */;
			buildSettings = {
				BUILD_LIBRARY_FOR_DISTRIBUTION = YES;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = 8UK3729HJ2;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GENERATE_INFOPLIST_FILE = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/AFNetworking/AFNetworking.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Alamofire/Alamofire.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/CocoaAsyncSocket/CocoaAsyncSocket.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/CocoaLumberjack/CocoaLumberjack.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/CocoaMQTT/CocoaMQTT.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/CryptoSwift/CryptoSwift.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/HandyJSON/HandyJSON.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/IVDevTools/IVDevTools.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/MBProgressHUD/MBProgressHUD.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/MqttCocoaAsyncSocket/MqttCocoaAsyncSocket.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/TIoTLinkVideo/TIoTLinkVideo.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/TPCircularBuffer/TPCircularBuffer.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/YYModel/YYModel.framework/Headers\"",
					"\"${PODS_ROOT}/Headers/Public\"",
					"\"${PODS_ROOT}/Headers/Public/TIoTLinkKit_FLV\"",
					"\"${PODS_ROOT}/Headers/Public/TIoTLinkKit_XP2P\"",
					"${PODS_ROOT}/TXLiteAVSDK_TRTC/TXLiteAVSDK_TRTC/TXLiteAVSDK_TRTC.framework/Headers/",
					"\"$(SRCROOT)/NiViewIoT/IotVideo/IVVASLib\"",
				);
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/NiViewIoT/IotVideo/IoTLib",
					"$(PROJECT_DIR)/NiViewIoT/IotVideo/IVFFmpeg/lib/ffmpeg",
					"$(PROJECT_DIR)/NiViewIoT/IotVideo/IVFFmpeg/lib/openssl",
					"$(PROJECT_DIR)/NiViewIoT/IotVideo/IVP2P",
					"$(PROJECT_DIR)/NiViewIoT/IotVideo/IVVASLib",
					"$(PROJECT_DIR)/NiViewIoT/TIoTLinkKit",
				);
				MACH_O_TYPE = staticlib;
				MARKETING_VERSION = 1.0;
				MODULEMAP_FILE = NiViewIoT/NiViewIoT.modulemap;
				MODULE_VERIFIER_SUPPORTED_LANGUAGES = "objective-c objective-c++";
				MODULE_VERIFIER_SUPPORTED_LANGUAGE_STANDARDS = "gnu17 gnu++20";
				ONLY_ACTIVE_ARCH = NO;
				OTHER_SWIFT_FLAGS = "$(inherited) -D COCOAPODS";
				PRODUCT_BUNDLE_IDENTIFIER = com.niceviewer.NiViewIoT;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		187BA4DD2C2BEC5D00AC4A65 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 599FF40C7949EDBEE6151396 /* Pods-NiViewIoT.release.xcconfig */;
			buildSettings = {
				BUILD_LIBRARY_FOR_DISTRIBUTION = YES;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = 8UK3729HJ2;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_OPTIMIZATION_LEVEL = s;
				GENERATE_INFOPLIST_FILE = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/AFNetworking/AFNetworking.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Alamofire/Alamofire.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/CocoaAsyncSocket/CocoaAsyncSocket.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/CocoaLumberjack/CocoaLumberjack.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/CocoaMQTT/CocoaMQTT.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/CryptoSwift/CryptoSwift.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/HandyJSON/HandyJSON.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/IVDevTools/IVDevTools.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/MBProgressHUD/MBProgressHUD.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/MqttCocoaAsyncSocket/MqttCocoaAsyncSocket.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/TIoTLinkVideo/TIoTLinkVideo.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/TPCircularBuffer/TPCircularBuffer.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/YYModel/YYModel.framework/Headers\"",
					"\"${PODS_ROOT}/Headers/Public\"",
					"\"${PODS_ROOT}/Headers/Public/TIoTLinkKit_FLV\"",
					"\"${PODS_ROOT}/Headers/Public/TIoTLinkKit_XP2P\"",
					"${PODS_ROOT}/TXLiteAVSDK_TRTC/TXLiteAVSDK_TRTC/TXLiteAVSDK_TRTC.framework/Headers/",
					"\"$(SRCROOT)/NiViewIoT/IotVideo/IVVASLib\"",
				);
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/NiViewIoT/IotVideo/IoTLib",
					"$(PROJECT_DIR)/NiViewIoT/IotVideo/IVFFmpeg/lib/ffmpeg",
					"$(PROJECT_DIR)/NiViewIoT/IotVideo/IVFFmpeg/lib/openssl",
					"$(PROJECT_DIR)/NiViewIoT/IotVideo/IVP2P",
					"$(PROJECT_DIR)/NiViewIoT/IotVideo/IVVASLib",
					"$(PROJECT_DIR)/NiViewIoT/TIoTLinkKit",
				);
				MACH_O_TYPE = staticlib;
				MARKETING_VERSION = 1.0;
				MODULEMAP_FILE = NiViewIoT/NiViewIoT.modulemap;
				MODULE_VERIFIER_SUPPORTED_LANGUAGES = "objective-c objective-c++";
				MODULE_VERIFIER_SUPPORTED_LANGUAGE_STANDARDS = "gnu17 gnu++20";
				OTHER_SWIFT_FLAGS = "$(inherited) -D COCOAPODS";
				PRODUCT_BUNDLE_IDENTIFIER = com.niceviewer.NiViewIoT;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SKIP_INSTALL = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		187BA4CC2C2BEC5D00AC4A65 /* Build configuration list for PBXProject "NiViewIoT" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				187BA4D92C2BEC5D00AC4A65 /* Debug */,
				187BA4DA2C2BEC5D00AC4A65 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		187BA4DB2C2BEC5D00AC4A65 /* Build configuration list for PBXNativeTarget "NiViewIoT" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				187BA4DC2C2BEC5D00AC4A65 /* Debug */,
				187BA4DD2C2BEC5D00AC4A65 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 187BA4C92C2BEC5D00AC4A65 /* Project object */;
}
