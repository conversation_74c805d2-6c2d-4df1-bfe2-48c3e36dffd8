//
//  NIotConnection.swift
//  NIot
//
//  Created by apple on 2021/11/25.
//

import UIKit
import NiViewIoT.NIot_IoTVideo_Private

@objc
public enum NIotConnType: UInt {
    case live = 0
    case fileDownload = 6
    case thumbDownload = 7
}

@objc
public enum NIotConnStatus: Int {
    case disconnecting = -1
    case disconnected = 0
    case connecting = 1
    case connected = 2
}

/// 连接过程子状态
/// - note:  部分设备/场景是没有子状态的
@objc
public enum NIotConnSubstate: Int {
    /// 服务器已收到连接请求，正在唤醒设备
    case wakingUpDev = 1
    /// 设备已收到唤醒通知，开始握手过程
    case handshaking = 2
    /// 握手过程完成，连接通道已就绪
    case connectReady = 3
}


@objc
public protocol NIotConnectionDelegate {
    /// 状态更新
    /// - Parameters:
    ///   - connection: 连接实例
    ///   - status: 状态
    @objc optional func connection(_ connection: NIotConnection, didUpdateStatus status: NIotConnStatus)
    
    /// 连接过程子状态更新
    /// - note:  部分设备/场景是没有子状态的
    /// - Parameters:
    ///   - connection: 连接实例
    ///   - substate: 连接过程子状态
    @objc optional func connection(_ connection: NIotConnection, didUpdateSubstate substate: NIotConnSubstate)
    
    /// 数据接收速率
    /// - Parameters:
    ///   - connection: 连接实例
    ///   - speed: 接收速率(字节/秒)
    @objc optional func connection(_ connection: NIotConnection, didUpdateSpeed speed: UInt32)
    
    /// 收到错误
    /// - Parameters:
    ///   - connection: 连接实例
    ///   - error: 错误
    @objc optional func connection(_ connection: NIotConnection, didReceiveError error: Error)
    
    /// 收到数据
    /// - Parameters:
    ///   - connection: 连接实例
    ///   - data: 数据
    @objc optional func connection(_ connection: NIotConnection, didReceiveData data: Data)
}
 
class NIoTConnectionDelegateProxy: NSObject, IoTConnectionDelegate {
    
    unowned var _connection: NIotConnection
    weak var connDelegate: NIotConnectionDelegate?
    
    init(connection: NIotConnection) {
        _connection = connection
    }
     
    
    // MARK: - Connection
    func connection(_ connection: IoTConnection, didUpdatedStatus status: IoTConnStatus) {
        guard let status = NIotConnStatus(rawValue: status.rawValue) else {
            return
        }
        connDelegate?.connection?(_connection, didUpdateStatus: status)
    }
    
    func connection(_ connection: IoTConnection, didUpdatedSubstate substate: IoTConnSubstate) {
        guard let substate = NIotConnSubstate(rawValue: substate.rawValue) else {
            return
        }
        connDelegate?.connection?(_connection, didUpdateSubstate: substate)
    }
    
    func connection(_ connection: IoTConnection, didUpdatedSpeed speed: UInt32) {
        connDelegate?.connection?(_connection, didUpdateSpeed: speed)
    }
    
    func connection(_ connection: IoTConnection, didReceivedError error: Error) {
        connDelegate?.connection?(_connection, didReceiveError: error)
    }
    
    func connection(_ connection: IoTConnection, didReceivedData data: Data) {
        connDelegate?.connection?(_connection, didReceiveData: data)
    }
}


/// Abstract class
@objcMembers
open class NIotConnection: NSObject {
    
    var _connection: IoTConnection?
    
    public weak var connDelegate: NIotConnectionDelegate? {
        didSet {
            _connection?.connDelegate = delegateProxy
            delegateProxy.connDelegate = connDelegate
        }
    }
    
    private var delegateProxy: NIoTConnectionDelegateProxy!
    
    override init() {
        super.init()
        defer {
            delegateProxy = NIoTConnectionDelegateProxy(connection: self)
        }
    }
     
}

extension NIotConnection {
    /// 设备ID
    open var deviceId: String? { _connection?.deviceId }
    /// 源ID（一个设备 可以对应 多个源），默认为0
    open var sourceId: UInt16? { _connection?.sourceId }
    /// 通道ID，连接成功该值才有效（一个设备+一个源 对应 唯一通道），无效值为-1
    open var channel: UInt32? { _connection?.channel }
    /// 连接类型
    open var connType: NIotConnType? {
        guard let connection = _connection else { return nil }
        return NIotConnType(rawValue: connection.iConnType.rawValue)
    }
    /// 连接状态
    open var connStatus: NIotConnStatus? {
        guard let connection = _connection else { return nil }
        return NIotConnStatus(rawValue: connection.iConnStatus.rawValue) }
}

/// iOS下载错误码 [21060-21079]
public enum NIotDownloadError: UInt {
    /// 下载器正忙，若要下载其他文件，请先暂停当前任务
    case downloaderBusy    = 21060
    /// 文件读取失败，文件被删除、存储设备拔出
    case fileUnavailable   = 21061
    /// 断点续传offset大于文件大小fileSize
    case incorrectOffset   = 21062
    /// 打开文件失败，文件权限、文件被删除、存储设备拔出
    case fileOpenFailed    = 21063
    /// 找不到文件，文件被删除，存储设备拔出
    case fileNotFound      = 21064
    /// 设备程序/模块退出、无法继续传输
    case processExited     = 21065
    /// 接收的数据大小不等于文件总大小
    case dataSizeAbnormal  = 21070
    /// 设备端发生未知错误
    case devUnknownError   = 21071
    /// 设备端无响应（如固件版本未支持下载，网络故障、程序故障）
    case devNoResponse     = 21072
}

/// iOS连接错误 [21020-21029]
public enum NIotConnError: UInt {
    /// APP端通道连接数已达上限, 见`MAX_CONNECTION_NUM`
    case exceedsMaxNumber  = 21020
    /// 重复的连接通道(同一台设备仅允许同时建立一个播放器连接)，即目标设备的连接通道已被其他播放器对象占用
    case duplicate         = 21021
    /// 建立连接失败
    case connectFailed     = 21022
    /// 连接已断开/连接失败
    case disconnected      = 21023
    /// 用户自定义数据长度超出上限 @c `MAX_PKG_BYTES`
    case exceedsMaxLength  = 21024
    /// 当前连接暂不可用
    case notAvailableNow   = 21025
    /// 发送用户数据失败
    case sendDataFailed    = 21026
}
