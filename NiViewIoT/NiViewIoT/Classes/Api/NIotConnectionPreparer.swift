//
//  NIotConnectionPreparer.swift
//  NiViewIoT
//
//  Created by apple on 2024/8/2.
//  Copyright © 2024 nice. All rights reserved.
//

import UIKit

private class ConnectionDelegate: NSObject, IVConnectionDelegate {
    weak var preparer: NIotConnectionPreparer?

    init(preparer: NIotConnectionPreparer) {
        self.preparer = preparer
    }
    func connection(_ connection: IVConnection, didUpdate status: IVConnStatus) {
        preparer?.handleConnectionUpdate(connection, status: status)
    }
}

/// 设备连接准备
///
/// 内部封装了设备唤醒流程，负责设备 OTA 升级、SD 卡格式化操作前的连接准备工作
public class NIotConnectionPreparer: NSObject {
    
    public typealias CompletionHandler = (_ success: Bool) -> Void
    
    let model: NVDeviceModel
    let completionHandler: CompletionHandler
    
    private var connectionDelegate: ConnectionDelegate?
    /// 用于唤醒设备而创建的通道
    private lazy var transmission: IVTransmission? = {
        let trans = IVTransmission(deviceId: model.device.tid)
        trans?.delegate = connectionDelegate
        return trans
    }()
    
    private var shouldWaitingForConnection = false
    private var connectDeviceSuccess: (() -> Void)?
    
    
    public required init(
        model: NVDeviceModel,
        completionHandler: @escaping CompletionHandler
    ) {
        self.model = model
        self.completionHandler = completionHandler
        super.init()
        self.connectionDelegate = ConnectionDelegate(preparer: self)
    }
    
    public static func prepareConnection(
        forDevice model: NVDeviceModel,
        completionHandler: @escaping CompletionHandler
    ) {
        let preparer = NIotConnectionPreparer(model: model, completionHandler: completionHandler)
        preparer.startConnecting()
    }
    
    func connectSuccessful() {
        NIotLogger.info("[Preparing] -> 设备连接成功")
        completionHandler(true)
        reset()
    }
    
    func connectFailed() {
        NIotLogger.error("[Preparing] -> 设备唤醒失败")
        completionHandler(false)
        reset()
    }
    
    func reset() {
        shouldWaitingForConnection = false
        transmission?.disconnect()
        NotificationCenter.default.removeObserver(self)
    }
    
}

extension NIotConnectionPreparer {
    
    public func startConnecting() {
        model.service.getOnlineStatus(ofDevice: model.device.tid) { status, error in
            DispatchQueue.main.async {
                if status == .online {
                    self.connectSuccessful()
                } else {
                    NIotLogger.info("[Preparing] -> 设备未在线，唤醒")
                    self.connectDevice {
                        NIotLogger.info("[Preparing] -> 设备在线")
                        self.connectSuccessful()
                    }
                }
            }
        }
    }
    
    func connectDevice(success: @escaping () -> Void) {
        connectDeviceSuccess = success
        if model.isV2Device {
            guard let productId = model.device.sdk2ProductId else {
                NIotLogger.error("[Preparing] -> 设备唤醒失败：2.0 设备没有返回 productId")
                connectFailed()
                return
            }
            NotificationCenter.default.addObserver(self, selector: #selector(self.devicePropertyDidUpdate(_:)), name: .NIot_DevicePropertyDidUpdate, object: nil)
            
            IotMessager.shared.wakeDeviceUp(model.device.tid, productId: productId) { tid, isSuccess in
                guard tid == self.model.device.tid else {
                    self.connectFailed()
                    return
                }
                if isSuccess {
                    NIotLogger.info("[Preparing] -> 2.0信令发送成功，等待xp2p info") // xp2p info 返回才是在线
                    self.shouldWaitingForConnection = true
                } else {
                    self.connectFailed()
                }
            }
        } else {
            shouldWaitingForConnection = true
            transmission?.connect()
        }
    }
    
    @objc func devicePropertyDidUpdate(_ notification: Notification) {
        guard let userInfo = notification.userInfo as? [String: String],
              model.isV2Device, shouldWaitingForConnection,
              let tid = model.device.tid, userInfo["deviceId"] == tid,
              let path = userInfo["path"], path == "_sys_xp2p_info"
        else { return }
        NIotLogger.info("[Preparing] xp2p info received, 唤醒成功.")
        connectDeviceSuccess?()
    }
    
    func handleConnectionUpdate(_ connection: IVConnection, status: IVConnStatus) {
        guard shouldWaitingForConnection else { return }
        DispatchQueue.main.async {
            if status == .connected {
                self.connectDeviceSuccess?()
            }
            if status == .disconnected {
                self.connectFailed()
            }
        }
    }
}

