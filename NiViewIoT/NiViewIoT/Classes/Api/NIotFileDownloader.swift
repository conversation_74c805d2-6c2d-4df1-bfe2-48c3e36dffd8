//
//  NIotFileDownloader.swift
//  NiViewIoT
//
//  Created by apple on 2024/7/4.
//

import UIKit
import NiViewIoT.NIot_IoTVideo_Private

/// 下载器状态
public enum NIotDownloaderStatus: Int {
    /// 空闲中
    case idle          = 0
    /// 请求文件中
    case requesting    = 1
    /// 已就绪（即将下载数据）
    case ready         = 2
    /// 下载中...
    case loading       = 3
}

/// SD 卡录像下载器
open class NIotFileDownloader {
    
    private var _downloader: IoTFileDownloader?
    
    public var status: NIotDownloaderStatus {
        set {
            _downloader?.downloaderStatus = IVDownloaderStatus(rawValue: newValue.rawValue) ?? .idle
        }
        get {
            guard let _downloader = _downloader else { return .idle }
            return NIotDownloaderStatus(rawValue: _downloader.downloaderStatus.rawValue) ?? .idle
        }
    }
    
    public init(deviceId: String, fileName: String? = nil, fileTime: UInt64 = 0, fileSize: UInt32 = 0, rcvSize: UInt = 0, status: NIotDownloaderStatus = .idle) {
        if fileName == nil {
            _downloader = IVFileDownloader(deviceId: deviceId)
        } else {
            _downloader = IVFileDownloader_v2(deviceId: deviceId, fileName: fileName, fileTime: fileTime, fileSize: fileSize)
        }
    }
    
}

public extension NIotFileDownloader {
    
    var fileName: String? { _downloader?.fileName }
    var fileTime: UInt64 { _downloader?.fileTime ?? 0 }
    var fileSize: UInt32 { _downloader?.fileSize ?? 0 }
    var rcvSize: UInt32 { _downloader?.rcvSize ?? 0 }
    
    func downloadPlaybackFile(
        _ fileTime: UInt64,
        offset: UInt32,
        ready: @escaping (UInt32) -> Void,
        progress: @escaping (Data) -> Void,
        canceled: @escaping () -> Void,
        success: @escaping () -> Void,
        failure: @escaping (Error) -> Void
    ) {
        _downloader?.downloadPlaybackFile(fileTime, offset: offset, ready: ready, progress: progress, canceled: canceled, success: success, failure: failure)
    }
    
    func cancel() {
        _downloader?.cancel()
    }
    
    func stop() {
        _downloader?.stop()
    }
    
}
