//
//  NIotMessager.swift
//  NiViewIoT
//
//  Created by apple on 2024/8/7.
//  Copyright © 2024 nice. All rights reserved.
//

import UIKit

private class MessageDelegate: NSObject, IVMessageDelegate {
    
    func didReceiveEvent(_ event: String, topic: String) {
        
    }
    
    func didUpdateProperty(_ json: String, path: String, deviceId: String) {
        if path == "ProReadonly",
           let readOnly = PropertyModel_v1.ModelProReadonly.deserialize(from: json),
           let NID = readOnly.NID?.stVal as? String, NID == "",
           let funcSet = readOnly.funcList?.stVal as? Int, funcSet == 0 {
            NIotLogger.error("[PropertyModel] property model is empty, skip notifing.")
            return
        }
        NotificationCenter.default.post(
            name: .NIot_DevicePropertyDidUpdate,
            object: nil,
            userInfo: [
                "json": json,
                "path": path,
                "deviceId": deviceId
            ]
        )
    }
    func didReceivePassthroughData(_ data: Data, deviceId: String) {
        
    }
}
public class NIotMessager: NSObject {
    public static let shared = NIotMessager()
    private let messageDelegate = MessageDelegate()
    
    public func startObserve() {
        IVMessageMgr.sharedInstance.delegate = messageDelegate
    }
}
