//
//  NIotLivePlayer.swift
//  NIot
//
//  Created by apple on 2021/11/23.
//

import UIKit
import NiViewIoT.NIot_IoTVideo_Private
 
// IVPlayer 封装
// DeviceModel 封装
// IVFileDownloader 封装，IVDownloadItem 不封装

/// 监控播放器
@objcMembers
open class NIotMonitorPlayer: NIotPlayer {
    
    private var _monitorPlayer: IoTMonitorPlayer! {
        get { _player as? IoTMonitorPlayer }
        set { _player = newValue }
    }
    
    /// 创建播放器
    /// - Parameters:
    ///   - deviceModel: device model
    ///   - uid: user id
    required public init?(deviceModel: NVDeviceModel, uid: Int64) {
        super.init()
        if deviceModel.isV2Device {
            _monitorPlayer = IVPlayer_v2(uid: uid, deviceModel: deviceModel)
        } else {
            guard let player = IVMonitorPlayer(deviceId: deviceModel.device.tid) else {
                return nil
            }
            _monitorPlayer = player
        }
    }
}

extension NIotMonitorPlayer {
    /// 当前与设备对讲的客户端数量
    open var talkerNum: UInt { _monitorPlayer.talkerNum }
    /// 是否正在对讲
    open var isTalking: Bool { _monitorPlayer.isTalking }
}

extension NIotMonitorPlayer {
      
    /// 开启对讲
    open func startTalking() {
        _monitorPlayer.startTalking()
    }
     
    /// 开启对讲
    open func startTalking(_ completionHandler: NIotSettingCallback?) {
        _monitorPlayer.startTalking(completionHandler)
    }
    
    /// 结束对讲
    open func stopTalking() {
        _monitorPlayer.stopTalking()
    }
     
    /// 结束对讲
    open func stopTalking(_ completionHandler: NIotSettingCallback?) {
        _monitorPlayer.stopTalking(completionHandler)
    }
}
