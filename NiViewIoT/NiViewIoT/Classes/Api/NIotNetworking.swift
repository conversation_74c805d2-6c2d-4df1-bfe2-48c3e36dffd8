//
//  NIotNetworking.swift
//  NIot
//
//  Created by apple on 2021/11/15.
//

import UIKit
import HandyJSON
import NiViewIoT.NIot_IoTVideo_Private

@objc
public enum NIotNetConfigLanguage: UInt {
   case Chinese = 1
   case English
}


@objcMembers
open class NIotNetworking: NSObject {
    
    let Domains = [
        "eur": "cloud-eur1.niceviewer.com",
        "ap": "cloud-ap1.niceviewer.com",
        "us": "cloud-us1.niceviewer.com",
        "cn": "cloud-cn1.niceviewer.com",
    ]
    public typealias CompletionHandler = (_ r: Response) -> Void
     
    @objc(sharedInstance)
    public static let shared = NIotNetworking()
      
    private let networkMgr = NVNetworkingManager.ShareInstance
    
    
    /// 初始化
    /// - Parameters:
    ///   - appName: app name
    ///   - appLanguage:  app language
    ///   - secretId: secret id
    ///   - secretKey:  secret key
    open func setup(appName: String, appLanguage: NIotNetConfigLanguage, appVersion: String, secretId: String, secretKey: String) {
        // 因为 token 也在 NVEnvironment 里维护，所以这里把 appName、appLanguage、secretId、secretKey 也交由 NVEnvironment 维护
        NVEnvironment.shared.setup(appName: appName, appLanguage: appLanguage, appVersion: appVersion, secretId: secretId, secretKey: secretKey)
        NotificationCenter.default.addObserver(forName: .NIot_LogoutAction, object: nil, queue: nil) { _ in
            self.logoutSuccessHandler()
        }
    }
    /// 语言发生变化时调用
    /// - Parameters:
    ///   - appLanguage:  app language
    open func update(appLanguage: NIotNetConfigLanguage) {
        NVEnvironment.shared.update(appLanguage: appLanguage)
    }
    
}

// MARK: - User
extension NIotNetworking {
    
    public func makeRequest(api: NVApi, completionHandler: @escaping CompletionHandler) {
        networkMgr.request(api) { completionHandler($0.data, $0.error) }
    }
    
    //根据是否有二级域名进行下一步操作
    open func requestSecondDomain(for account: String, completionHandler: @escaping CompletionHandler) {
        //请求二级域名并保存
        let api = NVApi.userRegion(account: account)
        networkMgr.request(api) { response in
            if response.isSuccess {
                if let dataDict = response.data as? [String: Any] {
                    if let domain = dataDict["serverDomain"] as? String,
                       let resAccount = dataDict["account"] as? String,
                       resAccount.lowercased() == account.lowercased() {
                        NIotLogger.info("[Domain] Account(\(account)) get second domain: \(domain)")
                        NVEnvironment.shared.update(domain: domain, for: account)
                        NVEnvironment.shared.update(currentDomain: domain)
                        completionHandler(domain as AnyObject, nil)
                    } else {
                        NIotLogger.error("[Domain] Account(\(account)) get second domain failed, msg:\(dataDict["msg"] ?? "")")
                        completionHandler(nil, nil)
                    }
                } else {
                    NIotLogger.error("[Domain] Account(\(account)) get second domain failed")
                    completionHandler(nil, nil)
                }
            } else {
                NIotLogger.error("[Domain] Account(\(account)) get second domain failed, error:\(response.error?.localizedDescription ?? "")")
                completionHandler(nil, response.error)
            }
        }
    }
    
    /// 根据是否有二级域名进行下一步操作
    /// 未注册用户通过验证码注册登录时，才匹配域名
    open func getSecondDomain(for account: String, continent: String? = nil, completionHandler: @escaping (String?) -> Void) {
        // 重置二级域名，防止出现`其他用户退出登录后，登录新的账号，但是新账号服务器接口不会返回匹配的二级域名，这时二级域名没有更新，还是用的上一个账号的，导致新用户注册不了`的情况，比如：上一个账号是中国域名，退出后注册美国账号
        NVEnvironment.shared.resetCurrentDomain()
        if let domain = NVEnvironment.shared.domain(for: account) {
            NIotLogger.info("[Domain] Account(\(account) already has a stored domain: \(domain)")
            NVEnvironment.shared.update(currentDomain: domain)
            completionHandler(domain)
        } else {
            requestSecondDomain(for: account) { [weak self] domain, error in
                guard let self = self else { return }
                guard error == nil else {
                    completionHandler(nil)
                    return
                }
                if let domain = domain {
                    completionHandler(domain as? String)
                } else {
                    if let continent = continent,
                       let secondDomain = self.Domains[continent] {
                        NIotLogger.info("[Domain] Account not register, set second domain by continent.")
                        NVEnvironment.shared.update(currentDomain: secondDomain)
                        NIotLogger.info("[Domain] continent: \(continent), domain: \(secondDomain)")
                        completionHandler(secondDomain)
                    } else {
                        completionHandler(nil)
                    }
                }
            }
        }
    }
    
    /// 发送邮箱验证码
    /// - Parameters:
    ///   - email: 邮箱
    ///   - continent: 地区（NiView 自定义的服务器区域，见 COUNTRY_CODES.plist）
    ///   - completionHandler: 回调
    open func sendVerifyCodeFor(email: String, continent: String? = nil, completionHandler: @escaping CompletionHandler) {
        getSecondDomain(for: email, continent: continent) { [weak self] _ in
            guard let self = self else { return }
            let api = NVApi.emailVerifyCode(email: email)
            self.networkMgr.request(api) { completionHandler($0.data, $0.error) }
        }
    }
    
    /// 发送手机验证码
    /// - Parameters:
    ///   - phone: 手机号码
    ///   - countryCode: 手机号码国家区号，目前仅支持中国，即：+86
    ///   - continent: 地区（NiView 自定义的服务器区域，见 COUNTRY_CODES.plist）
    ///   - completionHandler: 回调
    open func sendVerifyCodeFor(phone: String, countryCode: String, continent: String? = nil, completionHandler: @escaping CompletionHandler) {
        getSecondDomain(for: phone, continent: continent) { [weak self] _ in
            guard let self = self else { return }
            let api = NVApi.phoneVerifyCode(phone: phone, countryCode: countryCode)
            self.networkMgr.request(api) { completionHandler($0.data, $0.error) }
        }
    }
    
    /// 邮箱验证码登录
    /// - Parameters:
    ///   - email: 邮箱
    ///   - verifyCode: 邮箱验证码
    ///   - completionHandler: 回调
    open func login(email: String, verifyCode: String, completionHandler: @escaping CompletionHandler) {
        let api = NVApi.loginWithEmailVerifyCode(email: email, verifyCode: verifyCode)
        networkMgr.request(api) { [weak self] response in
            if response.isSuccess, let json = response.data as? [String: Any] {
                self?.loginSuccessHandler(json)
                if let currentDomain = json["domainName"] as? String {
                    let key = email.lowercased()
                    if let domain = NVEnvironment.shared.domain(for: key) {
                        if currentDomain != domain {
                            NVEnvironment.shared.update(currentDomain: currentDomain)
                        }
                    }
                    NVEnvironment.shared.update(domain: currentDomain, for: key)
                }
            }
            completionHandler(response.data, response.error)
        }
    }
    
    /// 手机验证码登录
    /// - Parameters:
    ///   - phone: 手机号码
    ///   - verifyCode: 手机验证码
    ///   - completionHandler: 回调
    open func login(phone: String, verifyCode: String, completionHandler: @escaping CompletionHandler) {
        let api = NVApi.loginWithPhoneVerifyCode(phone: phone, verifyCode: verifyCode)
        networkMgr.request(api) { [weak self] response in
            if response.isSuccess, let json = response.data as? [String: Any] {
                self?.loginSuccessHandler(json)
                if let currentDomain = json["domainName"] as? String {
                    let key = phone.lowercased()
                    if let domain = NVEnvironment.shared.domain(for: key) {
                        if currentDomain != domain {
                            NVEnvironment.shared.update(currentDomain: currentDomain)
                        }
                    }
                    NVEnvironment.shared.update(domain: currentDomain, for: key)
                }
            }
            completionHandler(response.data, response.error)
        }
    }
    
    /// 邮箱密码登录
    /// - Parameters:
    ///   - email: 邮箱
    ///   - password: 密码
    ///   - completionHandler: 回调
    open func login(email: String, password: String, completionHandler: @escaping CompletionHandler) {
        getSecondDomain(for: email) { [weak self] _ in
            guard let self = self else { return }
            let api = NVApi.loginWithEmailPwd(email: email, password: password)
            self.networkMgr.request(api) { response in
                if response.isSuccess, let json = response.data as? [String: Any] {
                    self.loginSuccessHandler(json)
                }
                completionHandler(response.data, response.error)
            }
        }
    }
    
    /// 手机密码登录
    /// - Parameters:
    ///   - phone: 手机号码
    ///   - password: 密码
    ///   - completionHandler: 回调
    open func login(phone: String, password: String, completionHandler: @escaping CompletionHandler) {
        getSecondDomain(for: phone) { [weak self] _ in
            guard let self = self else { return }
            let api = NVApi.loginWithPhonePwd(phone: phone, password: password)
            self.networkMgr.request(api) { response in
                if response.isSuccess, let json = response.data as? [String: Any] {
                    self.loginSuccessHandler(json)
                }
                completionHandler(response.data, response.error)
            }
        }
    }
     
    
    /// token 免密登录
    /// - Parameters:
    ///   - token: token
    ///   - completionHandler: 回调
    open func loginWithToken(_ completionHandler: @escaping CompletionHandler) {
        let api = NVApi.loginWithToken
        networkMgr.request(api) { [weak self] response in
            if response.isSuccess, let json = response.data as? [String: Any] {
                self?.loginSuccessHandler(json)
            }
            completionHandler(response.data, response.error)
        }
    }
    
    /// 退出登录
    /// - Parameters:
    ///   - uid: 用户 id
    ///   - completionHandler: 回调
    open func loginOut(uid: Int64, completionHandler: @escaping CompletionHandler) {
        let api = NVApi.logout(uid: uid)
        networkMgr.request(api) { [weak self] in
            completionHandler($0.data, $0.error)
            self?.logoutSuccessHandler()
        }
    }
    
    /// 更新用户密码
    /// - Parameters:
    ///   - account: 账号（email）
    ///   - uid: 用户 id
    ///   - password: 密码
    ///   - verifyCode: 验证码
    ///   - completionHandler: 回调
    open func updatePassword(account: String, uid: Int64, password: String, verifyCode: String, completionHandler: @escaping CompletionHandler) {
        let api = NVApi.updateLoginPwd(account: account, uid: uid, password: password, verifyCode: verifyCode)
        networkMgr.request(api) { completionHandler($0.data, $0.error) }
    }
    
    /// 注销账号
    /// - Parameters:
    ///   - account: 账号（email）
    ///   - uid: 用户 id
    ///   - verifyCode: 验证码
    ///   - completionHandler: 回调
    open func unregister(account: String, uid: Int64, verifyCode: String, completionHandler: @escaping CompletionHandler) {
        let api = NVApi.unregister(account: account, uid: uid, verifyCode: verifyCode)
        networkMgr.request(api) { [weak self] in
            completionHandler($0.data, $0.error)
            self?.logoutSuccessHandler()
            NVEnvironment.shared.update(domain: "", for: account)
        }
    }
    
    
    /// 更新用户头像
    /// - Parameters:
    ///   - uid: 用户 id
    ///   - image: 头像
    ///   - completionHandler: 回调
    open func updateAvatar(uid: Int64, image: UIImage, completionHandler: @escaping CompletionHandler) {
        let api = NVApi.userImage(uid: uid, image: image)
        networkMgr.uploadImage(
            api: api,
            progressClosure: { (progress) in },
            successClosure: { response, error  in completionHandler(response as AnyObject, nil) }
        )
    }
}

// MARK: - Device
extension NIotNetworking {
    
    /// 生成配网二维码
    /// - Parameters:
    ///   - uid: 用户 id
    ///   - timestamp: 时间戳，需要与 bindDevice 接口时间戳一致
    ///   - wifiName: wifi name
    ///   - wifiPassword: wifi password
    ///   - imgSize: image size
    /// - Returns: 二维码
    open func getQrCodeBitmap(uid: Int64, timestamp: Int64, wifiName: String, wifiPassword: String, imgSize: CGSize) -> UIImage? {
        var extraInfo = [String: String]()
        let strArr = NVEnvironment.shared.currentDomain.components(separatedBy: ".")
        if let region = strArr.first?.components(separatedBy: "-").last {
            extraInfo["B"] = region
        }
        extraInfo["6"] = String(uid)
        extraInfo["7"] = String(timestamp)
        extraInfo["9"] = getTimeZone()
        let language: IVNetConfigLanguage = {
            guard let appLanguage = NVEnvironment.shared.appLanguage,
                    let cfgLanguage = IVNetConfigLanguage.init(rawValue: appLanguage.rawValue) else {
                return .EN
            }
            return cfgLanguage
        }()
        return IVNetConfig.qrCode.createQRCode(withWifiName: wifiName, wifiPassword: wifiPassword, language: language, token: "", extraInfo: extraInfo, qrSize: imgSize)
    }
    
    /// 绑定设备
    /// - Parameters:
    ///   - uid: 用户 id
    ///   - timestamp: 时间戳，需要与 getQrCodeBitmap 接口时间戳一致
    ///   - completionHandler: 回调
    public func bindDevice(config: DeviceBindingConfig, completionHandler: @escaping CompletionHandler) {
        
        var api = NVApi.bindDevice_v2(config: config)
        if config.isBinding4gDevice {
            api = NVApi.bind4GDevice(config: config)
        }
        networkMgr.request(api) { response in
            if let dataDict = response.data as? [String: Any] {
                NVLog("配网成功返回的设备数据--->\(dataDict)")
                if let devToken = dataDict["devToken"] as? String {
                    IVNetConfig.subscribeDevice(withAccessToken: devToken, deviceId: dataDict["tid"] as! String) { result, error in
                        NVLog("result: \(result)")
                    }
                }
            }
            completionHandler(response.data, response.error)
        }
    }
    
    /// 解绑设备
    /// - Parameters:
    ///   - uid: 用户 id
    ///   - accessId: accessId
    ///   - did: 设备 did
    ///   - tid: 设备 tid
    ///   - role: 0：主设备，1：分享的设备
    ///   - completionHandler: 回调
    public func unbindDevice(uid: Int64, accessId: Int64, deviceModel: NVDeviceModel, completionHandler: @escaping (Error?) -> Void) {
        if deviceModel.isAdmin {
            deviceModel.service.deleteDevice(tid: deviceModel.device.tid) { data, error in
                guard error == nil,
                      deviceModel.isV2Device,
                      let productId = deviceModel.device.sdk2ProductId
                else { return }
                // 删除 2.0 设备后，主动唤醒一下设备
                IotMessager.shared.wakeDeviceUp(deviceModel.device.tid, productId: productId) { tid, isSuccess in
                    
                }
            }
        }
        guard let d = deviceModel.device,
              let did = d.id,
              let tid = d.tid,
              let role = deviceModel.role else {
            assertionFailure("invalid params")
            completionHandler(NVError(code: -1, errorMsg: "invalid params"))
            return
        }
        let api = NVApi.unbindDevice(uid: uid, accessId: accessId, did: did, tid: tid, role: role)
        networkMgr.request(api) { response in
            if response.isSuccess || response.code == .deviceAlreadyUnbindError {
                completionHandler(nil)
            } else {
                completionHandler(response.error)
            }
        }
        
    }
    
    /// 申请解绑设备
    /// - Parameters:
    ///   - nid: 设备 nid
    ///   - account: account
    ///   - verify: 验证码
    ///   - completionHandler: 回调
    public func applyUnbindDevice(nid: String, account: String, verify: String, completionHandler: @escaping (Error?) -> Void) {
        let api = NVApi.applyUnbindDevice(nid: nid, account: account, verify: verify)
        networkMgr.request(api) { completionHandler($0.error) }
        
    }
    
    /// 获取设备列表
    /// - Parameters:
    ///   - uid: 用户 id
    ///   - completionHandler: 回调
    public func getDeviceList(uid: Int64, completionHandler: @escaping ([NVDeviceModel]?, Error?) -> Void) {
        let api = NVApi.deviceList(uid: uid)
        networkMgr.request(api) { response in
            var deviceArr: [NVDeviceModel] = []
            if let devices = response.data as? NSArray {
                for i in 0..<devices.count {
                    if let nsDict: NSDictionary = devices[i] as? NSDictionary,
                       let model = NVDeviceModel.deserialize(from: nsDict) {
                        // 同步在线状态，修复在线状态无法缓存问题，修复下拉刷新时设备状态会先复原为离线然后又更新的问题
                        var pm: PropertyModel = model.isV2Device ? PropertyModel_v2() : PropertyModel_v1()
                        if model.isV2Device {
                            pm.onlineStatus = DeviceMgr.getDeviceOnlineStatus(tid: model.device.tid)
                        } else if let storedModel = NVEnvironment.shared.devices.first(where: { $0.device.tid == model.device.tid }) {
                            pm.onlineStatus = storedModel.propertyModel?.onlineStatus
                        }
                        //加载能力集缓存
                        if let value = DeviceMgr.readDeviceOption(devID: model.device.id, key: kUserDefaultsDeviceFuncList) as? Int {
                            model.propertyModel?.funcSet = PMFunctionSet(rawValue: value)
                        }
                        if let value = DeviceMgr.readDeviceOption(devID: model.device.id, key: kUserDefaultsDeviceTFCardState) as? [String: Any] {
                            pm.sdCardStorage = PMSDCardStorage.deserialize(from: value)
                            NIotLogger.info("[TFCardState] tid: \(model.device.tid ?? ""), read: \(value)")
                        }
                        model.propertyModel = pm
                        deviceArr.append(model)
                    }
                }
                NVEnvironment.shared.devices = deviceArr
                completionHandler(deviceArr, nil)
            } else {
                completionHandler(nil, response.error)
            }
        }
    }
    /// 更新设备名称
    /// - Parameters:
    ///   - did: 设备 did
    ///   - deviceName: 设备 name
    ///   - completionHandler: 回调
    open func updateDeviceName(did: Int64, deviceName: String, completionHandler: @escaping CompletionHandler) {
        let api = NVApi.updateDeviceName(did: did, deviceName: deviceName)
        networkMgr.request(api) { completionHandler($0.data, $0.error) }
    }
    
    /// 分享设备
    /// - Parameters:
    ///   - uid: 用户 id
    ///   - did: 设备 did
    ///   - accountToShare: account to share
    ///   - deviceName: device name
    ///   - completionHandler: 回调
    open func shareDevice(uid: Int64, did: Int64, accountToShare: String, deviceName: String, authConfig: String?, completionHandler: @escaping CompletionHandler) {
        let api = NVApi.shareDevice(uid: uid, accountToShare: accountToShare, did: did, deviceName: deviceName, authConfig: authConfig)
        networkMgr.request(api) { completionHandler($0.data, $0.error) }
    }
    
    /// 取消分享设备
    /// - Parameters:
    ///   - uid: 用户 id
    ///   - did: 设备 did
    ///   - accountToShare: account to share
    ///   - deviceName: device name
    ///   - completionHandler: 回调
    open func unshareDevice(uid: Int64, did: Int64, accountToShare: String, deviceName: String, completionHandler: @escaping CompletionHandler) {
        let api = NVApi.unshareDevice(uid: uid, accountToShare: accountToShare, did: did, deviceName: deviceName)
        networkMgr.request(api) { completionHandler($0.data, $0.error) }
    }
    
    /// 响应设备分享事件
    /// - Parameters:
    ///   - sid: 分享消息id
    ///   - hostUid: 分享用户id
    ///   - shareUid: 被分享用户id
    ///   - did: 设备 id
    ///   - choose: 1：接受 2：拒绝
    ///   - completionHandler: 回调
    open func responseDeviceSharing(sid: Int64, hostUid: Int64, shareUid: Int64, did: Int64, choose: Int, completionHandler: @escaping CompletionHandler) {
        let api = NVApi.responseDeviceSharing(sid: sid, hostUid: hostUid, shareUid: shareUid, did: did, choose: choose)
        networkMgr.request(api) { response in
            if choose == 1,
                let dataDict = response.data as? [String: Any],
                let devToken = dataDict["devToken"] as? String {
                NVLog("配网成功返回的设备数据--->\(dataDict)")
                IVNetConfig.subscribeDevice(withAccessToken: devToken, deviceId: dataDict["tid"] as! String) { result, error in
                    NVLog("result: \(result)")
                }
            }
            completionHandler(response.data, response.error)
        }
    }
    
    /// 获取分享消息列表
    /// - Parameters:
    ///   - uid: 用户 id
    ///   - completionHandler: 回调
    open func getShareMessage(uid: Int64, completionHandler: @escaping CompletionHandler) {
        let api = NVApi.getShareMessage(uid: uid)
        networkMgr.request(api) { completionHandler($0.json as AnyObject, $0.error) }
    }
    
    /// 获取最新分享消息
    /// - Parameters:
    ///   - uid: 用户 id
    ///   - completionHandler: 回调
    open func getLatestShareMessage(uid: Int64, completionHandler: @escaping CompletionHandler) {
        let api = NVApi.getLatestShareMessage(uid: uid)
        networkMgr.request(api) { completionHandler($0.data, $0.error) }
    }
    
    /// 设备分享用户列表
    /// - Parameters:
    ///   - uid: 用户 id
    ///   - did: 设备 id
    ///   - completionHandler: 回调
    open func getDeviceSharedUsersList(uid: Int64, did: Int64, completionHandler: @escaping CompletionHandler) {
        let api = NVApi.getDeviceSharedUsersList(uid: uid, did: did)
        networkMgr.request(api) { completionHandler($0.json as AnyObject, $0.error) }
    }
    
    
    /// 设置设备密码
    /// - Parameters:
    ///   - did: 设备 id
    ///   - type: 1 = 设置密码 2 = 修改密码
    ///   - pwd: 新密码
    ///   - oldpwd: 旧密码
    ///   - completionHandler: 回调
    open func setDevicePwdByOldpwd(did: Int64, type: Int, pwd: String, oldpwd: String = "", completionHandler: @escaping CompletionHandler) {
        let api = NVApi.set_device_pwd_by_oldpwd(did: did, type: type, pwd: pwd, oldpwd: oldpwd)
        networkMgr.request(api) { completionHandler($0.data, $0.error) }
    }
    
    /// 通过验证码修改设备密码
    /// - Parameters:
    ///   - did: 设备 id
    ///   - pwd: 新密码
    ///   - verify: 验证码
    ///   - completionHandler: 回调
    open func setDevicePwdByVerify(did: Int64, type: Int, pwd: String, verify: String, completionHandler: @escaping CompletionHandler) {
        let api = NVApi.set_device_pwd_by_verify(did: did, type: type, pwd: pwd, verify: verify)
        networkMgr.request(api) { completionHandler($0.data, $0.error) }
    }
    
    /// 设置设备排序
    /// - Parameters:
    ///   - startSort: 起始位置
    ///   - endSort: 目标修改位置
    ///   - completionHandler: 回调
    open func sortForDevice(startSort: String, endSort: String, completionHandler: @escaping CompletionHandler) {
        let api = NVApi.sortForDevice(startSort: startSort, endSort: endSort)
        networkMgr.request(api) { completionHandler($0.data, $0.error) }
    }
    
    /// 清理设备腾讯云激活码
    open func clearDeviceActiveCode(nid: Int64, completionHandler: @escaping CompletionHandler) {
        let api = NVApi.clearDeviceActiveCode(nid: nid)
        networkMgr.request(api) { completionHandler($0.data, $0.error) }
    }
    
    
    /// 获取设备事件列表
    /// - Parameters:
    ///   - uid: 用户 id
    ///   - did: 设备 id
    ///   - messageType: 消息类型(104：按键唤醒，100：移动侦测，106：设备报警，105：低电报警，查看全部消息传 -1)
    ///   - startTime: 开始时间
    ///   - endTime: 结束时间
    ///   - page: 分页
    ///   - completionHandler: 回调
    open func getDeviceEventsList(uid: Int64, did: Int64, messageType: String, startDate: String, endDate: String, page: Int, limit: Int, completionHandler: @escaping CompletionHandler) {
        let api = NVApi.getDeviceEventsList(uid: uid, did: did, messageType: messageType, startDate: startDate, endDate: endDate, page: page, limit: limit)
        networkMgr.request(api) { completionHandler($0.data, $0.error) }
    }
    
    /// 获取设备事件日历打点列表
    /// - Parameters:
    ///   - uid: 用户 id
    ///   - did: 设备 id
    ///   - messageType: 消息类型(104：按键唤醒，100：移动侦测，106：设备报警，105：低电报警，查看全部消息传 -1)
    ///   - completionHandler: 回调
    open func getDeviceEventsDatesList(uid: Int64, did: Int64, messageType: Int, completionHandler: @escaping CompletionHandler) {
        let api = NVApi.getDeviceEventsDatesList(uid: uid, did: did, messageType: messageType)
        networkMgr.request(api) { completionHandler($0.data, $0.error) }
    }
    
    /// 删除设备事件
    /// - Parameters:
    ///   - jsonArray: Json数组，字段包括（uid,did,createDate）
    ///   - completionHandler: 回调
    open func deleteDeviceEvents(jsonArray: [[String: Any]], completionHandler: @escaping CompletionHandler) {
        // TODO: 支持云存后，需要同步调用腾讯云的接口
        let api = NVApi.deleteDeviceEvents(jsonArray: jsonArray)
        networkMgr.request(api) { completionHandler($0.data, $0.error) }
    }
    
    /// 查询有无可升级的固件版本
    open func getCustomerService(nid: Int64, completionHandler: @escaping CompletionHandler) {
        let api = NVApi.customerService(nid: nid)
        networkMgr.request(api) { completionHandler($0.data, $0.error) }
    }
    
    // MARK: 2.0设备
    
    /// 获取设备在线状态，回调由服务器异步回调至 MQTT
    /// App 刚启动时，MQTT 建立连接后发送请求
    /// 下拉设备列表时的请求服务器也会异步回调，所以下拉列表时不需要再调用这个接口
    /// @see MQTTService.swift
    open func getDeviceStatus(tids: String, completionHandler: @escaping CompletionHandler) {
        let api = NVApi.getDeviceStatus(tids: tids)
        networkMgr.request(api) { completionHandler($0.data, $0.error) }
    }
    
    /// 2.0获取 xp2pInfo
    open func getXp2pInfo(tid: String, completionHandler: @escaping (String?) -> Void) {
        describeDeviceData(tid: tid, path: nil) { json, error in
            if let dict = json as? [String: Any],
               let data = dict["data"] as? [String: Any],
               let p2pInfo = data["data"] as? String {
                let infoModel = PropertyModel_v2.deserialize(from: p2pInfo)
                let xp2pInfoString = infoModel?.xp2pInfo.Value as? String ?? ""
                completionHandler(xp2pInfoString)
            } else {
                completionHandler(nil)
            }
        }
    }
    
    /// 获取设备属性数据
    /// - Parameters:
    ///   - tid: 设备 tid
    ///   - path: 物模型路径
    ///   - completionHandler: 回调
    open func describeDeviceData(tid: String, path: String?, completionHandler: @escaping CompletionHandler) {
        let api = NVApi.describeDeviceData(tid: tid, path: path)
        networkMgr.request(api) { completionHandler($0.json as AnyObject, $0.error) }
    }
    
    /// 修改设备属性数据
    open func controlDeviceData(tid: String, data: String, completionHandler: @escaping CompletionHandler) {
        let api = NVApi.controlDeviceData(tid: tid, data: data)
        networkMgr.request(api) { completionHandler($0.json as AnyObject, $0.error) }
    }
    
    /// 设备操作 Action（异步）
    open func callDeviceActionAsync(completionHandler: @escaping CompletionHandler) {
        let api = NVApi.callDeviceActionAsync
        networkMgr.request(api) { completionHandler($0.data, $0.error) }
    }
    
    /// 设备操作 Action（同步）
    open func callDeviceActionSync(tid: String, actionId: String, inputParams: String, completionHandler: @escaping CompletionHandler) {
        let api = NVApi.callDeviceActionSync(tid: tid, actionId: actionId, inputParams: inputParams)
        networkMgr.request(api) { completionHandler($0.json as AnyObject, $0.error) }
    }

    /// 查询有无可升级的固件版本
    public func appCheckFirmwareUpdate(ofDevice model: NVDeviceModel, completionHandler: @escaping (String?, Error?) -> Void) {
        if model.isV2Device {
            guard let nid = model.device.nid else {
                completionHandler(nil, NVError(code: -1, errorMsg: "unknown device"))
                return
            }
            let api = NVApi.appCheckFirmwareUpdate(nid: nid)
            networkMgr.request(api) { response in
                if let data = response.data as? [String: Any],
                   let CurrentVersion = data["CurrentVersion"] as? String,
                   let DstVersion = data["DstVersion"] as? String,
                   !DstVersion.isEmpty, CurrentVersion != DstVersion {
                    NIotLogger.info("[OTA升级] 2.0 nid:\(nid) has new firmware version.")
                    completionHandler(DstVersion, response.error)
                } else {
                    completionHandler(nil, response.error)
                    NIotLogger.info("[OTA升级] 2.0 nid:\(nid) has no new firmware version.")
                }
            }
        } else if let currentVer = model.propertyModel?.versionInfo?.swVer {
            model.service.getDeviceNewVersion(tid: model.device.tid, currentOTAVersion: currentVer) { json, error in
                guard let jsonStr = json, error == nil else {
                    completionHandler(nil, error)
                    return
                }
                let jsonModel = jsonStr.decode(IVModel<Dictionary<String, String>>.self)
                guard let dataDict = jsonModel?.data, jsonModel?.code == 0 else {
                    completionHandler(nil, NVError(code: -1, errorMsg: "invalid data"))
                    return
                }
                let newVer = dataDict["version"] ?? ""
                if newVer != currentVer, newVer.length > 0 {
                    completionHandler(newVer, nil)
                } else {
                    completionHandler(nil, nil)
                }
            }
        } else {
            completionHandler(nil, NVError(code: -1, errorMsg: "unknown device"))
        }
    }
    
    /// 确认升级固件
    open func appPublishFirmwareUpdateMessage(nid: Int64, completionHandler: @escaping CompletionHandler) {
        let api = NVApi.appPublishFirmwareUpdateMessage(nid: nid)
        networkMgr.request(api) { completionHandler($0.data, $0.error) }
    }
    
    
    /// 添加预置位图片
    open func uploadPresetPosImage(image: UIImage, tid: String, pos: String, completionHandler: @escaping CompletionHandler) {
        let api = NVApi.device_p_pos(tid: tid, pos: pos, image: image)
        networkMgr.uploadImage(
            api: api,
            progressClosure: { (progress) in },
            successClosure: { response, error  in completionHandler(response as AnyObject, error) }
        )
    }
    /// 删除预置位图片
    open func deletePresetPosImage(tid: String, pos: String, completionHandler: @escaping CompletionHandler) {
        let api = NVApi.device_d_pos(tid: tid, pos: pos)
        networkMgr.request(api) { completionHandler($0.data, $0.error) }
    }
    /// 修改分享用户权限配置
    open func updateAuthConfig(uid: Int64, did: Int64, authConfig: String, completionHandler: @escaping CompletionHandler) {
        let api = NVApi.updateAuthConfig(uid: uid, did: did, authConfig: authConfig)
        networkMgr.request(api) { completionHandler($0.data, $0.error) }
    }
}

// MARK: - Push
extension NIotNetworking {
    
    /// 上传手机推送token
    /// - Parameters:
    ///   - uid: 用户 id
    ///   - pushToken: 推送 token
    ///   - completionHandler: 回调
    open func putPushToken(uid: Int64, pushToken: String, completionHandler: @escaping CompletionHandler) {
        let api = NVApi.putPushToken(uid: uid, pushToken: pushToken)
        networkMgr.request(api) {
            completionHandler($0.data, $0.error)
        }
    }
    
    /// 设置推送勿扰模式
    /// - Parameters:
    ///   - uid: 用户 id
    ///   - delayPattern: -1：不接收推送，0：接收推送，大于0：表示多少时间内不接收推送，单位为秒
    ///   - completionHandler: 回调
    open func setDisturbTime(uid: Int64, delayPattern: Int, completionHandler: @escaping CompletionHandler) {
        let api = NVApi.setDisturbTime(uid: uid, delayPattern: delayPattern)
        networkMgr.request(api) { completionHandler($0.data, $0.error) }
    }
    
    /// 获取当前设置的推送勿扰模式
    /// - Parameters:
    ///   - uid: 用户 id
    ///   - completionHandler: 回调
    open func getDisturbTime(uid: Int64, completionHandler: @escaping CompletionHandler) {
        let api = NVApi.getDisturbTime(uid: uid)
        networkMgr.request(api) { completionHandler($0.data, $0.error) }
    }
    
}

// MARK: - App
extension NIotNetworking {
    
    /// 检查app版本，获取是否有新版本
    /// - Parameters:
    ///   - currentVersion: 当前版本
    ///   - completionHandler: 回调
    open func checkAppVersion(currentVersion: String, completionHandler: @escaping CompletionHandler) {
        let api = NVApi.checkAppVersion(currentVersion: currentVersion)
        networkMgr.request(api) { completionHandler($0.data, $0.error) }
    }
    
    /// 检查h5版本，获取是否有新版本
    open func getH5NewVersion(completionHandler: @escaping CompletionHandler) {
        networkMgr.request(NVApi.getH5NewVersion) { completionHandler($0.data, $0.error) }
    }
    
    /// 是否显示商城，上架审核时用到
    /// - Parameters:
    ///   - currentVersion: 当前版本
    ///   - completionHandler: 回调
    open func showFakeShopping(currentVersion: String, completionHandler: @escaping CompletionHandler) {
        let api = NVApi.showFakeShopping(currentVersion: currentVersion)
        networkMgr.request(api) { completionHandler($0.data, $0.error) }
    }
    
    /// 上传日志文件
    /// - Parameters:
    ///   - uid: 用户 ID
    ///   - completionHandler: 回调
    open func uploadLogFile(
        uid: Int64,
        data: Data,
        filename: String,
        progressClosure: @escaping (_ progress:Double) -> Void,
        completionHandler: @escaping CompletionHandler
    ) {
        let api = NVApi.uploadLogFile(uid: uid, data: data)
        NVNetworkingManager.ShareInstance.uploadFileData(api: api, filename: filename, progressClosure: progressClosure) { result, error in
            completionHandler(result as? AnyObject, error)
        }
    }
    
}

// MARK: - 云存
extension NIotNetworking {
    
    open func cloudPackages(type: String, completionHandler: @escaping CompletionHandler) {
        let api = NVApi.cloudPackages(type: type)
        networkMgr.request(api) { completionHandler($0.json as AnyObject, $0.error) }
    }

    open func wxpay(params: [String: Any], completionHandler: @escaping CompletionHandler) {
        let api = NVApi.wxpay(params: params)
        networkMgr.request(api) { completionHandler($0.data, $0.error) }
    }

    open func alipay(params: [String: Any], completionHandler: @escaping CompletionHandler) {
        let api = NVApi.alipay(params: params)
        networkMgr.request(api) { completionHandler($0.data, $0.error) }
    }

    open func paypal(params: [String: Any], completionHandler: @escaping CompletionHandler) {
        let api = NVApi.paypal(params: params)
        networkMgr.request(api) { completionHandler($0.data, $0.error) }
    }

    open func free(params: [String: Any], completionHandler: @escaping CompletionHandler) {
        let api = NVApi.free(params: params)
        networkMgr.request(api) { completionHandler($0.data, $0.error) }
    }
    
    open func paypalRes(params: [String: Any], completionHandler: @escaping CompletionHandler) {
        let api = NVApi.paypalRes(params: params)
        networkMgr.request(api) { completionHandler($0.data, $0.error) }
    }
    
    open func checkPayRes(params: [String: Any], completionHandler: @escaping CompletionHandler) {
        let api = NVApi.checkPayRes(params: params)
        networkMgr.request(api) { completionHandler($0.data, $0.error) }
    }

    open func currentCloudPkg(uid: Int64, dids: String, type: String, completionHandler: @escaping CompletionHandler) {
        let api = NVApi.currentCloudPkg(uid: uid, dids: dids, type: type)
        networkMgr.request(api) { completionHandler($0.json as AnyObject, $0.error) }
    }

    open func allOrderList(uid: Int64, type: String, completionHandler: @escaping CompletionHandler) {
        let api = NVApi.allOrderList(uid: uid, type: type)
        networkMgr.request(api) { completionHandler($0.json as AnyObject, $0.error) }
    }

    open func deviceOrderList(uid: Int64, did: Int64, type: String, completionHandler: @escaping CompletionHandler) {
        let api = NVApi.deviceOrderList(uid: uid, did: did, type: type)
        networkMgr.request(api) { completionHandler($0.json as AnyObject, $0.error) }
    }

    // MARK: 2.0云存
    
    /// 获取具有云存的日期 / 获取视频防盗链播放URL
    open func describeCloudStorageDate(tid: String, completionHandler: @escaping CompletionHandler) {
        let api = NVApi.describeCloudStorageDate(tid: tid)
        networkMgr.request(api) { completionHandler($0.data, $0.error) }
    }
    
    /// 拉取云存事件列表
    open func describeCloudStorageEvents(tid: String, size: String, eventId: String, startTime: String, endTime: String, completionHandler: @escaping CompletionHandler) {
        let api = NVApi.describeCloudStorageEvents(tid: tid, size: size, eventId: eventId, startTime: startTime, endTime: endTime)
        networkMgr.request(api) { completionHandler($0.data, $0.error) }
    }
    
    /// 获取某一天云存时间轴
    open func describeCloudStorageTime(completionHandler: @escaping CompletionHandler) {
        let api = NVApi.describeCloudStorageTime
        networkMgr.request(api) { completionHandler($0.data, $0.error) }
    }
    
    /// 获取视频防盗链播放URL
    open func generateSignedVideoURL(tid: String, videoURL: String?, expireTime: String, startTime: String, endTime: String, completionHandler: @escaping CompletionHandler) {
        let api = NVApi.generateSignedVideoURL(tid: tid, videoURL: videoURL, expireTime: expireTime, startTime: startTime, endTime: endTime)
        networkMgr.request(api) { completionHandler($0.data, $0.error) }
    }
    
    /// 删除2.0云存事件
    /// - Parameters:
    ///   - jsonArray: Json数组，字段包括（uid,did,createDate）
    ///   - completionHandler: 回调
    open func deleteDeviceEvents_v2(tid: String, jsonArray: [[String: Any]], completionHandler: @escaping CompletionHandler) {
        let api = NVApi.deleteCloudStorageEvent(tid: tid, jsonArray: jsonArray)
        networkMgr.request(api) { completionHandler($0.data, $0.error) }
    }
}


// MARK: 4G流量
extension NIotNetworking {
    
    /// 获取可购买的4G流量套餐
    open func get_package_manage(iccid: String, completionHandler: @escaping CompletionHandler) {
        let api = NVApi.get_package_manage(iccid: iccid)
        networkMgr.request(api) { completionHandler($0.data, $0.error) }
    }
    
    /// 单个设备的流量购买记录
    open func get_device_renew_list(iccid: String, completionHandler: @escaping CompletionHandler) {
        let api = NVApi.get_device_renew_list(iccid: iccid)
        networkMgr.request(api) { completionHandler($0.data, $0.error) }
    }
    
    ///当前套餐的用量数据
    open func get_device_current_package_usage(iccid: String, completionHandler: @escaping CompletionHandler) {
        let api = NVApi.get_device_current_package_usage(iccid: iccid)
        networkMgr.request(api) { completionHandler($0.data, $0.error) }
    }
    
    /// 查询iccid指定月份每日使用量
    open func getFlowUsageInMonth(iccid: String, completionHandler: @escaping CompletionHandler) {
        let api = NVApi.getFlowUsageInMonth(iccid: iccid)
        networkMgr.request(api) { completionHandler($0.data, $0.error) }
    }
    
    /// 获取 SIM 卡的 MCC 地址
    open func getMccRegion(iccid: String, completionHandler: @escaping CompletionHandler) {
        let api = NVApi.getMccRegion(iccid: iccid)
        networkMgr.request(api) { completionHandler($0.data, $0.error) }
    }
    
    /// 获取SIM套餐过期时间
    open func getPackageQueueLastTime(iccid: String, completionHandler: @escaping CompletionHandler) {
        let api = NVApi.getPackageQueueLastTime(iccid: iccid)
        networkMgr.request(api) { completionHandler($0.data, $0.error) }
    }
}

extension NIotNetworking {
    /// 生成配网二维码
    public func generateQRCodeImageWith(wifi: String, pwd: String, extraInfo: [String: String]? = nil, size: CGSize) -> UIImage? {
        var language = IVLanguageCode.EN
        if let codeValue = UInt(deviceBindingLanguage()),
            let code = IVLanguageCode(rawValue: codeValue) {
            language = code
        }
        return IVNetConfig.qrCode.createQRCode(
            withWifiName: wifi,
            wifiPassword: pwd,
            language: language,
            token: "",
            extraInfo: extraInfo,
            qrSize: size
        )
    }
}


// MARK: 内部处理
private extension NIotNetworking {
    
    func loginSuccessHandler(_ userInfo: [String: Any]) {
        guard let user = User.deserialize(from: userInfo) else { return }
        NVEnvironment.shared.update(user: user)
        NVEnvironment.shared.setupIoTVideo()
        NVEnvironment.shared.registerRemoteNotifications()
        if let config = MQTTConfig.deserialize(from: user.mqttConfig) {
            IotMessager.shared.mqttConfig = config
            IotMessager.shared.setup()
        }
    }
    
    func logoutSuccessHandler() {
        NVEnvironment.shared.update(user: nil)
        NVEnvironment.shared.resetCurrentDomain()
        if let userName = NVEnvironment.shared.userName {
            NVEnvironment.shared.update(domain: "", for: userName)
        }
        IotMessager.shared.disconnect()
    }
}
