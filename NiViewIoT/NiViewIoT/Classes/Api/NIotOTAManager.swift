//
//  NIotOTAManager.swift
//  NiViewIoT
//
//  Created by apple on 2024/7/31.
//  Copyright © 2024 nice. All rights reserved.
//

import UIKit
import NiViewIoT.NIot_IoTVideo_Private
import NiViewIoT.NIot_TIoTLinkKit_Private

public enum NIotOTAError: Int {
    
    // 自定义
    
    /// 失败
    case failed = -111
    /// 没有新的固件版本
    case noNewVersion = -222
    /// 设备唤醒失败
    case deviceWakeUpFailed = -333
    /// 已经在升级中
    case alreadyProcessing = -444
    
    // SDK返回的
    
    /// SD卡容量不足
    case insufficientSDCardStorage = -39
    /// 没有 SD 卡
    case noSDCard = -38
    /// 设备电量低
    case lowPower = -32
    /// 固件下载失败
    case firmwareDownloadFailed = -4
}

/// 设备 OTA 升级流程管理
public class NIotOTAManager: NSObject {
    public typealias StartHandler = () -> Void
    public typealias ProgressHandler = (_ progress: Int) -> Void
    public typealias CompletionHandler = (_ success: Bool, _ error: NIotOTAError?) -> Void
    
    let model: NVDeviceModel
    let startHandler: StartHandler
    let progressHandler: ProgressHandler
    let completionHandler: CompletionHandler
    
    public required init(
        model: NVDeviceModel,
        startHandler: @escaping StartHandler,
        progressHandler: @escaping ProgressHandler,
        completionHandler: @escaping CompletionHandler
    ) {
        self.model = model
        self.startHandler = startHandler
        self.progressHandler = progressHandler
        self.completionHandler = completionHandler
    }
    
    private var timer: Timer?
    private var discount: Int = 1
    private var retryCount = 0
    private var fakeProgress = 0
    
    
    func upgradeSuccessful() {
        NIotLogger.info("[OTA升级] -> 升级完成")
        completionHandler(true, nil)
        reset()
    }
    
    func upgradeFailed(_ code: Int = 0, error: NIotOTAError? = nil) {
        NIotLogger.error("[OTA升级] -> 升级失败: \(code)")
        let err = NIotOTAError(rawValue: code) ?? error ?? .failed
        completionHandler(false, err)
        reset()
    }
    
    func reset() {
        timer?.invalidate()
        UIApplication.shared.isIdleTimerDisabled = false
        NotificationCenter.default.removeObserver(self)
    }
    
}

extension NIotOTAManager {
    
    public func startUpgrade() {
        UIApplication.shared.isIdleTimerDisabled = true
        //设备状态
        NotificationCenter.default.addObserver(self, selector: #selector(self.devicePropertyDidUpdate(_:)), name: .NIot_DevicePropertyDidUpdate, object: nil)
        
        NIotConnectionPreparer.prepareConnection(forDevice: model) { success in
            if success {
//                DispatchQueue.main.asyncAfter(deadline: .now()+1) {
                    self.handNewVersion()
//                }
            } else {
                self.upgradeFailed(error: .deviceWakeUpFailed)
            }
        }
    }
    
    //处理新版本
    func handNewVersion() {
        if model.isV2Device {
            return handNewVersionV2()
        }
        NIotLogger.info("[OTA升级] -> 开启 OTA 升级流程，tid: \(model.device.tid ?? ""), time: \(Date().string(withFormat: "hh:mm:ss"))")
        model.service.takeAction(ofDevice: model.device.tid, path: kAction + kProOTAVersion, json: "{\"stVal\":\"\"}") { [weak self] (json, error) in
            guard let `self` = self else { return }
            guard error == nil else {
                if self.retryCount == 1 {
                    self.upgradeFailed()
                } else {
                    self.retryCount += 1
                }
                return
            }
            NIotLogger.info("[OTA升级] -> 发起信令")
            self.model.service.takeAction(ofDevice: (self.model.device.tid)!, path: kAction + kProOTAUpgrade, json: "{\"stVal\":1}") { (json, err) in
                DispatchQueue.main.async {
                    guard error == nil else {
                        NIotLogger.error("[OTA升级] -> 信令发送失败:\(error?.localizedDescription ?? "")")
                        self.upgradeFailed()
                        return
                    }
                    NIotLogger.info("[OTA升级] -> 信令发送成功")
                    self.fakeProgress = 0
                    self.startHandler()
                    self.timer = Timer.scheduledTimer(timeInterval: 1, target: self, selector: #selector(self.upgradeDiscount), userInfo: nil, repeats: true)
                }
            }
        }
    }
    /// 2.0 升级
    func handNewVersionV2() {
        guard model.isV2Device, let nid = model.device.nid else {
            upgradeFailed()
            return
        }
        NIotLogger.info("[OTA升级2.0] -> 开启 OTA 升级流程，tid: \(model.device.tid ?? ""), time: \(Date().string(withFormat: "hh:mm:ss"))")
        NIotLogger.info("[OTA升级] -> 发起信令")
        NIotNetworking.shared.appPublishFirmwareUpdateMessage(nid: nid) { [weak self] json, error in
            guard let self = self else { return }
            if let error = error as? NSError {
                if error.code != 0 {
                    self.upgradeFailed(error.code)
                } else {
                    self.upgradeFailed()
                }
            } else {
                NIotLogger.info("[OTA升级] -> 信令发送成功")
                self.fakeProgress = 0
                self.startHandler()
                self.timer = Timer.scheduledTimer(timeInterval: 1, target: self, selector: #selector(self.upgradeDiscount), userInfo: nil, repeats: true)
            }
        }
    }
    
    @objc func upgradeDiscount() {
        if discount > 600 {
            upgradeFailed()
        } else {
            discount += 1
            // 同步UI
            if model.isV2Device {
                fakeProgress += 1
                if fakeProgress >= 98 {
                    fakeProgress = 98
                }
                progressHandler(fakeProgress)
            }
        }
    }
    
    @objc func devicePropertyDidUpdate(_ notification: Notification) {
        guard let userInfo = notification.userInfo as? [String: String],
              let tid = model.device.tid, userInfo["deviceId"] == tid,
              let path = userInfo["path"]
        else { return }
        
        if model.isV2Device {
            let hasStartUpgradingV2 = timer?.isValid ?? false // 通过 timer 判断是否开启了升级流程
            if hasStartUpgradingV2,
               path.contains("hostState"),
               let hostStateValue = userInfo["json"]?.integerValue {
                NIotLogger.info("[OTA升级] -> hostState: \(hostStateValue)")
                if let hostState = PMHostState_v2(rawValue: hostStateValue) {
                    switch hostState {
                    case .downloadStarted:
                        NIotLogger.info("[OTA升级] -> 设备开始下载固件")
                    case .downloadFinished:
                        NIotLogger.info("[OTA升级] -> 设备下载固件完成")
                    case .startUpFlash:
                        NIotLogger.info("[OTA升级] -> 设备正在烧写flash")
                    case .idle, .updateFinished:
                        progressHandler(100)
                        // 这里延时一下，等待进度条进度到达 100 后再消失
                        DispatchQueue.main.asyncAfter(deadline: .now()+0.5) {
                            self.upgradeSuccessful()
                        }
                    }
                } else if hostStateValue < 0 {
                    // 升级失败
                    upgradeFailed(hostStateValue)
                }
            }
        } else {
            // 更新进度
            if path == kProReadonly + kProOTAUpgrade || path == kAction + kProOTAUpgrade {
                guard let json = userInfo["json"], let dict = json.stringValueDic(), let persent = dict[kProReadOnlyKey] as? Int else {
                    NVLog("OTA升级进度解析失败")
                    upgradeFailed()
                    return
                }
                guard persent >= 0 else {
                    NVLog("[OTA升级] -> 进度解析失败--->\(persent)")
                    upgradeFailed(persent)
                    return
                }
                NIotLogger.info("[OTA升级] -> 升级进度:\(persent)")
                progressHandler(persent)
                if persent == 100 {
                    // 这里延时一下，等待进度条进度到达 100 后再消失
                    DispatchQueue.main.asyncAfter(deadline: .now()+0.5) {
                        self.upgradeSuccessful()
                    }
                }
            }
        }
    }
}
