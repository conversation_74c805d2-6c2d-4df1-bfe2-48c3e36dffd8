//
//  NIotPlaybackPlayer.swift
//  NIot
//
//  Created by apple on 2021/11/23.
//

import UIKit
import NiViewIoT.NIot_IoTVideo_Private



/// 回放文件切换策略
@objc
public enum NIotPlaybackStrategy: UInt {
    /// 按文件开始时间从小到大（升序）自动播放，默认值
    case ascending = 0
    /// 按文件开始时间从大到小（降序）自动播放
    case descending = 1
    /// 播放单个文件，播完自动暂停
    case single = 2
}

/// 回放播放器
@objcMembers
open class NIotPlaybackPlayer: NIotPlayer {
    
    private var _playbackPlayer: IoTPlaybackPlayer! {
        get { _player as? IoTPlaybackPlayer }
        set { _player = newValue }
    }
    
    /// 创建播放器
    /// - Parameters:
    ///   - deviceId: 设备ID
    required public init?(deviceModel: NVDeviceModel, uid: Int64) {
        super.init()
        if deviceModel.isV2Device {
            _playbackPlayer = IVPlaybackPlayer_v2(uid: uid, deviceModel: deviceModel)
        } else {
            guard let player = IVPlaybackPlayer(deviceId: deviceModel.device.tid) else {
                return nil
            }
            _playbackPlayer = player
        }
    }
    
}

extension NIotPlaybackPlayer {
    
    /// 回放策略，默认 ascending
    open var playbackStrategy: NIotPlaybackStrategy {
        get {
            NIotPlaybackStrategy(rawValue: _playbackPlayer.iotPlaybackStrategy.rawValue) ?? .ascending
        }
        set {
            guard let ivStrategy = IoTPlaybackStrategy(rawValue: newValue.rawValue) else { return }
            _playbackPlayer.iotPlaybackStrategy = ivStrategy
        }
    }
    
    ///  设置回放策略
    open func setPlaybackStrategy(_ strategy: NIotPlaybackStrategy, completionHandler: NIotSettingCallback?) {
        guard let ivStrategy = IoTPlaybackStrategy(rawValue: strategy.rawValue) else { return }
        _playbackPlayer.setPlaybackStrategy(ivStrategy, completionHandler: completionHandler)
    }
    
    /// 回放倍速
    ///
    /// 默认1.0, 一般取值范围[0.5~16.0], SDK允许传参范围(0.0~32.0]，开发者应视设备性能而定!
    /// - note:  超过2倍速后设备是不会发音频的，并且视频只有关键帧
    open var playbackSpeed: Float {
        get { _playbackPlayer.playbackSpeed }
        set { _playbackPlayer.playbackSpeed = newValue }
    }
    
    /// 设置回放倍速
    open func setPlayerSpeed(_ speed: Float, completionHandler: NIotSettingCallback?) {
        _playbackPlayer.setPlaybackSpeed(speed, completionHandler: completionHandler)
    }
     
    /// 当前回放的文件。
    /// - note: 当前回放时间通过`-[NIotPlayer pts]`获取
    open var playbackItem: NIotPlaybackItem? {
        guard let item = _playbackPlayer.iotPlaybackItem else {
            return nil
        }
        return NIotPlaybackItem(ivPBItem: item)
    }
    
    /// (未播放前)设置回放参数.
    /// - note:  应在文件尚未播放时使用，需手动调用`play`开始播放.
    /// - Parameters:
    ///   - item: 播放的文件(可跨文件).
    ///   - seekToTime: 指定播放起始时间点（秒），取值范围`playbackItem.startTime >= time <= playbackItem.endTime`.
    open func setPlaybackItem(_ item: NIotPlaybackItem, seekToTime: TimeInterval) {
        _playbackPlayer.setIoTPlaybackItem(IVPlaybackItem(niPBItem: item), seekToTime: seekToTime)
    }
    
    /// (已播放后)跳到指定文件和时间播放
    /// - note:  应在文件正在播放时使用，无需再手动调用`play`开始播放.
    /// - Parameters:
    ///   - time: 指定播放起始时间点（秒），取值范围`playbackItem.startTime >= time <= playbackItem.endTime`
    ///   - playbackItem: 播放的文件(可跨文件)
    open func seek(toTime time: TimeInterval, playbackItem: NIotPlaybackItem) {
        _playbackPlayer.seekToTime(time, playbackItem: IVPlaybackItem(niPBItem: playbackItem))
    }
    
    /// (已播放后)跳到指定文件和时间播放
    /// - note:  应在文件正在播放时使用，无需再手动调用`play`开始播放.
    /// - Parameters:
    ///   - time: 指定播放起始时间点（秒），取值范围`playbackItem.startTime >= time <= playbackItem.endTime`
    ///   - playbackItem: 播放的文件(可跨文件)
    ///   - completionHandler: 回调
    open func seek(toTime time: TimeInterval, playbackItem: NIotPlaybackItem, completionHandler: NIotSettingCallback?) {
        _playbackPlayer.seekToTime(time, playbackItem: IVPlaybackItem(niPBItem: playbackItem), completionHandler: completionHandler)
    }
    
    open func pause() {
        _playbackPlayer.pause()
    }
    
    open func pause(completionHandler: NIotSettingCallback?) {
        _playbackPlayer.pause(completionHandler)
    }
    
    open func resume() {
        _playbackPlayer.resume()
    }
    
    open func resume(completionHandler: NIotSettingCallback?) {
        _playbackPlayer.resume(completionHandler)
    }
}

