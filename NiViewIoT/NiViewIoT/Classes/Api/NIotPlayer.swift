//
//  NIotPlayer.swift
//  NIot
//
//  Created by apple on 2021/11/23.
//

import UIKit
import NiViewIoT.NIot_IoTVideo_Private


public typealias NIotSettingCallback = (Error?) -> Void

@objc
public enum NIotVideoDefinition: UInt {
    case low = 0
    case mid = 1
    case high = 2
}

@objc
public enum NIotPlayerStatus: Int {
    /// 停止中...
    case stopping = -1
    /// 已停止
    case stopped = 0
    /// 准备中...
    case preparing = 1
    /// 已就绪（通道建立完成）
    case ready = 2
    /// 加载中...
    case loading = 3
    /// 播放中...
    case playing = 4
    /// 已暂停
    case paused = 5
    /// 快进中...（已过期）
    case fastForward = 6
    /// 跳转中...
    case seeking = 7
}

@objc
public enum NIotPlayerInterruptedReason: UInt {
    case otaUpgrade
    case offline
}

@objc
public protocol NIotPlayerDelegate: NIotConnectionDelegate {
    
    /// 播放器状态回调
    /// - Parameters:
    ///   - player: 播放器实例
    ///   - status: 状态值
    @objc optional func player(_ player: NIotPlayer, didUpdateStatus status: NIotPlayerStatus)
    
    /// 播放时间回调
    /// - Parameters:
    ///   - player: 播放器实例
    ///   - PTS: 时间戳
    @objc optional func player(_ player: NIotPlayer, didUpdatePTS PTS: TimeInterval)
    
    /// SD卡回放文件即将播放
    /// - Parameters:
    ///   - player: 播放器实例
    ///   - fileTime: 时间戳
    @objc optional func player(_ player: NIotPlayer, willBeginOfFile fileTime: TimeInterval)
    
    /// SD卡回放文件播放结束
    /// - Parameters:
    ///   - player: 播放器实例
    ///   - fileTime: 时间戳
    @objc optional func player(_ player: NIotPlayer, didEndOfFile fileTime: TimeInterval, error: Error?)
    
    /// 观看人数变更
    /// - Parameters:
    ///   - player: 播放器实例
    ///   - viewerNum: 观看人数
    @objc optional func player(_ player: NIotPlayer, didUpdateViewerNum viewerNum: Int)
    
    /// 对讲人数变更
    /// - Parameters:
    ///   - player: 播放器实例
    ///   - talkerNum: 观看人数
    @objc optional func player(_ player: NIotPlayer, didUpdateTalkerNum talkerNum: Int)
    
    /// 播放错误回调
    ///
    /// 播放器捕获到的一些错误（不一定会导致播放中断），用于辅助开发者定位和发现问题，不要在此处stop()
    /// - Parameters:
    ///   - player: 播放器实例
    ///   - error: 错误实例
    @objc optional func player(_ player: NIotPlayer, didReceiveError error: Error)
    
    
    
    @objc optional func player(_ player: NIotPlayer, didReceivedAVHeader avHeader: Any)
    
    /// 是否触发 4G 流量警告
    /// - Parameter player: 播放器实例
    /// - Returns: 4G 流量警告触发标识
    @objc optional func shouldWarn4GDataUsage(for player: NIotPlayer) -> Bool
    
    
    /// 播放被中断，用于代理更新 UI
    /// - Parameters:
    ///   - player: 播放器实例
    ///   - reason: 中断原因
    @objc optional func player(_ player: NIotPlayer, didInterrupted reason: NIotPlayerInterruptedReason)
}
 
class NIoTPlayerDelegateProxy: NIoTConnectionDelegateProxy, IoTPlayerDelegate {
    
    unowned var _player: NIotPlayer
    weak var playerDelegate: NIotPlayerDelegate? {
        didSet {
            connDelegate = playerDelegate
        }
    }
    
    init(player: NIotPlayer) {
        _player = player
        super.init(connection: player)
    }
    
    func player(_ player: IoTPlayer, didUpdatedStatus status: IoTPlayerStatus) {
        guard let status = NIotPlayerStatus(rawValue: status.rawValue) else {
            return
        }
        playerDelegate?.player?(_player, didUpdateStatus: status)
    }
    
    func player(_ player: IoTPlayer, didUpdatedPTS PTS: TimeInterval) {
        playerDelegate?.player?(_player, didUpdatePTS: PTS)
    }
    
    func player(_ player: IoTPlayer, willBeginOfFile fileTime: TimeInterval) {
        playerDelegate?.player?(_player, willBeginOfFile: fileTime)
    }
    
    func player(_ player: IoTPlayer, didEndOfFile fileTime: TimeInterval, error: Error?) {
        playerDelegate?.player?(_player, didEndOfFile: fileTime, error: error)
    }
    
    func player(_ player: IoTPlayer, didUpdatedViewerNum viewerNum: Int) {
        playerDelegate?.player?(_player, didUpdateViewerNum: viewerNum)
    }
    
    func player(_ player: IoTPlayer, didUpdatedTalkerNum talkerNum: Int) {
        playerDelegate?.player?(_player, didUpdateTalkerNum: talkerNum)
    }
    
    func player(_ player: IoTPlayer, didReceivedError error: Error) {
        playerDelegate?.player?(_player, didReceiveError: error)
    }
    
    func player(_ player: IoTPlayer, didReceivedAVHeader avHeader: Any) {
        playerDelegate?.player?(_player, didReceivedAVHeader: avHeader)
    }
    
    func shouldWarn4GDataUsage(for player: IoTPlayer) -> Bool {
        return playerDelegate?.shouldWarn4GDataUsage?(for: _player) ?? false
    }
    
    func player(_ player: IoTPlayer, didInterrupted reason: NIotPlayerInterruptedReason) {
        playerDelegate?.player?(_player, didInterrupted: reason)
    }
    
}

/// Abstract class
@objcMembers
open class NIotPlayer: NIotConnection {
      
    var _player: IoTPlayer? {
        get { _connection as? IoTPlayer }
        set { _connection = newValue }
    }
    
    public weak var delegate: NIotPlayerDelegate? {
        didSet {
            _player?.connDelegate = delegateProxy
            _player?.playerDelegate = delegateProxy
            delegateProxy.playerDelegate = delegate
        }
    }
    
    private var delegateProxy: NIoTPlayerDelegateProxy!
    
    override init() {
        super.init()
        defer {
            delegateProxy = NIoTPlayerDelegateProxy(player: self)
        }
    }
    
}

extension NIotPlayer {
    /// 视频清晰度
    public var definition: NIotVideoDefinition {
        get {
            guard let _player = _player else { return .mid }
            return NIotVideoDefinition(rawValue: _player.playerDefinition.rawValue) ?? .mid
        }
        set {
            _player?.playerDefinition = IoTVideoDefinition(rawValue: newValue.rawValue) ?? .mid
        }
    }
    
    /// 设置视频清晰度
    public func setDefinition(_ definition: NIotVideoDefinition, completionHandler: NIotSettingCallback?) {
        _player?.setPlayerDefinition(IoTVideoDefinition(rawValue: definition.rawValue) ?? .high, completionHandler: { error in
            completionHandler?(error)
        })
    }
    
    /// 静音，  默认 false（即允许播放声音）
    public var mute: Bool {
        get { _player?.mute ?? false }
        set { _player?.mute = newValue }
    }
    
    /// 免提， 默认 true, 有外设时无效
    ///
    /// true 没有外设时外放声音,
    /// false  没有外设时听筒处播放声音
    public var handsFree: Bool {
        get { _player?.handsFree ?? false }
        set { _player?.handsFree = newValue }
    }
    
    /// 视频画面
    public var videoView: UIView? { _player?.videoView }
    /// 当前设备观众人数
    public var audience: UInt { _player?.audience ?? 0 }
    /// 播放器状态
    public var status: NIotPlayerStatus? {
        guard let status = _player?.playerStatus else {
            return nil
        }
        return NIotPlayerStatus(rawValue: status.rawValue)
    }
    /// 当前播放时间戳（秒）
    public var pts: TimeInterval { _player?.pts ?? 0 }
    /// 是否正在录像
    public var isPlaying: Bool { _player?.isPlaying ?? false }
    /// 是否正在录像
    public var isRecording: Bool { _player?.isRecording ?? false }
}

extension NIotPlayer {
    
    /// 开始播放
    /// - note: 设备会发送流媒体信息头，接着发送音视频数据
    open func startPlay(forceRefresh: Bool = false) {
        _player?.startPlay(forceRefresh: forceRefresh)
    }
    
    /// 播放
    open func play() {
        _player?.play()
    }
    
    /// 停止播放
    /// - note: 该操作APP将与设备断开连接
    open func stop(cancelReconnect: Bool = false) {
        _player?.stop(cancelReconnect: cancelReconnect)
    }
    
    /// 截图
    /// - Parameter completionHandler: 截图回调
    open func takeScreenshot(scale: CGFloat = 1, _ completionHandler: @escaping (UIImage?) -> Void) {
        _player?.takeScreenshot(scale: scale, completionHandler)
    }
    
    /// 开始录像
    /// - Parameters:
    ///   - savePath: 录像文件路径
    ///   - completionHandler: 完成回调
    open func startRecording(_ savePath: String, completionHandler: @escaping (_ savePath: String?, _ error: Error?) -> Void) {
        _player?.startRecording(savePath, completionHandler: completionHandler)
    }
    
    /// 停止录像
    open func stopRecording() {
        _player?.stopRecording()
    }
    
    /// 发送自定义数据
    /// 与设备建立连接后才可发送，适用于较大数据传输、实时性要求较高的场景，如多媒体数据传输。
    /// 接收到设备端发来的数据见`-[NIoTConnectionDelegate connection:didReceiveData:]`
    /// - Parameters:
    ///   - data: 要发送的数据，data.length不能超过 64000
    open func send(_ data: Data) -> Bool {
        return _player?.send(data: data) ?? false
    }
    
    /// 发送自定义数据
    /// - Parameters:
    ///   - data: 要发送的数据，data.length不能超过 64000
    ///   - sequence: 是否串行发送
    ///   - completionHandler: 完成回调。completionHandler非nil则通过callback回调；completionHandler为nil则通过代理回调；
    open func send(_ data: Data, sequence: Bool, completionHandler: ((_ data: Data?, _ error: Error?, _ finished: UnsafeMutablePointer<ObjCBool>) -> Void)?) {
        _player?.send(data: data, sequence: sequence) { data, error, finished in
            completionHandler?(data, error, finished)
        }
    }
    
}

extension NIotPlayer {
    
    /// 播放器调试模式，默认`NO`。若设为`YES`则在编/解码时将音视频数据写入Document根目录。说明文档: Docs/抓取音视频流方法.md
    /// @note ⚠️开启可能会导致卡顿。
    /// @code
    ///  播放相关音频文件:
    ///  au_rcv_in   音频 接收器 输入流
    ///  au_dec_in   音频 解码器 输入流
    ///  au_dec_out  音频 解码器 输出流
    ///
    ///  播放相关视频文件:
    ///  vi_rcv_in   视频 接收器 输入流
    ///  vi_dec_in   视频 解码器 输入流
    ///
    ///  对讲相关音频文件:
    ///  au_fill_in  音频 采集器 输出流
    ///  au_enc_in   音频 编码器 输入流
    ///  au_enc_out  音频 编码器 输出流
    ///
    ///  双向视频通话相关文件:
    ///  vi_enc_out  视频 编码器 输出流
    /// @endcode
    public static var debugMode: Bool = false {
        didSet {
            IVPlayer.debugMode = NIotPlayer.debugMode
        }
    }
}

extension NIotPlayer {
    
    public func clearObserver() {
        if let player = _player as? IVPlaybackPlayer_v2 {
            // 2.0 SD卡录像播放器停止播放后，需要清空播放器的监听，防止继续响应事件
            NIotLogger.info("[IVPlayer_v2][Playback] stop play playback, clear observer.")
            player.clearObserver()
        }
    }
    
    public func addNotification() {
        if let player = _player as? IVPlaybackPlayer_v2 {
            // 2.0 SD卡录像播放器停止播放后，需要清空播放器的监听，防止继续响应事件
            NIotLogger.info("[IVPlayer_v2][Playback] stop play playback, clear observer.")
            player.addNotification()
        }
    }
    
    public func stopService(_ willDisappear: Bool = false) {
        _player?.stopService(willDisappear)
    }
}
