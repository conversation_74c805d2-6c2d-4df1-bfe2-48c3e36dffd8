//
//  SignalManager.swift
//  NIot
//
//  Created by apple on 2024/6/24.
//

import UIKit

public enum NVDirection: Int {
    case unknow = 0
    case up
    case right
    case down
    case left
    case stop
     
    var data: [Int] {
        switch self {
        case .down:     return [2, 1, 2]
        case .up:       return [2, 2, 2]
        case .left:     return [1, 2, 2]
        case .right:    return [1, 1, 2]
        case .stop:     return [0]
        default: return [0]
        }
    }
    
    // 2.0
    var cmd_v2: String? {
        switch self {
        case .left: return "ptz_left"
        case .right: return "ptz_right"
        case .up: return "ptz_up"
        case .down: return "ptz_down"
        case .stop: return "ptz_release_pre"
        case .unknow: return nil
        }
    }
    
    enum MsgType: Int {
        case preset = 1 //预置位
        case `guard`    //看守位
        case check      //自检
        case free       //自由
    }
    
    enum Action: Int {
        case set = 1    //设置/更改
        case implement  //执行
        case delete     //删除
    }
}

/// PTZ信令
///
/// see https://dkf6ct3k5v.feishu.cn/wiki/wikcnseKpoPLx6XEUcqXT4WYO2f
public enum PTZ {
    
    public enum MsgType {
        case preset(_ index: Int) //预置位 [1~9]
        case `guard`(_ value: Int)    //看守位 [1~2]
        case check      //自检
        case free(_ direction: NVDirection)       //自由
        
        var value: String {
            switch self {
            case .preset: return "1"
            case .guard: return "2"
            case .check: return "3"
            case .free: return "4"
            }
        }
        
        var data: [Int]? {
            switch self {
            case .preset(let index): return [index]
            case .`guard`(let value): return [value]
            case .free(let direction): return direction.data
            default: return nil
            }
        }
    }
    
    public enum Action: String {
        case set = "1"   //设置/更改
        case implement = "2"  //执行
        case delete = "3"     //删除
    }
}

public enum Resolution: String {
    case sd, hd
}
public enum ZoomAction: Int {
    case zoomIn = 1
    case zoomOut = -1
    case stop = 0
}

// TODO: 统一信令透传类型
public enum SignalingAction {
    /// 光学变焦
    case lockFocus
    /// 自动对焦，执行一次，限制用户5s内只触发一次该操作，返回0成功，其他失败
    case autoFocus
    case zoom(_ action: ZoomAction)
    case zoomGo(_ value: Int)
    case getZoomScale
    /// 标定
    case startDemarcating
    case endDemarcating
    case cancelDemarcating
    case setDemarcating(_ pos: Int, _ point: CGPoint)
    case startAutoDemarcating
    case getAutoDemarcatingResult
    /// PTZ
    case ptz(_ msgType: PTZ.MsgType, _ action: PTZ.Action)
    /// 手指追踪
    case ptzFingerTrack(_ point: CGPoint)
    /// T3 高/标清切换指令
    case getResolutionStatusForT3
    case setResolutionStatusForT3(_ r: Resolution)
    /// 2.0 观看录像时直播会退出，需要定期发送信令
    case sendFlag(_ uid: Int64, _ flag: Int)
    /// 获取TF卡状态
    case getTFCardStatus
    case formatTFCard
    
    var actionCmd: [String: Any] {
        switch self {
        case .lockFocus:
            return ["zoom": 100]
        case .autoFocus:
            return ["zoom": 101]
        case .zoom(let action):
            return ["zoom": action.rawValue]
        case .zoomGo(let value):
            return ["zoom_go": value]
        case .getZoomScale:
            return ["zoom": 200]
        case .startDemarcating:
            return ["ptz_cali": "start"]
        case .endDemarcating:
            return ["ptz_cali": "end"]
        case .cancelDemarcating:
            return ["ptz_cali": "cancel"]
        case let .setDemarcating(pos, point):
            return ["ptz_cali": "set", "pos": pos, "px": point.x, "py": point.y]
        case .startAutoDemarcating:
            return ["ptz_cali_auto": "start"]
        case .getAutoDemarcatingResult:
            return ["ptz_cali_auto": "inquire"]
        case let .ptz(msgType, action):
            var cmd: [String : Any] = ["msg_type": msgType.value, "action": action.rawValue, "data": [Int]()]
            if let data = msgType.data {
                cmd["data"] = data
            }
            return ["mt_data": cmd]
        case let .ptzFingerTrack(point):
            return ["ptz_finger_track": ["px": Int(point.x), "py": Int(point.y)]]
        case .getResolutionStatusForT3:
            return ["hd_sd": "get"]
        case let .setResolutionStatusForT3(resolution):
            return ["hd_sd": "set", "val": resolution.rawValue]
        case let .sendFlag(uid, flag):
            return ["uid": "\(uid)", "flag": flag]
        case .getTFCardStatus:
            return ["get_tf_status": "get"]
        case .formatTFCard:
            return ["tf_format": "set"]
        }
    }
}

public enum ZoomCorrelativeError: String {
    /// 已旋转到最大角度
    case maxAngle = "-200"
    /// 设备复位中
    case resetting1 = "-202"
    /// 设备复位中
    case resetting2 = "-101"
    /// 正在自动标定
    case autoDemarcating = "-103"
    /// 正在变焦校准
    case zoomCalibration = "-2"
    /// 上次追踪操作还未完成
    case previousTrackingOperationPending = "1"
    
//    var msg: String {
//        switch self {
//        case .maxAngle: return NVLang("device_max_angle", "已旋转到最大角度")
//        case .resetting1, .resetting2: return NVLang("ptz_resetting", "设备复位中")
//        case .autoDemarcating: return NVLang("Device_Demarcate_Auto_Calibrating", "正在自动标定")
//        case .zoomCalibration: return NVLang("Device_ZoomCalibration_In_Progress", "正在变焦校准")
//        }
//    }
}

/// 信令发送 mgr
public class NIotSignalMessager {
    public static let shared = NIotSignalMessager()
    public weak var requestSender: NIotPlayer?
    
    public func sendAction(_ action: SignalingAction, completionHandler: @escaping ([String: Any]?, Error?) -> Void) {
        NIotLogger.info("[信令透传] action: \(action.actionCmd)")
        guard let str = dicValueString(["data": action.actionCmd]) else {
            NIotLogger.error("[信令透传] 数据发送失败")
            completionHandler(nil, NSError.init(domain: "", code: 0, userInfo: [NSLocalizedDescriptionKey: "数据发送失败"]))
            return
        }
        sendActionStr(str, completionHandler: completionHandler)
    }
    
    public func sendActionStr(_ actionStr: String, completionHandler: @escaping ([String: Any]?, Error?) -> Void) {
        NIotLogger.info("[信令透传] action string: \(actionStr)")
        guard let requestSender = requestSender,
              let data = actionStr.data(using: .utf8)
        else {
            NIotLogger.error("[信令透传] 数据发送失败")
            completionHandler(nil, NSError.init(domain: "", code: 0, userInfo: [NSLocalizedDescriptionKey: "数据发送失败"]))
            return
        }
        requestSender.send(data, sequence: true) { responseData, error, isFinished in
            DispatchQueue.main.async {
//                ["data": ["unknown cmd": -2]]
                if let data = responseData {
                    let jsonStr = String.init(data: data, encoding: .utf8)
                    if jsonStr?.contains("unknown cmd") ?? false {
                        NIotLogger.error("[信令透传]\(actionStr) 未知命令, jsonStr: \(jsonStr)")
                        completionHandler(nil, NSError.init(domain: "", code: 0, userInfo: [NSLocalizedDescriptionKey: "unknown cmd"]))
                    } else if let json = try? JSONSerialization.jsonObject(with: data, options: .fragmentsAllowed) as? [String: Any] {
                        NIotLogger.info("[信令透传]\(actionStr) json: \(json), error: \(error?.localizedDescription ?? "nil")")
                        completionHandler(json, error)
                    } else {
                        NIotLogger.error("[信令透传]\(actionStr) 数据解析失败, jsonStr: \(jsonStr) error: \(error?.localizedDescription ?? "nil")")
                        completionHandler(nil, NSError.init(domain: "", code: 0, userInfo: [NSLocalizedDescriptionKey: "数据解析失败"]))
                    }
                } else {
                    NIotLogger.error("[信令透传]\(actionStr) 返回失败, error: \(error?.localizedDescription ?? "nil")")
                    completionHandler(nil, error)
                }
                
            }
            isFinished.pointee = true
        }
    }
    
    /// set
    public func ptzFingerTrack(_ point: CGPoint, completionHandler: @escaping (String) -> Void) {
        sendAction(.ptzFingerTrack(point)) { json, error in
            if let result = json?["result"] as? String {
                if result == "0" {
                    completionHandler("")
                } else if let err = ZoomCorrelativeError.init(rawValue: result) {
                    completionHandler(result)
                }
            } else {
                completionHandler(error?.localizedDescription ?? "")
            }
        }
    }
    
    public func getZoomScale(completionHandler: @escaping (_ zoomScale: Double?, _ maxScale: Int?, _ lockFocusTimeout: Int?) -> Void) {
        sendAction(.getZoomScale) { json, error in
            NIotLogger.info("[光学变焦] json:\(json ?? [:]), error:\(error?.localizedDescription ?? "")")
            /// 锁焦时间，默认 2 分钟
            var lockFocusTimeout: Int?
            if let info = json?["more_info"] as? [String: Any] {
                if let valueStr = info["cali_timeout"] as? String,
                   let value = valueStr.doubleValue {
                    lockFocusTimeout = Int(ceil(value/60))
                    NIotLogger.info("[光学变焦] 获取锁焦时间：\(value)s")
                }
            }
            if let zoomScale = json?["result"] as? String {
                
                let zoomScaleValue = zoomScale.doubleValue ?? Double(zoomScale)
                
                if let zoomScaleValue = zoomScaleValue {
                    NIotLogger.info("[光学变焦] 获取变焦倍数：\(zoomScaleValue)")
                    if zoomScaleValue >= 0 {
                        let maxScale = json?["version"] as? Int
                        completionHandler(zoomScaleValue, maxScale, lockFocusTimeout)
                    } else {
                        NIotLogger.error("[光学变焦] 变焦倍数<0, zoomScaleValue=\(zoomScaleValue)")
                        completionHandler(nil, nil, lockFocusTimeout)
                    }
                } else {
                    NIotLogger.error("[光学变焦] 数据解析失败：zoomScale->zoomScaleValue 获取值失败, zoomScale=\(zoomScale)")
                    completionHandler(nil, nil, lockFocusTimeout)
                }
            } else {
                // 光学变焦最小值为 1
                completionHandler(nil, nil, lockFocusTimeout)
                NIotLogger.error("[光学变焦] 获取变焦倍数失败：\(error?.localizedDescription ?? "")")
            }
        }
    }
    
    public func getTFCardStatus(tid: String, completionHandler: @escaping (PMSDCardStorage?) -> Void) {
        sendAction(.getTFCardStatus) { json, error in
            guard let json = json else {
                completionHandler(nil)
                return
            }
            let storage = PMSDCardStorage.deserialize(from: json)
            if let storage = storage, storage.state != nil, let json = storage.toJSON() {
                DeviceMgr.writeDeviceOption(devID: tid, key: kUserDefaultsDeviceTFCardState, value: json)
                NIotLogger.info("[TFCardState] tid: \(tid), write: \(json)")
            }
            completionHandler(storage)
        }
    }
    
    public func formatTFCard(completionHandler: @escaping (Bool) -> Void) {
        sendAction(.formatTFCard) { json, error in
            completionHandler(json?["result"] as? String == "0")
        }
    }
}
