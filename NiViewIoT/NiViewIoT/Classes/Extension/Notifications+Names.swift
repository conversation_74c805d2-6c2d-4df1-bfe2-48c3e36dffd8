//
//  Notifications+Names.swift
//  NiView
//
//  Created by nice on 2021/6/3.
//  Copyright © 2021 nice. All rights reserved.
//

import Foundation

public
extension Notification.Name {
    
    static let NIot_DevicePropertyDidUpdate = NSNotification.Name("NIot_DevicePropertyDidUpdate")
    
    static let NIot_LogoutAction = NSNotification.Name("NIot_LogoutAction")
    // 2.0 直播收到 -1003 状态码后通知 viewController 重试播放
    static let retryPlayLiveFor2_0 = NSNotification.Name("retryPlayLiveFor2_0")
    /// 格式化结果回调
    static let formatTFCardResult = NSNotification.Name("formatTFCardResult")
    
}
