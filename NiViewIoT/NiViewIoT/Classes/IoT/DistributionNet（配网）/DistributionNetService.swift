//
//  DistributionNetService.swift
//  NiView
//
//  Created by apple on 2022/2/11.
//  Copyright © 2022 nice. All rights reserved.
//

import UIKit
//import IoTVideo.IVNetConfig

public protocol DistributionNetService {
    
    func generateQRCodeImageWith(wifi: String, pwd: String, extraInfo: [String: String]?, size: CGSize) -> UIImage?
    
    
    
}

class DistributionNetService_v1: DistributionNetService {
    
    static let shared: DistributionNetService = DistributionNetService_v1()
    
    func generateQRCodeImageWith(wifi: String, pwd: String, extraInfo: [String: String]? = nil, size: CGSize) -> UIImage? {
        let isChinese = getCurrentLanguage() == "zh" || getCurrentLanguage() == "zh-Hant"
        return IVNetConfig.qrCode.createQRCode(
            withWifiName: wifi,
            wifiPassword: pwd,
            language: isChinese ? .CN : .EN,
            token: "",
            extraInfo: extraInfo,
            qrSize: size
        )
    }
}


class DistributionNetService_v2: DistributionNetService {
    static let shared: DistributionNetService = DistributionNetService_v2()
    
    func generateQRCodeImageWith(wifi: String, pwd: String, extraInfo: [String: String]? = nil, size: CGSize) -> UIImage? {
        return nil
    }
}
