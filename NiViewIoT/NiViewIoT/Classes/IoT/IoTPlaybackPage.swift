//
//  IoTPlaybackPage.swift
//  NiView
//
//  Created by apple on 2022/1/10.
//  Copyright © 2022 nice. All rights reserved.
//

import UIKit
//import IoTVideo

/// 回放文件分页
@objcMembers
public class IoTPlaybackPage<PBItem>: NSObject {
    /// 当前页码索引
    public let pageIndex: UInt32
    /// 总页数
    public let totalPage: UInt32
    /// 回放文件数组
    public let items: [PBItem]
    
    public init(pageIndex: UInt32, totalPage: UInt32, items: [PBItem]) {
        self.pageIndex = pageIndex
        self.totalPage = totalPage
        self.items = items
    }
}

/// 回放文件
public protocol PlaybackItemRepresentable: NSObject {
    /// 回放文件起始时间（秒），用UTC+0表示
    var startTime: TimeInterval { get set }
    /// 回放文件结束时间（秒），用UTC+0表示
    var endTime: TimeInterval { get set }
    /// 回放文件持续时间（秒），duration = endTime - startTime
    var duration: TimeInterval { get set }
    /// 回放文件类型（例如手动录像、人形侦测等，由设备端应用层定义）
    var type: String { get set }
    /// 文件名 2.0 用到
    var fileName: String? { get set }
    
    func isEqualTo(_ other: PlaybackItemRepresentable) -> Bool
}

extension PlaybackItemRepresentable {
    public func isEqualTo(_ other: PlaybackItemRepresentable) -> Bool {
        return startTime == other.startTime
        && endTime == other.endTime
        && duration == other.duration
        && type == other.type
    }
}

extension IVPlaybackItem: PlaybackItemRepresentable {
    public var fileName: String? {
        get { nil }
        set { }
    }
}

extension TIoTLocalFileModel: PlaybackItemRepresentable {
    
    public var startTime: TimeInterval {
        get {
            guard let start_time = start_time, let startValue = TimeInterval(start_time) else {
                return 0
            }
            return startValue
        }
        set {}
    }
    public var endTime: TimeInterval {
        get {
            guard let end_time = end_time, let endValue = TimeInterval(end_time) else {
                return 0
            }
            return endValue
        }
        set {}
    }
    public var duration: TimeInterval {
        get { endTime - startTime }
        set {}
    }
    public var type: String {
        get { file_type?.string ?? "" }
        set {
            guard let type = TIoTLocalFileModel.FileType.type(with: newValue) else { return }
            file_type = type
        }
    }
    
    public var fileName: String? {
        get { file_name }
        set { file_name = newValue }
    }
     
}
