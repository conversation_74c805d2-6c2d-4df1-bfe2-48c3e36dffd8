//
//  IoTCloudService.swift
//  NiView
//
//  Created by apple on 2022/1/11.
//  Copyright © 2022 nice. All rights reserved.
//

import UIKit

/// 云存服务层
public protocol IoTPlaybackService {
    
    static var shared: IoTPlaybackService { get }
    
    // MARK: - SD Card records
    
    /// 获取SD卡录像日历打点
    /// - Parameters:
    ///   - tid: 设备 id
    ///   - pageIndex: page index
    ///   - countPerPage: 每页数量
    ///   - startTime: 开始时间，毫秒
    ///   - endTime: 结束时间，毫秒
    ///   - channel_v2: [2.0接口]通道
    ///   - monthDate: [2.0接口]时间
    ///   - completionHandler: 回调
    func getSDCardRecordDateList(
        ofDevice tid: String,
        pageIndex: UInt32,
        countPerPage: UInt32,
        startTime: TimeInterval,
        endTime: TimeInterval,
        channel_v2: Int,
        monthDate: Date,
        completionHandler: @escaping PlaybackDateCallback
    )
    
    /// 获取SD卡录像列表
    /// - Parameters:
    ///   - tid: 设备 id
    ///   - pageIndex: page index
    ///   - countPerPage: 每页数量
    ///   - startTime: 开始时间，毫秒
    ///   - endTime: 结束时间，毫秒
    ///   - filterType: 录像类型（””：全部；”pir”：移动侦测；”key”：按键唤醒；”live”：主动唤醒；”alltime”：全时录像）
    ///   - channel_v2: [2.0接口]通道，年月，格式：yyyymm   
    ///   - completionHandler: 回调
    func getSDCardPlaybackList(
        ofDevice tid: String,
        pageIndex: UInt32,
        countPerPage: UInt32,
        startTime: TimeInterval,
        endTime: TimeInterval,
        filterType: String?,
        ascendingOrder: Bool,
        channel_v2: Int,
        completionHandler: @escaping PlaybackListCallback
    )
    
    
    // MARK: - Cloud records
    
    /// 查询有云存视频的日期列表
    /// - Parameters:
    ///   - tid: 设备 id
    ///   - timezone: 相对于0时区的秒数，例如东八区28800
    ///   - completionHandler: 回调
    func getCloudVideoDateList(withDeviceId tid: String, timezone: Int, completionHandler: @escaping ([String]) -> Void)
     
    /// 获取事件列表
    /// - Parameters:
    ///   - tid: 设备 id
    ///   - startTime: 事件告警开始UTC时间, 一般为当天开始时间， 单位秒
    ///   - endTime: 事件告警结束UTC时间，获取更多应传入当前列表的最后一个事件的开始时间(事件列表按时间逆序排列)
    ///   - pageSize: 本次最多查询多少条记录，取值范围 [1 - 50]
    ///   - typeMasks: 筛选指定类型的事件掩码数组
    ///   - validCloudStorage: 是否只返回有效云存期内的事件
    ///   - completionHandler: 回调
    func getCloudEventList(
        withDeviceId: String,
        startTime: TimeInterval,
        endTime: TimeInterval,
        pageSize: Int,
        filterTypeMask: [NSNumber]?,
        validCloudStorage: Bool,
        completionHandler: ((CloudEventInfoRepresentable?, Error?) -> Void)?
    )
    
    /// 获取回放 m3u8 播放地址
    func getCloudVideoPlayAddress(
        withDeviceId: String,
        startTime: TimeInterval,
        endTime: TimeInterval,
        videoUrl_v2: String?,
        completionHandler: ((IVCSPlayInfo?, Error?) -> Void)?
    )
    
    /// 根据时间删除事件
    /// - Parameters:
    ///   - tid: 设备 id
    ///   - alarmTimes: 事件开始时间(startTime)的数组 [uint64_t], 单位秒
    ///   - completionHandler: 回调
    func deleteCloudEvents(withDeviceId: String, alarmTimes: [NSNumber], completionHandler: CompletionHandler?)
}
