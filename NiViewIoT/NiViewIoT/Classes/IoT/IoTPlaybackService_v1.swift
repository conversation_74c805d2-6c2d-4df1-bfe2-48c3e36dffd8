//
//  IoTPlaybackService_v1.swift
//  NiView
//
//  Created by apple on 2022/1/11.
//  Copyright © 2022 nice. All rights reserved.
//

import UIKit
import NiViewIoT.NIot_IVVAS_Private

class IoTPlaybackService_v1: IoTPlaybackService {
    
    static let shared: IoTPlaybackService = IoTPlaybackService_v1()
    
    // MARK: - SD Card records
    
    func getSDCardRecordDateList(
        ofDevice tid: String,
        pageIndex: UInt32,
        countPerPage: UInt32,
        startTime: TimeInterval,
        endTime: TimeInterval,
        channel_v2: Int = 0,
        monthDate: Date,
        completionHandler: @escaping PlaybackDateCallback
    ) {
        IVPlaybackPlayer.getDateList(
            ofDevice: tid,
            pageIndex: pageIndex,
            countPerPage: countPerPage,
            startTime: startTime,
            endTime: endTime
        ) { (playbackPage, error) in
            guard let items = playbackPage?.items, !items.isEmpty, error == nil else {
                completionHandler(nil, error)
                return
            }
            
            let page = IoTPlaybackPage.init(
                pageIndex: playbackPage?.pageIndex ?? 0,
                totalPage: playbackPage?.totalPage ?? 0,
                items: items
            )
            completionHandler(page, error)
        }
    }
    /// ascendingOrder 目前不生效
    func getSDCardPlaybackList(
        ofDevice tid: String,
        pageIndex: UInt32,
        countPerPage: UInt32,
        startTime: TimeInterval,
        endTime: TimeInterval,
        filterType: String?,
        ascendingOrder: Bool,
        channel_v2: Int = 0,
        completionHandler: @escaping PlaybackListCallback
    ) {
        IVPlaybackPlayer.getPlaybackList(
            ofDevice: tid,
            pageIndex: pageIndex,
            countPerPage: countPerPage,
            startTime: startTime,
            endTime: endTime,
            filterType: filterType,
            ascendingOrder: true
        ) { (playbackPage, error) in
            guard let items = playbackPage?.items, !items.isEmpty, error == nil else {
                completionHandler(nil, error)
                return
            }
            
            let page = IoTPlaybackPage.init(
                pageIndex: playbackPage?.pageIndex ?? 0,
                totalPage: playbackPage?.totalPage ?? 0,
                items: items.map {
                    NIotPlaybackItem.init(
                        startTime: $0.startTime,
                        endTime: $0.endTime,
                        duration: $0.duration,
                        type: $0.type
                    )
                }
            )
            completionHandler(page, error)
        }
    }

    
    
    // MARK: - Cloud records
    
    func getCloudVideoDateList(
        withDeviceId tid: String,
        timezone: Int,
        completionHandler: @escaping ([String]) -> Void
    ) {
        IVVAS.shared.getVideoDateList(withDeviceId: tid, timezone: timezone) { (json, error) in
            guard let jsonModel = json?.decode(IVModel<NVCSMarkItems>.self), error == nil else {
                completionHandler([])
                return
            }
            if jsonModel.code == 0, let dates = jsonModel.data {
                let dateArr = dates.list.map { "\($0)" }
                completionHandler(dateArr)
            } else {
                completionHandler([])
            }
        }
    }
    
    func getCloudEventList(
        withDeviceId tid: String,
        startTime: TimeInterval,
        endTime: TimeInterval,
        pageSize: Int,
        filterTypeMask: [NSNumber]?,
        validCloudStorage: Bool,
        completionHandler: ((CloudEventInfoRepresentable?, Error?) -> Void)?
    ) {
        IVVAS.shared.getEventList(
            withDeviceId: tid,
            startTime: startTime,
            endTime: endTime,
            pageSize: pageSize,
            filterTypeMask: filterTypeMask,
            validCloudStorage: validCloudStorage) { json, error in
                guard let jsonModel = json?.decode(IVModel<IVCSEventsInfo>.self), jsonModel.code == 0,
                      error == nil else {
                    completionHandler?(nil, error)
                    return
                }
                let data = jsonModel.data
                // 配置缩略图 URL
                if let prefix = data?.imgUrlPrefix, let suffix = data?.thumbUrlSuffix {
                    data?.list?.forEach {
                        $0.configThumbnailURL(prefix: prefix, suffix: suffix)
                    }
                }
                completionHandler?(data, nil)
            }
    }
    
    func getCloudVideoPlayAddress(
        withDeviceId tid: String,
        startTime: TimeInterval,
        endTime: TimeInterval,
        videoUrl_v2: String?,
        completionHandler: ((IVCSPlayInfo?, Error?) -> Void)?
    ) {
        IVVAS.shared.getVideoPlayAddress(
            withDeviceId: tid,
            startTime: startTime,
            endTime: endTime) { json, error in
                guard let jsonStr = json,
                      let model = jsonStr.decode(IVModel<IVCSPlayInfo>.self)
                else {
                    completionHandler?(nil, NSError(error: "数据解析失败"))
                    return
                }
                guard model.code == 0, let info = model.data else {
                    let err = NSError(
                        domain: "com.niview.error",
                        code: model.code,
                        userInfo: [NSLocalizedDescriptionKey: model.msg ?? "获取数据失败"]
                    )
                    completionHandler?(nil, err)
                    return
                }
                completionHandler?(info, nil)
            }
    }
    
    func deleteCloudEvents(
        withDeviceId tid: String,
        alarmTimes: [NSNumber],
        completionHandler: CompletionHandler?
    ) {
        IVVAS.shared.deleteEvents(
            withDeviceId: tid,
            alarmTimes: alarmTimes,
            responseHandler: completionHandler
        )
    }
    

}
