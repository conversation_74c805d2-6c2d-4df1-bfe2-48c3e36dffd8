//
//  IoTCloudService_v2.swift
//  NiView
//
//  Created by apple on 2022/1/11.
//  Copyright © 2022 nice. All rights reserved.
//

import UIKit
import HandyJSON
import TIoTLinkVideo
//import TIoTLinkKit

class IoTPlaybackService_v2: IoTPlaybackService {
    
    static let shared: IoTPlaybackService = IoTPlaybackService_v2()
    
    // MARK: - SD Card records
    
    func getSDCardRecordDateList(
        ofDevice name: String,
        pageIndex: UInt32,
        countPerPage: UInt32,
        startTime: TimeInterval,
        endTime: TimeInterval,
        channel_v2: Int = 0,
        monthDate: Date,
        completionHandler: @escaping PlaybackDateCallback
    ) {
        let cmd = "{\"data\":{\"get_clock_in\": \"get\",\"start_time\":0,\"end_time\":\(String(format: "%ld", endTime))}}"
        IoT.sdk_2.service.sendData(toDevice: name, data: cmd.data(using: .utf8)!, withResponse: { data, error in
            guard error == nil else {
                completionHandler(nil, error!)
                return
            }
            guard let data = data,
                  let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
                  let dict = json["data"] as? [String: Any],
                  let dateArray = dict["date"] as? [[String: Any]]
            else {
                completionHandler(nil, NSError.init(domain: "", code: 0, userInfo: [NSLocalizedDescriptionKey: "数据解析失败"]))
                return
            }
            var dateTimeArr: [NSNumber] = []
             // 遍历日期数组
             for yearObject in dateArray {
                 for year in yearObject.keys {
                     if let monthObject = yearObject[year] as? [String: Int] {
                         for month in monthObject.keys {
                             if let decimalism = monthObject[month] {
                                 let binaryStr = self.getBinaryStringFromDecimal(decimalism)
                                 let datesInMonth = self.getDatesFromBinaryString(binaryStr, withYearAndMonth: "\(year)\(month)")
                                 let dateTimeArrInMonth = datesInMonth.compactMap { dateStr -> NSNumber? in
                                     let fmt = DateFormatter()
                                     fmt.dateFormat = "yyyy-MM-dd"
                                     guard let date = fmt.date(from: dateStr) else {
                                         return nil
                                     }
                                     return NSNumber.init(value: date.timeIntervalSince1970)
                                 }
                                 dateTimeArr.append(contentsOf: dateTimeArrInMonth)
                             }
                         }
                     }
                 }
             }
            let page = IoTPlaybackPage.init(pageIndex: 0, totalPage: 0, items: dateTimeArr)
            completionHandler(page, nil)
             
        })
    }
    
     
    func getSDCardPlaybackList(
        ofDevice name: String,
        pageIndex: UInt32,
        countPerPage: UInt32,
        startTime: TimeInterval,
        endTime: TimeInterval,
        filterType: String?,
        ascendingOrder: Bool,
        channel_v2: Int = 0,
        completionHandler: @escaping PlaybackListCallback
    ) {
        let startTimeStr = String(format: "%.0f", startTime)
        let endTimeStr = String(format: "%.0f", endTime)
        // 设备那边SDK 不传 type， 所以这里把 type 放在 end_time 后面，
        // 169418880 9filterType9pageIndex9countPerPage
        // 9->分隔符
        let typeStr = "9\(filterType ?? "88")"
        let pageStr = "9\(String(format: "%02d", locale: Locale(identifier: "en_US"), pageIndex))"
        var countPerPageStr = "\(countPerPage)"
        if countPerPage > 100 {
            countPerPageStr = ""
        }
        let countStr = "9\(countPerPageStr)"
        let fmtStr = endTimeStr + typeStr + pageStr + countStr
        let cmd = "action=inner_define&channel=\(channel_v2)&cmd=get_file_list&start_time=\(startTimeStr)&end_time=\(fmtStr)"
        
        TIoTCoreXP2PBridge.sharedInstance().getCommandRequest(withAsync: name, cmd: cmd, timeout: 2_000_000) { jsonStr in
            guard !jsonStr.isEmpty, jsonStr != "{\"status\":\"0\"}" else {
                let page = IoTPlaybackPage.init(pageIndex: 0, totalPage: 0, items: [NIotPlaybackItem]())
                completionHandler(page, nil)
                return
            }
            guard let models = [TIoTLocalFileModel].deserialize(from: jsonStr, designatedPath: "file_list") else {
                completionHandler(nil, NSError.init(domain: "", code: 0, userInfo: [NSLocalizedDescriptionKey: "数据解析失败"]))
                return
            }
            let page = IoTPlaybackPage.init(
                pageIndex: 0,
                totalPage: 0,
                items: models.compactMap{$0}.map {
                    NIotPlaybackItem.init(
                        startTime: $0.startTime,
                        endTime: $0.endTime,
                        duration: $0.duration,
                        type: $0.type
                    )
                }
            )
            completionHandler(page, nil)
        }
    }
    
    
    // MARK: - Cloud records
    
    func getCloudVideoDateList(
        withDeviceId tid: String,
        timezone: Int,
        completionHandler: @escaping ([String]) -> Void
    ) {
        NIotNetworking.shared.describeCloudStorageDate(tid: tid) { json, error in
            
            if let resDict = json as? [String: Any],
               resDict["code"] as? Int == 0,
               let data = resDict["data"] as? [String: Any],
               let dateStrings = data["data"] as? [String] {
                let fmt = DateFormatter()
                fmt.dateFormat = "yyyy-MM-dd"
                fmt.timeZone = TimeZone(abbreviation: "UTC")
                let result = dateStrings.compactMap { dateStr -> String? in
                    guard let date = fmt.date(from: dateStr) else { return nil }
                    return "\(date.timeIntervalSince1970)"
                }
                completionHandler(result)
            } else {
                completionHandler([])
            }
            
        }
    }
    
    func getCloudEventList(
        withDeviceId tid: String,
        startTime: TimeInterval,
        endTime: TimeInterval,
        pageSize: Int,
        filterTypeMask: [NSNumber]?,
        validCloudStorage: Bool,
        completionHandler: ((CloudEventInfoRepresentable?, Error?) -> Void)?
    ) {
        var eventId = ""
        if let filterType = filterTypeMask?.first?.intValue {
            switch filterType {
            case 1: eventId = "_sys_id1_data"
            case 131072: eventId = "_sys_id16_data"
            default: break
            }
        }
        NIotNetworking.shared.describeCloudStorageEvents(tid: tid, size: "999", eventId: eventId, startTime: String(format: "%.0f", startTime), endTime: String(format: "%.0f", endTime)) { json, error in
            if let resDict = json as? [String: Any], resDict["code"] as? Int == 0 {
                guard let data = resDict["data"] as? [String: Any] else {
                    completionHandler?(nil, nil)
                    return
                }
                guard let info = TIoTCloudEventsInfo.deserialize(from: data) else {
                    completionHandler?(nil, NSError.init(domain: "", code: 0, userInfo: [NSLocalizedDescriptionKey: "数据解析失败"]))
                    return
                }
                info._events?.forEach { event in
                    event.tid = tid
                    event.VideoURL_v2 = info.VideoURL_v2
                }
                completionHandler?(info, nil)
            } else {
                completionHandler?(nil, NSError(error: NVLang("common_request_api_error")))
            }
        }
    }
    
    func getCloudVideoPlayAddress(
        withDeviceId tid: String,
        startTime: TimeInterval,
        endTime: TimeInterval,
        videoUrl_v2: String?,
        completionHandler: ((IVCSPlayInfo?, Error?) -> Void)?
    ) {
        let expireTime = String(format: "%.0f", Date().timeIntervalSince1970 + 3600)
        var params: [String: Any] = [
            "expireTime": expireTime
        ]
        let startTimeStr = String(format: "%.0f", startTime)
        let endTimeStr = String(format: "%.0f", endTime)
        var videoURL: String?
        if let videoUrl_v2 = videoUrl_v2 {
            videoURL = "\(videoUrl_v2)?starttime_epoch=\(startTimeStr)&endtime_epoch=\(endTimeStr)" // 后台处理
        } else {
            params["tid"] = tid
            params["startTime"] = startTimeStr
            params["endTime"] = endTimeStr
        }
        
        NIotNetworking.shared.generateSignedVideoURL(tid: tid, videoURL: videoURL, expireTime: expireTime, startTime: startTimeStr, endTime: endTimeStr) { json, error in
            if let resDict = json as? [String: Any],
               resDict["code"] as? Int == 0,
               let data = resDict["data"] as? [String: Any],
               let signedVideoUrl = data["signedVideoURL"] as? String {
                var info = IVCSPlayInfo()
                info.startTime = Int(startTime)
                info.endTime = Int(endTime)
                info.url = signedVideoUrl
                completionHandler?(info, nil)
            } else {
                completionHandler?(nil, error)
            }
        }
    }
    
    func deleteCloudEvents(
        withDeviceId tid: String,
        alarmTimes: [NSNumber],
        completionHandler: CompletionHandler?
    ) {
        
    }
    
    

    /// 从十进制数字获取二进制字符串
    private func getBinaryStringFromDecimal(_ d: Int) -> String {
        var binaryStr = ""
        var decimalism = d
        while decimalism != 0 {
            binaryStr = "\(decimalism%2)" + binaryStr
            if decimalism/2 < 1 {
                break
            }
            decimalism = decimalism / 2
        }
        if binaryStr.count % 4 != 0 {
            let appendCount = 4 - binaryStr.count % 4
            var appendStr = ""
            for _ in 0..<appendCount {
                appendStr += "0"
            }
            binaryStr = appendStr + binaryStr
        }
        return binaryStr
    }
    
    
    /// 从二进制字符串获取日期列表
    /// - Parameters:
    ///   - binary: 二进制字符串，从低位到高位每一比特代表月份的第几天是否有录像；例如：0010000010000000 表示8号和14号有录像；
    ///   - time: 年月字符串
    /// - Returns: 日期列表
    private func getDatesFromBinaryString(_ binary: String, withYearAndMonth time: String) -> [String] {
        var dateArr: [String] = []
        let yearIndex = time.index(time.startIndex, offsetBy: 4)
        let year = String(time[time.startIndex..<yearIndex])
        let month = String(time[yearIndex..<time.endIndex])
        var mBinary = binary
        let totalCount = binary.count
        while !mBinary.isEmpty {
            let indicator = mBinary.removeLast()
            if indicator == "1" {
                let date = String(format: "%@-%@-%02ld", year, month, totalCount - mBinary.count)
                dateArr.append(date)
            }
        }
        return dateArr
    }
    
    
}
