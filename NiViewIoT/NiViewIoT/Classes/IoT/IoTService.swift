//
//  IoTService.swift
//  NiView
//
//  Created by apple on 2022/1/10.
//  Copyright © 2022 nice. All rights reserved.
//

import UIKit

enum IoT {
    case sdk_1, sdk_2
    
    var service: IoTService {
        switch self {
        case .sdk_1: return IoTService_v1.shared
        case .sdk_2: return IoTService_v2.shared
        }
    }
    var playbackService: IoTPlaybackService {
        switch self {
        case .sdk_1: return IoTPlaybackService_v1.shared
        case .sdk_2: return IoTPlaybackService_v2.shared
        }
    }
    
    var distributionNetService: DistributionNetService {
        switch self {
        case .sdk_1: return DistributionNetService_v1.shared
        case .sdk_2: return DistributionNetService_v2.shared
        }
    }
}

public extension NVDeviceModel {
    var service: IoTService {
        isV2Device ? IoT.sdk_2.service : IoT.sdk_1.service
    }
    var playbackService: IoTPlaybackService {
        isV2Device ? IoT.sdk_2.playbackService : IoT.sdk_1.playbackService
    }
    var distributionNetService: DistributionNetService {
        isV2Device ? IoT.sdk_2.distributionNetService : IoT.sdk_1.distributionNetService
    }
}

public typealias CompletionHandler = (_ json: String?, _ error: Error?) -> Void
public typealias DataResponse = (_ data: Data?, _ error: Error?) -> Void
public typealias LinkStatusCallback = (_ status: Int) -> Void
public typealias PlaybackListCallback = (IoTPlaybackPage<NIotPlaybackItem>?, Error?) -> Void
public typealias PlaybackDateCallback = (IoTPlaybackPage<NSNumber>?, Error?) -> Void


/// IoT 服务层
public protocol IoTService {
    
    static var shared: IoTService { get }
    
    
    // MARK: - 读写
    
    /// 获取设备物模型属性
    /// - Parameters:
    ///   - tid: 设备ID
    ///   - path: 路径（JSON的叶子节点）
    ///   - completionHandler: 回调
    func readProperty(ofDevice tid: String, path: String, completionHandler: CompletionHandler?)
    
    /// 设置设备物模型属性
    /// - Parameters:
    ///   - tid: 设备ID
    ///   - path: 路径（JSON的叶子节点）
    ///   - json: 内容（JSON的具体字符串）
    ///   - completionHandler: 回调
    func writeProperty(ofDevice tid: String, path: String, json: String, completionHandler: CompletionHandler?)
    
    /// 执行动作
    /// - Parameters:
    ///   - tid: 设备ID
    ///   - path: 路径（JSON的叶子节点）
    ///   - json: 内容（JSON的具体字符串）
    ///   - completionHandler: 回调
    func takeAction(ofDevice tid: String, path: String, json: String, completionHandler: CompletionHandler?)
    
    
    // MARK: - 信令数据透传
    
    /// 信令数据透传给设备（单次数据回传）
    ///
    /// 不需要建立通道连接，数据经由服务器转发，适用于实时性不高、数据小于`MAX_DATA_SIZE`、需要回传的场景，如获取信息。
    /// - note: 完成回调条件：收到ACK错误、消息超时 或 有数据回传
    /// - Parameters:
    ///   - tid: 设备ID
    ///   - data: 数据内容，data.length不能超过`MAX_DATA_SIZE`
    ///   - response: 回调
    func sendData(toDevice tid: String, data: Data, withResponse response: DataResponse?)
    
    
    // MARK: - SD Card
    
    /// 格式化TF卡，格式化结果通过物模型监听 storage.stVal.state 判断，详情查看 文档-物模型 说明
    /// - Parameters:
    ///   - tid: 设备 id
    ///   - completionHandler: 回调
    func formatTFCardOfDevice(tid: String, completionHandler: CompletionHandler?)
    
    
    // MARK: - Device
    
    /// 获取设备最新版本
    func getNewVersion(ofDevice model: NVDeviceModel, completionHandler: @escaping CompletionHandler)
    
    /// 获取设备最新版本
    /// - Parameters:
    ///   - tid: 设备 id
    ///   - currentOTAVersion: 设备当前版本
    ///   - completionHandler: 回调
    func getDeviceNewVersion(tid: String, currentOTAVersion: String, completionHandler: @escaping CompletionHandler)
    
    /// 设备升级
    ///
    /// - note: 设备升级需要先唤醒设备，设备升级进度超时建议不小于5分钟，格式化结果通过物模型监听 Action.doUpgrade 判断，详情查看 文档-物模型 说明
    /// - Parameters:
    ///   - tid: 设备 id
    ///   - completionHandler: 回调
    func upgradeDevice(tid: String, completionHandler: @escaping CompletionHandler)
    
    /// 重启设备
    /// - Parameters:
    ///   - tid: 设备 id
    ///   - completionHandler: 回调
    func rebootDevice(tid: String, completionHandler: @escaping CompletionHandler)
    
    // MARK: - Convenience
    
    // GET
    
    /// 获取设备的所有物模型信息
    func getAllModelProperties(ofDevice tid: String, completionHandler: @escaping (PropertyModel?, Error?) -> Void)
    /// 获取设备的所有物模型信息 JSON
    func getAllModelPropertiesJSONString(ofDevice tid: String, completionHandler: @escaping (String?, Error?) -> Void)
    
    /// 获取设备在线状态
    func getOnlineStatus(ofDevice tid: String, completionHandler: @escaping (PMOnlineStatus?, Error?) -> Void)
    
    /// 获取设备 SD 存储卡信息
    func getSDCardStorageInfo(ofDevice tid: String, completionHandler: @escaping (PMSDCardStorage?, Error?) -> Void)
    
    /// 获取设备的 WiFi 信息
    func getWiFiInfo(ofDevice tid: String, completionHandler: @escaping (PMWirelessModInfo?, Error?) -> Void)
    
    /// 获取设备的 MccInfo 信息
    func getMccInfo(ofDevice deviceModel: NVDeviceModel, completionHandler: @escaping (String?, String?, Error?) -> Void)
    
    /// 检查设备是否在ota升级中
    func checkDeviceIsOTAUpgrading(_ deviceModel: NVDeviceModel, completionHandler: @escaping (Bool) -> Void)
    
    // SET
    
    /// 设置物模型某一选项值
    func setProperty(ofDevice model: NVDeviceModel, value: Any, path: PropertyModelPath, completionHandler: CompletionHandler?)
    
    /// 同步设备时区
    func setTimezone(ofDevice tid: String, timezone: String, completionHandler: CompletionHandler?)
    
    /// 开启云存图片上传功能
    func enableCloudImageUpload(ofDevice model: NVDeviceModel)
    /// 云存缩略图
    func setCloudThumbnail(ofDevice tid: String, completionHandler: CompletionHandler?)
    
    /// 设置预设位
    func setPresetPositionInfo(ofDevice tid: String, info: [String: Any], completionHandler: CompletionHandler?)
    
    // 自定义信令
    
    ///  获取低电保护百分比
    /// - Parameters:
    ///   - tid: 设备 id
    ///   - completionHandler: 回调
    func getBatteryProtectPercent(ofDevice model: NVDeviceModel, completionHandler: @escaping (Int?) -> Void)
    
    /// 删除设备
    /// - Parameters:
    ///   - tid: 设备 id
    ///   - completionHandler: 回调
    func deleteDevice(tid: String, completionHandler: @escaping CompletionHandler)
    
    /// 声光警戒
    /// - Parameters:
    ///   - tid: 设备 id
    ///   - completionHandler: 回调
    func doSoundLightAlarm(tid: String, completionHandler: @escaping CompletionHandler)
    
    
    /// 获取对讲人数
    func getTalkUsers(ofDevice model: NVDeviceModel, completionHandler: @escaping (Int?, Error?) -> Void)
}

