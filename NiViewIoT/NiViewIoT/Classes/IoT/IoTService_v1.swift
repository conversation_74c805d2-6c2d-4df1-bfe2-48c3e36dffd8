//
//  IoT1.swift
//  NiView
//
//  Created by apple on 2022/1/10.
//  Copyright © 2022 nice. All rights reserved.
//

import UIKit
import HandyJSON
//import NiViewIoT.NIot_IoTVideo_Private

extension NSError {
    /// 快捷创建一个 NSError 对象
    public convenience init(error: String) {
        self.init(domain: "com.NiView.error", code: 0, userInfo: [NSLocalizedDescriptionKey: error])
    }
}

class IoTService_v1: NSObject, IoTService {
    
    private let messageMgr = IVMessageMgr.sharedInstance
    
    static let shared: IoTService = IoTService_v1()
    
    override init() {
        super.init()
        messageMgr.delegate = self
    }
    
    // MARK: - 读写
    
    func readProperty(ofDevice tid: String, path: String, completionHandler: CompletionHandler? = nil) {
        messageMgr.readProperty(ofDevice: tid, path: path, completionHandler: completionHandler)
    }
    
    func writeProperty(ofDevice tid: String, path: String, json: String, completionHandler: CompletionHandler? = nil) {
        messageMgr.writeProperty(ofDevice: tid, path: path, json: json, completionHandler: completionHandler)
    }
    
    func takeAction(ofDevice tid: String, path: String, json: String, completionHandler: CompletionHandler?) {
        messageMgr.takeAction(ofDevice: tid, path: path, json: json, completionHandler: completionHandler)
    }
    
    // MARK: - 信令数据透传
    
    func sendData(toDevice tid: String, data: Data, withResponse response: DataResponse? = nil) {
        messageMgr.sendData(toDevice: tid, data: data, withResponse:response)
    }
    
    // MARK: - SD Card
    
    func formatTFCardOfDevice(tid: String, completionHandler: CompletionHandler? = nil) {
        takeAction(ofDevice: tid, path: kAction + "tfFormat", json: "{\"stVal\":1}", completionHandler: completionHandler)
    }
    
    // MARK: - Device
    
    func getNewVersion(ofDevice model: NVDeviceModel, completionHandler: @escaping CompletionHandler) {
        guard let currentVer = model.propertyModel?.versionInfo?.swVer else {
            return completionHandler(nil, nil)
        }
        getDeviceNewVersion(tid: model.device.tid, currentOTAVersion: currentVer) { json, error in
            guard let jsonStr = json, error == nil else {
                return completionHandler(nil, error)
            }
            let jsonModel = jsonStr.decode(IVModel<Dictionary<String, String>>.self)
            guard let dataDict = jsonModel?.data, jsonModel?.code == 0 else {
                return completionHandler(nil, NVError(code: jsonModel?.code ?? -1, errorMsg: jsonModel?.msg ?? ""))
            }
            
            // 升级版本通知
            var newVer = ""
            if let new = dataDict["version"] {
                newVer = new
            }
            if newVer != currentVer, newVer.length > 0 {
                completionHandler(newVer, nil)
            } else {
                completionHandler(nil, nil)
            }
        }
    }
    
    func getDeviceNewVersion(tid: String, currentOTAVersion: String, completionHandler: @escaping CompletionHandler) {
        IVDeviceMgr.queryDeviceNewVersionWidthDevieId(tid, currentVersion: currentOTAVersion, language: nil, responseHandler: completionHandler)
    }
    
    func upgradeDevice(tid: String, completionHandler: @escaping CompletionHandler) {
        takeAction(ofDevice: tid, path: kAction + kProOTAVersion, json: "{\"stVal\":\"\"}") { [weak self] (json, error) in
            guard let self = self, error == nil else {
                completionHandler(nil, error)
                return
            }
            self.takeAction(ofDevice: tid, path: kAction + kProOTAUpgrade, json: "{\"stVal\":1}", completionHandler: completionHandler)
        }
    }
    
    // 重启设备
    func rebootDevice(tid: String, completionHandler: @escaping CompletionHandler) {
        takeAction(ofDevice: tid, path: kAction + kProReboot, json: "{\"stVal\":1}", completionHandler: completionHandler)
    }
    
    // MARK: - Convenience
    
    // GET
    
    func getAllModelProperties(ofDevice tid: String, completionHandler: @escaping (PropertyModel?, Error?) -> Void) {
        readProperty(ofDevice: tid, path: "") { json, error in
            completionHandler(PropertyModel_v1.deserialize(from: json), error)
        }
    }
    func getAllModelPropertiesJSONString(ofDevice tid: String, completionHandler: @escaping (String?, Error?) -> Void) {
        readProperty(ofDevice: tid, path: "", completionHandler: completionHandler)
    }
    
    func getOnlineStatus(ofDevice tid: String, completionHandler: @escaping (PMOnlineStatus?, Error?) -> Void) {
        let path = kProReadonly + kProOnline
        readProperty(ofDevice: tid, path: path) { json, error in
            guard let dict = json?.stringValueDic(),
                  let stVal = dict[kProReadOnlyKey] as? Int,
                  let status = PMOnlineStatus(rawValue: stVal) else {
                      completionHandler(nil, error)
                      return
                  }
            completionHandler(status, error)
        }
    }
    
    func getSDCardStorageInfo(ofDevice tid: String, completionHandler: @escaping (PMSDCardStorage?, Error?) -> Void) {
        let path = kProReadonly + kProStorage
        readProperty(ofDevice: tid, path: path) { json, error in
            completionHandler(PMSDCardStorage.deserialize(from: json, designatedPath: kProReadOnlyKey), error)
        }
    }
    
    func getWiFiInfo(ofDevice tid: String, completionHandler: @escaping (PMWirelessModInfo?, Error?) -> Void) {
        let path = kProReadonly + kProWifiInfo
        readProperty(ofDevice: tid, path: path) { json, error in
            completionHandler(PMWirelessModInfo.deserialize(from: json, designatedPath: kProReadOnlyKey), error)
        }
    }
    
    func getMccInfo(ofDevice deviceModel: NVDeviceModel, completionHandler: @escaping (String?, String?, Error?) -> Void) {
        readProperty(ofDevice: deviceModel.device.tid, path: PropertyModelPath.MccInfo.path_v1) { json, error in
            let dict = json?.stringValueDic()
            let value = dict?[kProWritableKey] as? String
            completionHandler(value, PropertyModelPath.MccInfo.path_v1, error)
        }
    }
    
    /// 检查设备是否在ota升级中
    func checkDeviceIsOTAUpgrading(_ deviceModel: NVDeviceModel, completionHandler: @escaping (Bool) -> Void) {
        sendData(toDevice: deviceModel.device.tid, data: "{\"data\":{\"get_host_state\":\"\"}}".data(using: .utf8)!, withResponse: { (data, error) in
            DispatchQueue.main.async {
                if let dict = data?.string(with: .utf8)?.stringValueDic(), let dataDict = dict["data"] as? [String: String] {
                    if let otaStatusRes = dataDict["get_host_state"], otaStatusRes == "100" || otaStatusRes == "30" {
                        completionHandler(true)
                    } else {
                        completionHandler(false)
                    }
                } else {
                    completionHandler(false)
                }
            }
        })
    }
    // SET
    
    func setProperty(ofDevice model: NVDeviceModel, value: Any, path: PropertyModelPath, completionHandler: CompletionHandler?) {
        guard let json = [kProWritableKey: value].toString() else {
            completionHandler?(nil, NSError(error: "invalid json error."))
            return
        }
        let path = kProWritable + path.path_v1
        writeProperty(ofDevice: model.device.tid, path: path, json: json, completionHandler: completionHandler)
    }
    
    func setTimezone(ofDevice tid: String, timezone: String, completionHandler: CompletionHandler?) {
        guard let json = [kProWritableKey: timezone].toString() else {
            completionHandler?(nil, NSError(error: "invalid json error."))
            return
        }
        let path = kProWritable + kProTimezone
        writeProperty(ofDevice: tid, path: path, json: json, completionHandler: completionHandler)
    }
    
    
    func enableCloudImageUpload(ofDevice model: NVDeviceModel) {
        if let pm = model.propertyModel as? PropertyModel_v1,
           let dict = pm.ProWritable?._almEvtSetting?.setVal as? [String: Any],
           let uploadImgEnable = dict["uploadImgEna"] as? Int,
           uploadImgEnable != 1 {
            //云存缩略图
            setCloudThumbnail(ofDevice: model.device.tid, completionHandler: nil)
        }
    }
    
    func setCloudThumbnail(ofDevice tid: String, completionHandler: CompletionHandler?) {
        let path = kProWritable + kProAlmEvtSetting + ".setVal.uploadImgEna"
        writeProperty(ofDevice: tid, path: path, json: "1", completionHandler: completionHandler)
    }
    
    func setPresetPositionInfo(ofDevice tid: String, info: [String: Any], completionHandler: CompletionHandler?) {
        guard let json = [kProWritableKey: info].toString() else {
            completionHandler?(nil, NSError(error: "invalid json error."))
            return
        }
        let path = kProWritable + kProPresetPosInfo
        writeProperty(ofDevice: tid, path: path, json: json, completionHandler: completionHandler)
    }
    
    // 自定义信令
    
    func getBatteryProtectPercent(ofDevice model: NVDeviceModel, completionHandler: @escaping (Int?) -> Void) {
        sendData(toDevice: model.device.tid, data: "{\"data\":{\"getBatteryProtectPercent\":\"\"}}".data(using: .utf8)!) { data, error in
            if let dict = data?.string(with: .utf8)?.stringValueDic(),
               let dataDict = dict["data"] as? [String: String],
               let str = dataDict["getBatteryProtectPercent"],
               let value = Int(str) {
                completionHandler(value)
            } else {
                completionHandler(nil)
            }
        }
    }
    
    func deleteDevice(tid: String, completionHandler: @escaping CompletionHandler) {
        sendData(toDevice: tid, data: "{\"data\":{\"deleteDevice\":\"\"}}".data(using: .utf8)!) { data, error in
            completionHandler(nil, error)
        }
    }
    
    func doSoundLightAlarm(tid: String, completionHandler: @escaping CompletionHandler) {
        sendData(toDevice: tid, data: "{\"data\":{\"do_s_l_alarm\":\"\"}}".data(using: .utf8)!, withResponse: { (data, error) in
            completionHandler(data?.string(with: .utf8), error)
        })
    }
    
    func getTalkUsers(ofDevice model: NVDeviceModel, completionHandler: @escaping (Int?, Error?) -> Void) {
        completionHandler(model.operationValue.talkerNum, nil)
    }
}

extension IoTService_v1: IVMessageDelegate {
    func didUpdateProperty(_ json: String, path: String, deviceId: String) {
        if path == "ProReadonly",
           let readOnly = PropertyModel_v1.ModelProReadonly.deserialize(from: json),
           let NID = readOnly.NID?.stVal as? String, NID == "",
           let funcSet = readOnly.funcList?.stVal as? Int, funcSet == 0 {
            NIotLogger.error("[PropertyModel] property model is empty, skip notifing.")
            return
        }
        NotificationCenter.default.post(
            name: .NIot_DevicePropertyDidUpdate,
            object: nil,
            userInfo: [
                "json": json,
                "path": path,
                "deviceId": deviceId
            ]
        )
    }
}
