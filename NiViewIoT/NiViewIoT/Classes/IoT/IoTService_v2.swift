//
//  IoT2.swift
//  NiView
//
//  Created by apple on 2022/1/10.
//  Copyright © 2022 nice. All rights reserved.
//

import UIKit
import TIoTLinkVideo


class IoTService_v2: IoTService {
    
    
    static let shared: IoTService = IoTService_v2()

    func readProperty(ofDevice tid: String, path: String, completionHandler: CompletionHandler?) {
        NIotLogger.info("[IoTService] readProperty tid:\(tid) path:\(path)")
        NIotNetworking.shared.describeDeviceData(tid: tid, path: path) { json, error in
            if let resDict = json as? [String: Any],
               resDict["code"] as? Int == 0,
               let data = resDict["data"] as? [String: Any] {
                var jsonStr: String?
                if let json = data["data"] as? String {
                    jsonStr = json
                } else if let value = data["Value"] as? [String: Any] {
                    jsonStr = value.toString()
                } else if let value = data["Value"] as? String {
                    jsonStr = value
                } else {
                    jsonStr = data.toString()
                }
                completionHandler?(jsonStr, nil)
                NIotLogger.info("[IoTService] readProperty tid:\(tid) path:\(path) json:\(String(describing: jsonStr))")
            } else {
                completionHandler?(nil, NSError(error: NVLang("common_request_api_error")))
                NIotLogger.error("[IoTService] readProperty tid:\(tid) path:\(path)  error: \(error)")
            }
        }
    }
    
    func writeProperty(ofDevice tid: String, path: String, json: String, completionHandler: CompletionHandler?) {
        NIotLogger.info("[IoTService] writeProperty tid:\(tid) path:\(path) json:\(json)")
        NIotNetworking.shared.controlDeviceData(tid: tid, data: json) { json, error in
            if let resDict = json as? [String: Any],
               resDict["code"] as? Int == 0 {
                completionHandler?(nil, nil)
                NIotLogger.info("[IoTService] writeProperty tid:\(tid) path:\(path) success")
            } else {
                completionHandler?(nil, NSError(error: NVLang("common_request_api_error")))
                NIotLogger.error("[IoTService] writeProperty tid:\(tid) path:\(path) error: \(error)")
            }
            
        }
    }
    
    func takeAction(ofDevice tid: String, path: String, json: String, completionHandler: CompletionHandler?) {
        NIotLogger.info("[IoTService] takeAction tid:\(tid) actionId:\(path) inputParams:\(json)")
        NIotNetworking.shared.callDeviceActionSync(tid: tid, actionId: path, inputParams: json) { json, error in
            if let resDict = json as? [String: Any],
               resDict["code"] as? Int == 0,
               let data = resDict["data"] as? [String: Any],
               let outputParams = data["outputParams"] as? String,
               let strData = outputParams.data(using: .utf8),
               let output = try? JSONSerialization.jsonObject(with: strData) as? [String: Any],
                let ret = output["ret"] as? Int, ret == 0
            {
                completionHandler?(nil, nil)
                NIotLogger.info("[IoTService] takeAction tid:\(tid) actionId:\(path) success")
            } else {
                let str = (json as? [String: Any])?.toString()
                completionHandler?(str, NSError(error: NVLang("common_request_api_error")))
                NIotLogger.error("[IoTService] takeAction tid:\(tid) actionId:\(path) failed, response: \(str ?? "")")
            }
        }
    }
    
    func sendData(toDevice tid: String, data: Data, withResponse response: DataResponse?) {
        guard let cmd = data.string(with: .utf8) else {
            response?(nil, NSError(error: "invalid data error."))
            return
        }
        let fullCmd = "action=user_define&channel=0&cmd=\(cmd)"
        NIotLogger.info("[IoTService] sendData tid:\(tid) fullCmd:\(fullCmd)")
        TIoTCoreXP2PBridge.sharedInstance().getCommandRequest(withAsync: tid, cmd: fullCmd, timeout: 2*1000*1000) { json in
            NIotLogger.info("[IoTService] sendData tid:\(tid) fullCmd:\(fullCmd) success")
            response?(json.data(using: .utf8), nil)
        }
    }
    
    func formatTFCardOfDevice(tid: String, completionHandler: CompletionHandler?) {
        guard let json = ["action": 1].toString() else {
            completionHandler?(nil, NSError(error: "invalid json error."))
            return
        }
        takeAction(ofDevice: tid, path: "tfFormat", json: json, completionHandler: completionHandler)
    }
    
    func getNewVersion(ofDevice model: NVDeviceModel, completionHandler: @escaping CompletionHandler) {
        NIotNetworking.shared.appCheckFirmwareUpdate(ofDevice: model) { newVer, error in
            completionHandler(newVer, error)
        }
    }
    
    func getDeviceNewVersion(tid: String, currentOTAVersion: String, completionHandler: @escaping CompletionHandler) {
        // 物模型 hostState 替代
    }
    
    func upgradeDevice(tid: String, completionHandler: @escaping CompletionHandler) {
        
    }
    
    func rebootDevice(tid: String, completionHandler: @escaping CompletionHandler) {
        guard let json = ["action": 1].toString() else {
            completionHandler(nil, NSError(error: "invalid json error."))
            return
        }
        takeAction(ofDevice: tid, path: "dev_reboot", json: json, completionHandler: completionHandler)
    }
    
    func getAllModelProperties(ofDevice tid: String, completionHandler: @escaping (PropertyModel?, Error?) -> Void) {
        readProperty(ofDevice: tid, path: "") { json, error in
            completionHandler(PropertyModel_v2.deserialize(from: json), error)
        }
    }
    
    func getAllModelPropertiesJSONString(ofDevice tid: String, completionHandler: @escaping (String?, Error?) -> Void) {
        readProperty(ofDevice: tid, path: "", completionHandler: completionHandler)
    }
    
    func getOnlineStatus(ofDevice tid: String, completionHandler: @escaping (PMOnlineStatus?, Error?) -> Void) {
        let device = NVEnvironment.shared.devices.first { $0.device.tid == tid }
        completionHandler(device?.propertyModel?.onlineStatus, nil)
    }
    
    func getSDCardStorageInfo(ofDevice tid: String, completionHandler: @escaping (PMSDCardStorage?, Error?) -> Void) {
        readProperty(ofDevice: tid, path: "storage") { json, error in
            // 只是用 PropertyModel_v2 来序列化拿到 sdCardStorage
            guard let pm = PropertyModel_v2.deserialize(from: json) else {
                completionHandler(nil, NSError(error: "invalid json error."))
                return
            }
            completionHandler(pm.sdCardStorage, error)
        }
    }
    
    func getWiFiInfo(ofDevice tid: String, completionHandler: @escaping (PMWirelessModInfo?, Error?) -> Void) {
        readProperty(ofDevice: tid, path: "NetInfo") { json, error in
            // 只是用 PropertyModel_v2 来序列化拿到 PMWirelessModInfo
            guard let pm = PropertyModel_v2.deserialize(from: json) else {
                completionHandler(nil, NSError(error: "invalid json error."))
                return
            }
            completionHandler(pm.wirelessModInfo, error)
        }
    }
    
    func getMccInfo(ofDevice deviceModel: NVDeviceModel, completionHandler: @escaping (String?, String?, Error?) -> Void) {
        readProperty(ofDevice: deviceModel.device.tid, path: PropertyModelPath.MccInfo.path_v2) { json, error in
            completionHandler(json, PropertyModelPath.MccInfo.path_v2, error)
        }
    }
    
    func checkDeviceIsOTAUpgrading(_ deviceModel: NVDeviceModel, completionHandler: @escaping (Bool) -> Void) {
        if let pm = deviceModel.propertyModel as? PropertyModel_v2,
           let hostState = pm.hostStateEnum {
            completionHandler(hostState == .updateFinished || hostState == .startUpFlash)
        } else {
            completionHandler(false)
        }
    }
    
    func setProperty(ofDevice model: NVDeviceModel, value: Any, path: PropertyModelPath, completionHandler: CompletionHandler?) {
        
        var valueToUpdate: Any = value
        if let pm = model.propertyModel as? PropertyModel_v2 {
            switch path {
            case .humanoidTrackingSwitch:
                if var json = pm.SmdCfg.Value as? [String: Any] {
                    json["on_off"] = value
                    valueToUpdate = json
                }
            case .humanoidIndicatorSwitch:
                if var json = pm.SmdCfg.Value as? [String: Any] {
                    json["osd"] = value
                    valueToUpdate = json
                }
            case .pirPostCfgSwitch:
                if var json = pm.MsgPushCfg.Value as? [String: Any] {
                    json["mov_call"] = value
                    valueToUpdate = json
                }
            case .lowPowerPostCfgSwitch:
                if var json = pm.MsgPushCfg.Value as? [String: Any] {
                    json["lowpower_call"] = value
                    valueToUpdate = json
                }
            case .lowPowerProtectionSwitch:
                if var json = pm.PowerMode.Value as? [String: Any] {
                    json["lp_proect"] = value
                    valueToUpdate = json
                }
            case .powerMode:
                if var json = pm.PowerMode.Value as? [String: Any] {
                    json["mode"] = value
                    valueToUpdate = json
                }
            default: break
            }
        }
        guard let json = [path.path_v2: valueToUpdate].toString() else {
            completionHandler?(nil, NSError(error: "invalid json error."))
            return
        }
        writeProperty(ofDevice: model.device.tid, path: "", json: json, completionHandler: completionHandler)
    }
    
    func setTimezone(ofDevice tid: String, timezone: String, completionHandler: CompletionHandler?) {
        guard let json = ["timezone": timezone].toString() else {
            completionHandler?(nil, NSError(error: "invalid json error."))
            return
        }
        writeProperty(ofDevice: tid, path: "", json: json, completionHandler: completionHandler)
    }
    
    func enableCloudImageUpload(ofDevice model: NVDeviceModel) {
        
    }
    
    func setCloudThumbnail(ofDevice tid: String, completionHandler: CompletionHandler?) {
        
    }
    
    func setPresetPositionInfo(ofDevice tid: String, info: [String : Any], completionHandler: CompletionHandler?) {
        guard let json = ["PresetPos": info].toString() else {
            completionHandler?(nil, NSError(error: "invalid json error."))
            return
        }
        writeProperty(ofDevice: tid, path: "", json: json, completionHandler: completionHandler)
    }
    
    // 自定义信令
    
    func getBatteryProtectPercent(ofDevice model: NVDeviceModel, completionHandler: @escaping (Int?) -> Void) {
        // 物模型 lowBatteryProtectval 替代
        if let pm = model.propertyModel as? PropertyModel_v2,
           let value = pm.lowBatteryProtectval.Value as? Int {
            completionHandler(value)
        } else {
            completionHandler(nil)
        }
    }
    
    func deleteDevice(tid: String, completionHandler: @escaping CompletionHandler) {
        guard let json = ["action": 1].toString() else {
            completionHandler(nil, NSError(error: "invalid json error."))
            return
        }
        takeAction(ofDevice: tid, path: "unBindDevice", json: json, completionHandler: completionHandler)
    }
    
    func doSoundLightAlarm(tid: String, completionHandler: @escaping CompletionHandler) {
        // 改为使用直播通道自定义信令，格式同设置高标清，key为do_s_l_alarm
        sendData(toDevice: tid, data: "{\"data\":{\"do_s_l_alarm\":\"\"}}".data(using: .utf8)!, withResponse: { (data, error) in
            completionHandler(data?.string(with: .utf8), error)
        })
    }
    
    func getTalkUsers(ofDevice model: NVDeviceModel, completionHandler: @escaping (Int?, Error?) -> Void) {
        getTalkUsers(tid: model.device.tid) { json, error in
            if let dict = json?.stringValueDic(),
               let str = dict["result"] as? String,
               let talkNum = str.integerValue {
                completionHandler(talkNum, nil)
            } else {
                completionHandler(nil, error)
            }
        }
    }
    
    // MARK: - 2.0 独有
    
    
    func getTalkUsers(tid: String, completionHandler: @escaping CompletionHandler) {
        sendData(toDevice: tid, data: "{\"data\":{\"get_talk_usr\":\"\"}}".data(using: .utf8)!, withResponse: { (data, error) in
            completionHandler(data?.string(with: .utf8), error)
        })
    }
    
    func upgradeOTA(tid: String, completionHandler: @escaping CompletionHandler) {
        guard let json = ["action": 1].toString() else {
            completionHandler(nil, NSError(error: "invalid json error."))
            return
        }
        takeAction(ofDevice: tid, path: "otaUpgrade", json: json, completionHandler: completionHandler)
    }
}
