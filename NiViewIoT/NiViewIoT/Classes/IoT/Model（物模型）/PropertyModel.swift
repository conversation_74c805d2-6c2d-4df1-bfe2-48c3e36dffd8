//
//  DeviceModel.swift
//  NiView
//
//  Created by apple on 2022/1/14.
//  Copyright © 2022 nice. All rights reserved.
//

import UIKit
import HandyJSON

public class PropertyModelHelper {
    public static func parseBatteryInfo(path: String, json: String) -> PMBatteryInfo? {
        var batteryInfo: PMBatteryInfo?
        if path.contains(PropertyModelPath.batteryInfo.path_v1) {
            batteryInfo = PMBatteryInfo.deserialize(from: json, designatedPath: kProBatteryInfo)
        } else if path.contains(PropertyModelPath.batteryInfo.path_v2),
                  let info_v2 = PMBatteryInfo_v2.deserialize(from: json) {
            batteryInfo = PMBatteryInfo()
            batteryInfo!.value = info_v2.value
            batteryInfo!.BatteryPercent = info_v2.percent
        }
        return batteryInfo
    }
}

/// 物模型层
public protocol PropertyModel {
     
    /// 更新物模型
    /// - Parameters:
    ///   - json: 内容（JSON的具体字符串）
    ///   - path: 路径（JSON的叶子节点）
    /// - Returns: 由于物模型某些属性可能为空，为空情况下取不到属性值，也取不到类型，所以不能通过 data.toJSON() 反序列化出对应属性然后给物模型赋值，所以这里直接返回一个新的物模型
    func update(_ json: String, path: String) -> PropertyModel
    
    func update(_ value: Int, path: PropertyModelPath) -> PropertyModel
    
    // MARK: - Custom 自行添加
    
    /// 时区
    var xp2pInfoStr: String? { get set }
    
    // MARK: - Action
    
    var otaVersion: String? { get set }
    var otaUpgrade: Int? { get set }
    var supportReboot: Bool { get }
    // doUpgrade
    // tfFormat
     
    // MARK: - ProConst
    
    var versionInfo: PMVersionInfo? { get set }
    
    // MARK: - ProReadonly
    
    /// 设备在线状态
    var onlineStatus: PMOnlineStatus? { get set }
    /// sd card 信息
    var sdCardStorage: PMSDCardStorage? { get set }
    /// 电池信息
    var batteryInfo: PMBatteryInfo? { get set }
    /// WiFi 信息
    var wirelessModInfo: PMWirelessModInfo? { get set }
    /// 以太网（4G）
    var EthInfo: PMEthInformation? { get set }
    /// 设备能力集
    var funcSet: PMFunctionSet? { get set }
    /// MCU版本
    var mcuVersion: String? { get set }
    /// MOD版本
    var modVersion: String? { get set }
    /// 设备NID
    var NID: String? { get set }
    /// fwVerInfo
    var fwVerInfo: String? { get set }
    /// 设备当前网络模式
    /// 监听物模型ProReadonly.DeviceNetMode（0：wifi，1：4G），判断与netmode状态是否不同，不同时刷新设备状态和UI
    var deviceNetMode: Int? { get set }
    
    
    // MARK: - ProWritable
    
    /// 对讲音量
    var talkVolume: Int? { get set }
    /// 全彩/夜视
    var nightFillMode: PMNightFillMode? { get set }
    /// 指示灯开关
    var ledIndicatorSwitch: PMSwitch? { get set }
    /// 抗闪烁
    var antiFlickerSwitch: PMAntiFlickerSwitch? { get set }
    /// 时区
    var timezone: String? { get set }
    /// 画面上下翻转
    var videoFlipSwitch: PMSwitch? { get set }
    /// pir侦测范围
    var pirSensitisationSwitch: PMPirSensitisationSwitch? { get set }
    /// 人形追踪
    var humanoidTrackingSwitch: PMSwitch? { get set }
    /// 人形标识
    var humanoidIndicatorSwitch: PMSwitch? { get set }
    /// 按键推送开关
    var keyPostCfgSwitch: PMSwitch? { get set }
    /// pir侦测推送
    var pirPostCfgSwitch: PMSwitch? { get set }
    /// 低电报警推送
    var lowPowerPostCfgSwitch: PMSwitch? { get set }
    /// 全时录像
    var allDayRecordSwitch: PMSwitch? { get set }
    /// 低电保护
    var lowPowerProtectionSwitch: PMSwitch? { get set }
    /// 功耗模式
    var powerMode: PMPowerMode? { get set }
    /// 摇头机预置位信息
    var presetPosInfo: [String: Any]? { get set }
    /// 声光模式
    var slMode: PMSoundLightMode? { get set }
    /// 4g设备用户私卡情况下，值为 1 时，系统设置新增云存上传开关，默认隐藏
    var csUpload: Int? { get set }
    /// 时区
    var MccInfo: String? { get set }
    /// 设备当前网络模式
    var devCurNetMode: String? { get set }
    
}

/// 在线状态
public enum PMOnlineStatus: Int, HandyJSONEnum {
    case offline, online, dormant
}

/// WiFi 信息
public class PMWirelessModInfo: HandyJSON {
    /// MAC地址
    public var mac: String?
    /// 信号强度，范围：0-100
    public var rssi: Int?
    /// 当前连接的WIFI
    public var current_ap: String?
    // IP地址
    public var current_ip: String?
    /// Mod版本
    public var version: String?
    public var _operator: String?
    public var imei: String?
    public var iccid: String?
    public required init() {}
    public func mapping(mapper: HelpingMapper) {
        mapper <<< self._operator <-- "operator"
    }
}

/// 以太网（4G)
public class PMEthInformation: HandyJSON {
    /// MAC地址
    public var mac: String?
    ///
    public var ip: String?
    
    public required init() {}
}

/// SD 卡信息
public class PMSDCardStorage: HandyJSON {
    /// TF状态
    public enum Status: Int, HandyJSONEnum {
        /// 设备未插入TF卡
        case notInserted
        /// 设备已插入TF卡，TF卡容量变化时回调此值
        case normal
        /// TF卡文件格式无法识别(仅支持fat32), 需要格式化TF卡
        case unrecognized
        /// 格式化TF卡失败
        case formatFailed
        /// 正在格式化TF卡
        case formatting
        /// 格式化TF卡成功
        case formatSuccessful
        /// 当前有其他用户正在观看录像回放，无法进行格式化
        case otherWatching
        /// TF卡为只读状态,无法录像,请尝试格式化
        case readonly
    }
    
    public var state: Status?
    public var total: Double?
    public var remain: Double?
    public required init() {}
}

/// 版本信息
public class PMVersionInfo: HandyJSON {
    /// 设备SDK版本
    public var sdkVer: String?
    /// 设备固件版本
    public var swVer: String?
    public var hwVer: String?
    public required init() {}
}

/// 电池信息
public class PMBatteryInfo: HandyJSON {
    /// 电池状态
    public enum Status: Int, HandyJSONEnum {
        /// 未知
        case unknown
        /// 电池供电
        case normal
        /// 低电
        case lowAlarm
        /// 充电中
        case charging
        /// 充电满
        case chargend
        /// USB供电
        case usb
        /// 关机
        case powerOff
        
        public var localizedString: String {
            switch self {
            case .normal: return NVLang("device_battery_normal")
            case .lowAlarm: return NVLang("device_battery_lowpower")
            case .charging: return NVLang("device_battery_charging")
            case .chargend: return NVLang("device_battery_chargend")
            case .usb: return NVLang("device_battery_usb")
            default: return ""
            }
        }
    }
    /// 原始值，高 8 位为充电类型，低 8 位为电池状态
    public var value: Int?
    public var BatteryPercent: Int?
    
    public var BatteryStatus: Status? {
        guard let value = value else { return nil }
        let low8Bit = value & 0xff
        return Status.init(rawValue: low8Bit)
    }
    public var batteryStatHigh8bit: Int {
        let value = value ?? 0
        return (value >> 8) & 0xff
    }
    /// 太阳能充电
    public var isSolorCharging: Bool { (batteryStatHigh8bit & 0x01) == 1 }
    // USB充电
    public var isUSBCharging: Bool { (batteryStatHigh8bit >> 1 & 0x01) == 1 }
    
    public required init() {}
    public func mapping(mapper: HelpingMapper) {
        mapper <<< self.value <-- "BatteryStat"
    }
}

/// 声光模式，value 为该模式下对应的数值
public class PMSoundLightMode {
    
    public enum Mode: Int {
        /// 红外
        case infraredRay
        /// 全彩
        case fullColor
        /// 夜间警报
        case nightAlarm
        /// 全天警报
        case allDayAlarm
        /// 节能红外
        case energySaving_infraredRay
        /// 节能全彩
        case energySaving_fullColor
        
        public var key: String {
            switch self {
            case .infraredRay: return "alarm_ir"
            case .fullColor: return "alarm_color"
            case .nightAlarm: return "alarm_night"
            case .allDayAlarm: return "alarm_all_day"
            case .energySaving_infraredRay: return "alarm_energySaving_ir"
            case .energySaving_fullColor: return "alarm_energySaving_color"
            }
        }
        /// 在字节高 4 位值中表示的值
        public var value: Int8 {
            switch self {
            case .infraredRay: return 0x00
            case .fullColor: return 0x10
            case .nightAlarm: return 0x20
            case .allDayAlarm: return 0x30
            case .energySaving_infraredRay: return 0x40
            case .energySaving_fullColor: return 0x50
            }
        }
    }
    /// 模式
    public let mode: Mode
    /// 配置值
    public let settingValue: Int
    /// 字节值
    public let byteValue: Int
      
    /// 初始化
    /// - Parameters:
    /// - byteValue: 单字节值，8个位表示，高4位表示选中的模式，低4位表示相应模式下的子选项配置，
    /// 用slMode / 16获取选中的模式，slMode % 16获取子选项的配置
    public init?(byteValue: Int) {
        let high4Bit = byteValue / 16
        let low4Bit = byteValue % 16
        guard let mode = Mode.init(rawValue: high4Bit) else {
            return nil
        }
        self.mode = mode
        self.settingValue = low4Bit
        self.byteValue = byteValue
    }
    
}
/// 开关，0=关，1=开
public enum PMSwitch: Int, HandyJSONEnum {
    case off, on
}
/// 全彩/夜视
public enum PMNightFillMode: Int, HandyJSONEnum {
    case fill, night
}
/// 抗闪烁
public enum PMAntiFlickerSwitch: Int, HandyJSONEnum {
    case off = 50, on = 60
}
/// pir侦测范围
public enum PMPirSensitisationSwitch: Int, HandyJSONEnum {
    case off, near, medium, far
}
///  功耗模式
public enum PMPowerMode: Int, HandyJSONEnum {
    case normal, auto, fullTime
}

/// 设备能力集
public struct PMFunctionSet: OptionSet {
    public init(rawValue: Int) {
        self.rawValue = rawValue
    }
    public let rawValue: Int
    
    public var isValid: Bool {
        return rawValue != 0
    }
    
    public var isSupportOpticalZoom: Bool {
        return contains(.opticalZoom) || contains(.opticalZoomDigital)
    }
    
    public var isSupportAutoFocusAndZoomCalibration: Bool {
        return contains(.opticalZoom) && !contains(.opticalZoomDigital)
    }
    
    ///门铃按键
    public static let call         = PMFunctionSet(rawValue: 1 << 0)
    ///PW键
    public static let power        = PMFunctionSet(rawValue: 1 << 1)
    ///SOS键
    public static let sos          = PMFunctionSet(rawValue: 1 << 2)
    ///防拆报警
    public static let tamper       = PMFunctionSet(rawValue: 1 << 3)
    ///全彩(同时支持红外夜视与全彩夜视时，显示夜视模式配置选项)
    public static let color        = PMFunctionSet(rawValue: 1 << 4)
    ///红外(同时支持红外夜视与全彩夜视时，显示夜视模式配置选项)
    public static let infrared     = PMFunctionSet(rawValue: 1 << 5)
    ///PIR(无FUN_PIR 并且 无FUN_PERSON_TRACK时，隐藏移动侦测灵敏度配置)
    public static let pir          = PMFunctionSet(rawValue: 1 << 6)
    ///TF卡
    public static let sd           = PMFunctionSet(rawValue: 1 << 7)
    ///太阳能充电
    public static let solarEnergy  = PMFunctionSet(rawValue: 1 << 8)
    ///4G
    public static let cellular     = PMFunctionSet(rawValue: 1 << 9)
    ///wifi
    public static let wifi         = PMFunctionSet(rawValue: 1 << 10)
    ///摇头机
    public static let shaking      = PMFunctionSet(rawValue: 1 << 11)
    ///非低功耗设备(UI去掉电池图标，配置中去掉"低电推送")
    public static let noBattDev    = PMFunctionSet(rawValue: 1 << 12)
    ///声光警戒(声光警戒配置，配置开/关，直播界面增加手动触发声光警戒按键)
    public static let SLAlarm      = PMFunctionSet(rawValue: 1 << 13)
    ///人形追踪(人形追踪配置项，配置开/关，保留移动侦测灵敏度配置)
    public static let personTrack  = PMFunctionSet(rawValue: 1 << 14)
    ///是否支持倒挂
    public static let upsideDown   = PMFunctionSet(rawValue: 1 << 15)
    ///功耗
    public static let consumption  = PMFunctionSet(rawValue: 1 << 16)
    ///光学变焦
    public static let opticalZoom  = PMFunctionSet(rawValue: 1 << 17)
    ///手指追踪
    public static let fingerTrack  = PMFunctionSet(rawValue: 1 << 18)
    ///不支持倒挂也不支持翻转
    public static let notUpsideDownOrFlip  = PMFunctionSet(rawValue: 1 << 19)
    ///指示灯
    public static let ledIndicator = PMFunctionSet(rawValue: 1 << 20)
    ///是否不支持对讲能力集，不支持则不显示对讲按钮、不显示设置音量调整
    public static let isIntercomUnsupported = PMFunctionSet(rawValue: 1 << 21)
    ///数字变焦设备没有变焦校准、自动对焦功能，判断逻辑：先判断光学变焦 17，再判断 22，如果有 22，就不支持变焦校准、自动对焦
    public static let opticalZoomDigital = PMFunctionSet(rawValue: 1 << 22)
    ///手指追踪无需标定
    public static let fingerTrackNoCali = PMFunctionSet(rawValue: 1 << 23)
    ///是否开启人形识别框 0 表示开启，1表示关闭
    public static let doNotShowAIRect = PMFunctionSet(rawValue: 1 << 24)
    ///支持节能模式
    public static let energySaving = PMFunctionSet(rawValue: 1 << 25)
    ///AOV设备
    public static let isAOV = PMFunctionSet(rawValue: 1 << 26)
    ///使用直播通道检测 TF 卡
    public static let isUsingLiveChannelToCheckSd = PMFunctionSet(rawValue: 1 << 27)

}
