//
//  PropertyModelPath.swift
//  NiView
//
//  Created by apple on 2023/9/5.
//  Copyright © 2023 nice. All rights reserved.
//

import UIKit

public enum PropertyModelPath: CaseIterable {
    
    // v1 有 v2 没有
    
    /// 设备在线状态
    case onlineStatus
    case otaVersion
    case otaUpgrade
    ///  以太网（4G）
    case EthInfo
    /// MCU版本
    case mcuVersion
    /// MOD版本
    case modVersion
    
    case versionInfo
    /// 全彩/夜视
    case nightFillMode
    /// 按键推送开关
    case keyPostCfgSwitch
    /// 全时录像
    case allDayRecordSwitch
    
    // v1 v2 都有
    
    /// 分辨率
    case resolution
    /// sd card 信息
    case sdCardStorage
    /// 电池信息
    case batteryInfo
    /// WiFi 信息
    case wirelessModInfo
    /// 设备能力集
    case funcSet
    /// 设备NID
    case NID
    /// 对讲音量
    case talkVolume
    /// 指示灯开关
    case ledIndicatorSwitch
    /// 抗闪烁
    case antiFlickerSwitch
    /// 时区
    case timezone
    /// 画面上下翻转
    case videoFlipSwitch
    /// pir侦测范围
    case pirSensitisationSwitch
    /// 人形追踪
    case humanoidTrackingSwitch
    /// 人形标识
    case humanoidIndicatorSwitch
    /// pir侦测推送
    case pirPostCfgSwitch
    /// 低电报警推送
    case lowPowerPostCfgSwitch
    /// 低电保护
    case lowPowerProtectionSwitch
    /// 功耗模式
    case powerMode
    /// 摇头机预置位信息
    case presetPosInfo
    /// 声光模式
    case slMode
    /// 云存上传功能开关
    case csUpload
    /// MccInfo
    case MccInfo
    
    /// 自定义行为，用于设置界面触发事件
    /// 变焦校准
    case operation_ZoomCalibration
    /// 重启设备
    case operation_RebootDevice
    
    func isSame(_ string: String) -> Bool {
        return string == path_v1 || string == path_v2
    }
    
    public var isPostCfg: Bool {
        return self == .keyPostCfgSwitch
        || self == .pirPostCfgSwitch
        || self == .lowPowerPostCfgSwitch
    }
    
    public var isLowPowerCfg: Bool {
        return self == .lowPowerPostCfgSwitch
        || self == .lowPowerProtectionSwitch
    }
    
    public var path_v1: String {
        switch self {
        case .onlineStatus: return "_online"
        case .otaVersion: return "_otaVersion"
        case .otaUpgrade: return "_otaUpgrade"
        case .versionInfo: return "_versionInfo"
        case .EthInfo: return "EthInforMation"
        case .mcuVersion: return "mcuVersion"
        case .modVersion: return "modVersion"
        case .nightFillMode: return "nightFillMode"
        case .keyPostCfgSwitch: return "keyPostCfg"
        case .allDayRecordSwitch: return "allDayRecord"
        
        case .resolution: return "resolution"
        case .sdCardStorage: return "storage"
        case .batteryInfo: return "batteryInfo"
        case .wirelessModInfo: return "WirelessModInfo"
        case .funcSet: return "funcList"
        case .NID: return "NID"
        case .talkVolume: return "talkVolume"
        case .ledIndicatorSwitch: return "ledIndicator"
        case .antiFlickerSwitch: return "antiFlicker"
        case .timezone: return "timezone"
        case .videoFlipSwitch: return "videoFlip"
        case .pirSensitisationSwitch: return "pirSensit"
        case .humanoidTrackingSwitch: return "smdActive"
        case .humanoidIndicatorSwitch: return "humanIndicator"
        case .pirPostCfgSwitch: return "pirPostCfg"
        case .lowPowerPostCfgSwitch: return "lowpowerPostCfg"
        case .lowPowerProtectionSwitch: return "lpProtect"
        case .powerMode: return "powerMode"
        case .presetPosInfo: return "presetPosInfo"
        case .slMode: return "slMode"
        case .csUpload: return "csUpload"
        case .MccInfo: return "ProWritable.MccInfo"
        default: return ""
        }
    }
    
    public var path_v2: String {
        switch self {
        case .onlineStatus: return "_onlineStatus"
        case .otaVersion: return "_otaVersion"
        case .otaUpgrade: return "_otaUpgrade"
        case .versionInfo: return "_versionInfo"
        case .EthInfo: return "EthInforMation"
        case .mcuVersion: return "mcuVersion"
        case .modVersion: return "modVersion"
        case .nightFillMode: return "nightFillMode"
        case .keyPostCfgSwitch: return "keyPostCfg"
        case .allDayRecordSwitch: return "allDayRecord"
        
        case .resolution: return "resolution"
        case .sdCardStorage: return "storage"
        case .batteryInfo: return "BatteryInfo"
        case .wirelessModInfo: return "NetInfo"
        case .funcSet: return "funcList"
        case .NID: return "NID"
        case .talkVolume: return "TalkVolume"
        case .ledIndicatorSwitch: return "LedIndicator"
        case .antiFlickerSwitch: return "AntiFlicker"
        case .timezone: return "timezone"
        case .videoFlipSwitch: return "VideoFlip"
        case .pirSensitisationSwitch: return "PirSensit"
        case .humanoidTrackingSwitch: return "SmdCfg"
        case .humanoidIndicatorSwitch: return "SmdCfg"
        case .pirPostCfgSwitch: return "MsgPushCfg"
        case .lowPowerPostCfgSwitch: return "MsgPushCfg"
        case .lowPowerProtectionSwitch: return "PowerMode"
        case .powerMode: return "PowerMode"
        case .presetPosInfo: return "PresetPos"
        case .slMode: return "slMode"
        case .csUpload: return "csUpload"
        case .MccInfo: return "mccInfo"
        default: return ""
        }
    }
    
    static func from(path: String) -> PropertyModelPath? {
        return allCases.first { $0.path_v2 == path }
    }
}
