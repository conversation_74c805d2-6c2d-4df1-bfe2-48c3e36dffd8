//
//  DeviceModel_v1.swift
//  NiView
//
//  Created by apple on 2022/1/14.
//  Copyright © 2022 nice. All rights reserved.
//

import UIKit
import HandyJSON


class PropertyModel_v1: NSObject, HandyJSON {
    
    class DataAttribute: HandyJSON {
        var stVal: Any?
        var setVal: Any?
        var t: Int64?
        required init() {}
    }
    class ModelAction: HandyJSON {
        var _otaVersion: DataAttribute?
        var _otaUpgrade: DataAttribute?
        var dev_reboot: DataAttribute?
        required init() {}
    }
    class ModelProConst: HandyJSON {
        var _versionInfo: PMVersionInfo?
        required init() {}
    }
    class ModelProReadonly: HandyJSON {
        var _online: DataAttribute?
        var storage: DataAttribute?
        var batteryInfo: DataAttribute?
        var WirelessModInfo: DataAttribute?
        var mcuVersion: DataAttribute?
        var modVersion: DataAttribute?
        var NID: DataAttribute?
        var fwVerInfo: DataAttribute?
        /// 以太网（4G)
        var EthInforMation: DataAttribute?
        
        /// 能力集
        var funcList: DataAttribute?
        var DeviceNetMode: DataAttribute?
        
        required init() {}
    }
    class ModelProWritable: HandyJSON {
        var talkVolume: DataAttribute?
        var nightFillMode: DataAttribute?
        var ledIndicator: DataAttribute?
        var antiFlicker: DataAttribute?
        var timezone: DataAttribute?
        var videoFlip: DataAttribute?
        var pirSensit: DataAttribute?
        /// 人形追踪
        var smdActive: DataAttribute?
        var humanIndicator: DataAttribute?
        var keyPostCfg: DataAttribute?
        var pirPostCfg: DataAttribute?
        var lowpowerPostCfg: DataAttribute?
        var allDayRecord: DataAttribute?
        /// 低电保护
        var lpProtect: DataAttribute?
        /// 功耗模式
        var powerMode: DataAttribute?
        /// 摇头机预置位信息
        var presetPosInfo: DataAttribute?
        
        /// 云存缩略图
        var _almEvtSetting: DataAttribute?
        /// 声光模式
        var slMode: DataAttribute?
        /// 4g设备用户私卡情况下，值为 1 时，系统设置新增云存上传开关，默认隐藏
        var csUpload: DataAttribute?
        
        var MccInfo: DataAttribute?
        
        var devCurNetMode: DataAttribute?
        
        required init() {}
    }
    
    var Action: ModelAction?
    var ProConst: ModelProConst?
    var ProReadonly: ModelProReadonly?
    var ProWritable: ModelProWritable?
    
    required override init() {}
    
}

extension PropertyModel_v1: PropertyModel {
    
    func update(_ value: Int, path: PropertyModelPath) -> PropertyModel {
        if let json = [kProWritableKey: value].toString() {
            return update(json, path: kProWritable+path.path_v1)
        }
        return self
    }
    /// 通过更新字典 value 的方式来更新 model
    func update(_ json: String, path: String) -> PropertyModel {
        autoreleasepool {
            var valueToUpdate: Any = json
            // 0. 如果是字典字符串，则转成字典
            if let dataDict = json.stringValueDic() {
                valueToUpdate = dataDict
            }
            // 1. model 转成 JSON 字典
            guard let currJSON = self.toJSON() else { return self }
            // 2. 拆分 path，对应字典每个层级 key
            let paths = path.split(separator: ".").map { String($0) }
            // 3. 更新 model 字典
            let updatedJSON = updateJSON(currJSON, newValue: valueToUpdate, paths: paths)
            // 4. 根据已更新的 JSON 字典，反序列化 model
            return PropertyModel_v1.deserialize(from: updatedJSON) ?? self
        }
    }
    
    private func updateJSON(_ original: [String: Any], newValue: Any, paths: [String]) -> [String: Any] {
        var original = original
        if let firstPath = paths.first {
            if paths.count > 1 {
                // 1.1 取出第一个路径的字典值，没有原值则直接赋值
                let subDict = (original[firstPath] as? [String: Any]) ?? [String: Any]()
                // 2. 遍历子层级进行赋值
                let newPaths = Array(paths.dropFirst())
                original[firstPath] = updateJSON(subDict, newValue: newValue, paths: newPaths)
            } else {
                // 1.2 只有一个路径，直接赋值
                original[firstPath] = newValue
            }
        } else {
            original = newValue as? [String: Any] ?? [:]
        }
        return original
    }
    
    // MARK: - Custom
    
    var xp2pInfoStr: String? {
        get { nil }
        set {  }
    }
    
    // MARK: - Action
    
    var otaVersion: String? {
        get { Action?._otaVersion?.stVal as? String }
        set { Action?._otaVersion?.stVal = newValue }
    }
    var otaUpgrade: Int? {
        get { Action?._otaUpgrade?.stVal as? Int }
        set { Action?._otaUpgrade?.stVal = newValue }
    }
    var supportReboot: Bool {
        get { Action?.dev_reboot != nil }
    }
    
    // MARK: - ProConst
    
    var versionInfo: PMVersionInfo? {
        get { ProConst?._versionInfo }
        set { ProConst?._versionInfo = newValue }
    }
    
    // MARK: - ProReadonly
    
    var onlineStatus: PMOnlineStatus? {
        get {
            guard let value = ProReadonly?._online?.stVal as? Int,
                  let status = PMOnlineStatus(rawValue: value) else {
                return nil
            }
            return status
        }
        set {
            ProReadonly?._online?.stVal = newValue?.rawValue
        }
    }
    /// sd card 信息
    var sdCardStorage: PMSDCardStorage? {
        get {
            guard let value = ProReadonly?.storage?.stVal as? [String: Any],
                  let storage = PMSDCardStorage.deserialize(from: value) else {
                return nil
            }
            return storage
        }
        set {
            ProReadonly?.storage?.stVal = newValue?.toJSON()
        }
    }
    
    var batteryInfo: PMBatteryInfo? {
        get {
            guard let value = ProReadonly?.batteryInfo?.stVal as? [String: Any],
                  let batteryInfo = PMBatteryInfo.deserialize(from: value) else {
                return nil
            }
            return batteryInfo
        }
        set {
            ProReadonly?.batteryInfo?.stVal = newValue?.toJSON()
        }
    }
    
    var EthInfo: PMEthInformation?{
        get {
            guard let value = ProReadonly?.EthInforMation?.stVal as? [String: Any],
                  let batteryInfo = PMEthInformation.deserialize(from: value) else {
                return nil
            }
            return batteryInfo
        }
        set {
            ProReadonly?.EthInforMation?.stVal = newValue?.toJSON()
        }
    }
    
    var wirelessModInfo: PMWirelessModInfo? {
        get {
            guard let value = ProReadonly?.WirelessModInfo?.stVal as? [String: Any],
                  let batteryInfo = PMWirelessModInfo.deserialize(from: value) else {
                return nil
            }
            return batteryInfo
        }
        set {
            ProReadonly?.WirelessModInfo?.stVal = newValue?.toJSON()
        }
    }
    
    var mcuVersion: String? {
        get { ProReadonly?.mcuVersion?.stVal as? String }
        set { ProReadonly?.mcuVersion?.stVal = newValue }
    }
    
    var modVersion: String? {
        get { ProReadonly?.modVersion?.stVal as? String }
        set { ProReadonly?.modVersion?.stVal = newValue }
    }
    
    var NID: String? {
        get { ProReadonly?.NID?.stVal as? String }
        set { ProReadonly?.NID?.stVal = newValue }
    }
    
    var fwVerInfo: String? {
        get { ProReadonly?.fwVerInfo?.stVal as? String }
        set { ProReadonly?.fwVerInfo?.stVal = newValue }
    }
    
    var deviceNetMode: Int? {
        get { ProReadonly?.DeviceNetMode?.stVal as? Int }
        set { ProReadonly?.DeviceNetMode?.stVal = newValue }
    }
    
    var funcSet: PMFunctionSet? {
        get {
            guard let value = ProReadonly?.funcList?.stVal as? Int else {
                return nil
            }
            return PMFunctionSet(rawValue: value)
        }
        set {
            ProReadonly?.funcList?.stVal = newValue?.rawValue
        }
    }
    
    // MARK: - ProWritable
    
    /// 对讲音量
    var talkVolume: Int? {
        get { ProWritable?.talkVolume?.setVal as? Int }
        set { ProWritable?.talkVolume?.setVal = newValue }
    }
    
    /// 全彩/夜视
    var nightFillMode: PMNightFillMode? {
        get {
            guard let value = ProWritable?.nightFillMode?.setVal as? Int,
                  let status = PMNightFillMode(rawValue: value) else {
                return nil
            }
            return status
        }
        set {
            ProWritable?.nightFillMode?.setVal = newValue?.rawValue
        }
    }
    /// 指示灯开关
    var ledIndicatorSwitch: PMSwitch? {
        get {
            guard let value = ProWritable?.ledIndicator?.setVal as? Int,
                  let status = PMSwitch(rawValue: value) else {
                return nil
            }
            return status
        }
        set {
            ProWritable?.ledIndicator?.setVal = newValue?.rawValue
        }
    }
    /// 抗闪烁
    var antiFlickerSwitch: PMAntiFlickerSwitch? {
        get {
            guard let value = ProWritable?.antiFlicker?.setVal as? Int,
                  let status = PMAntiFlickerSwitch(rawValue: value) else {
                return nil
            }
            return status
        }
        set {
            ProWritable?.antiFlicker?.setVal = newValue?.rawValue
        }
    }
    /// 时区
    var timezone: String? {
        get { ProWritable?.timezone?.setVal as? String }
        set { ProWritable?.timezone?.setVal = newValue }
    }
    /// 画面上下翻转
    var videoFlipSwitch: PMSwitch? {
        get {
            guard let value = ProWritable?.videoFlip?.setVal as? Int,
                  let status = PMSwitch(rawValue: value) else {
                return nil
            }
            return status
        }
        set {
            ProWritable?.videoFlip?.setVal = newValue?.rawValue
        }
    }
    /// pir侦测范围
    var pirSensitisationSwitch: PMPirSensitisationSwitch? {
        get {
            guard let value = ProWritable?.pirSensit?.setVal as? Int,
                  let status = PMPirSensitisationSwitch(rawValue: value) else {
                return nil
            }
            return status
        }
        set {
            ProWritable?.pirSensit?.setVal = newValue?.rawValue
        }
    }
    ///  人形追踪
    var humanoidTrackingSwitch: PMSwitch? {
        get {
            guard let value = ProWritable?.smdActive?.setVal as? Int,
                  let status = PMSwitch(rawValue: value) else {
                return nil
            }
            return status
        }
        set {
            ProWritable?.smdActive?.setVal = newValue?.rawValue
        }
    }
    ///  人形标识
    var humanoidIndicatorSwitch: PMSwitch? {
        get {
            guard let value = ProWritable?.humanIndicator?.setVal as? Int,
                  let status = PMSwitch(rawValue: value) else {
                return nil
            }
            return status
        }
        set {
            ProWritable?.humanIndicator?.setVal = newValue?.rawValue
        }
    }
    /// 按键推送开关
    var keyPostCfgSwitch: PMSwitch? {
        get {
            guard let value = ProWritable?.keyPostCfg?.setVal as? Int,
                  let status = PMSwitch(rawValue: value) else {
                return nil
            }
            return status
        }
        set {
            ProWritable?.keyPostCfg?.setVal = newValue?.rawValue
        }
    }
    /// pir侦测推送
    var pirPostCfgSwitch: PMSwitch? {
        get {
            guard let value = ProWritable?.pirPostCfg?.setVal as? Int,
                  let status = PMSwitch(rawValue: value) else {
                return nil
            }
            return status
        }
        set {
            ProWritable?.pirPostCfg?.setVal = newValue?.rawValue
        }
    }
    /// 低电报警推送
    var lowPowerPostCfgSwitch: PMSwitch? {
        get {
            guard let value = ProWritable?.lowpowerPostCfg?.setVal as? Int,
                  let status = PMSwitch(rawValue: value) else {
                return nil
            }
            return status
        }
        set {
            ProWritable?.lowpowerPostCfg?.setVal = newValue?.rawValue
        }
    }
    /// 全时录像
    var allDayRecordSwitch: PMSwitch? {
        get {
            guard let value = ProWritable?.allDayRecord?.setVal as? Int,
                  let status = PMSwitch(rawValue: value) else {
                return nil
            }
            return status
        }
        set {
            ProWritable?.allDayRecord?.setVal = newValue?.rawValue
        }
    }
    /// 低电保护
    var lowPowerProtectionSwitch: PMSwitch? {
        get {
            guard let value = ProWritable?.lpProtect?.setVal as? Int,
                  let status = PMSwitch(rawValue: value) else {
                return nil
            }
            return status
        }
        set {
            ProWritable?.lpProtect?.setVal = newValue?.rawValue
        }
    }
    /// 功耗模式
    var powerMode: PMPowerMode? {
        get {
            guard let value = ProWritable?.powerMode?.setVal as? Int,
                  let status = PMPowerMode(rawValue: value) else {
                return nil
            }
            return status
        }
        set {
            ProWritable?.powerMode?.setVal = newValue?.rawValue
        }
    }
    
    /// 摇头机预置位信息
    var presetPosInfo: [String: Any]? {
        get { return ProWritable?.presetPosInfo?.setVal as? [String: Any] }
        set { ProWritable?.presetPosInfo?.setVal = newValue }
    }
    /// 声光模式
    var slMode: PMSoundLightMode? {
        get {
            guard let setVal = ProWritable?.slMode?.setVal as? Int,
                  let mode = PMSoundLightMode.init(byteValue: setVal) else {
                return nil
            }
            return mode
        }
        set { 
            ProWritable?.powerMode?.setVal = newValue?.byteValue
        }
    }
    
    /// 4g设备用户私卡情况下，值为 1 时，系统设置新增云存上传开关，默认隐藏
    var csUpload: Int? {
        get { ProWritable?.csUpload?.setVal as? Int }
        set { ProWritable?.csUpload?.setVal = newValue }
    }
    ///
    var MccInfo: String? {
        get { ProWritable?.MccInfo?.setVal as? String }
        set { ProWritable?.MccInfo?.setVal = newValue }
    }
    
    var devCurNetMode: String? {
        get { ProWritable?.devCurNetMode?.setVal as? String }
        set { ProWritable?.devCurNetMode?.setVal = newValue }
    }
    
}
