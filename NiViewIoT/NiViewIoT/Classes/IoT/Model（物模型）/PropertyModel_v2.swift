//
//  PropertyModel_v2.swift
//  NiView
//
//  Created by apple on 2023/8/16.
//  Copyright © 2023 nice. All rights reserved.
//

import UIKit
import HandyJSON


@objcMembers
class PropertyModel_v2: NSObject, HandyJSON {
    
    class DataAttribute: NSObject, HandyJSON {
        var LastUpdate: String?
        var Value: Any?
        required override init() {}
    }
    
    /// 自行添加
    var _onlineStatus = DataAttribute()
    
    
    /// xp2p信息
    var xp2pInfo = DataAttribute()
    /// 云存全时天数
    var cloudStorageDays = DataAttribute()
    /// 云存开关
    var cloudStorageStatus = DataAttribute()
    /// 云存类型
    var cloudStorageType = DataAttribute()
    /// AI 开关
    var aiStatus = DataAttribute()
    /// 存储卡信息
    var storage = DataAttribute()
    /// 固件版本信息
    var _fwVerInfo = DataAttribute()
    /// 能力集
    var funcList = DataAttribute()
    /// 电池信息
    var BatteryInfo = DataAttribute()
    
    var _NID = DataAttribute()
    /// 对讲音量
    var TalkVolume = DataAttribute()
    /// 响铃音量
    var RingVolume = DataAttribute()
    /// 指示灯开关
    var LedIndicator = DataAttribute()
    /// 抗闪烁
    var AntiFlicker = DataAttribute()
    /// 画面翻转
    var VideoFlip = DataAttribute()
    /// PIR灵敏度
    var PirSensit = DataAttribute()
    /// 人形检测配置
    var SmdCfg = DataAttribute()
    /// 推送配置
    var MsgPushCfg = DataAttribute()
    /// 功耗管理
    var PowerMode = DataAttribute()
    /// 声光模式
    var _slMode = DataAttribute()
    /// 摄像头预置位看守位
    var PresetPos = DataAttribute()
    /// 时区
    var _timezone = DataAttribute()
    
    var _mcuVersion = DataAttribute()
    ///
    var _mccInfo = DataAttribute()
    
//    "netInfo4G": {
//        "Value": {
//            "csq": 0,
//            "iccid": "",
//            "imei": "",
//            "imsi": "",
//            "ipAddr": "",
//            "macAddr": "",
//            "mod_name": "",
//            "oper": "",
//            "signal": 0,
//            "version": ""
//        },
//        "LastUpdate": 1699416157401
//    },
//    "netInfoEth": {
//        "Value": {
//            "ipAddr": "",
//            "macAddr": ""
//        },
//        "LastUpdate": 1699416157401
//    },
//    "netInfoWiFi": {
//        "Value": {
//            "ap_wifi": "",
//            "ipAddr": "",
//            "macAddr": "00:00:00:00:00:00",
//            "signal": 100,
//            "version": "20.3.4"
//        },
//        "LastUpdate": 1699416157401
//    },
    /// 网络模块
    var netInfo4G = DataAttribute()
    var netInfoEth = DataAttribute()
    var netInfoWiFi = DataAttribute()
    
    /// 设备 OTA 升级状态
    var hostState = DataAttribute()
    var upgVer = DataAttribute()
    
    var swVer = DataAttribute()
    
    var _csUpload = DataAttribute()
    
    var _devCurNetMode = DataAttribute()
    
    var hostStateEnum: PMHostState_v2? {
        guard let value = hostState.Value as? Int,
              let state = PMHostState_v2(rawValue: value) else {
            return nil
        }
        return state
    }
    
    var lowBatteryProtectval = DataAttribute()
    
    
    required override init() {}
    func mapping(mapper: HelpingMapper) {
        mapper <<< self.xp2pInfo <-- "_sys_xp2p_info"
        mapper <<< self.cloudStorageDays <-- "_sys_cs_days"
        mapper <<< self.cloudStorageStatus <-- "_sys_cs_status"
        mapper <<< self.cloudStorageType <-- "_sys_cs_type"
        mapper <<< self.aiStatus <-- "_sys_ai_status"
        mapper <<< self._NID <-- "NID"
        mapper <<< self._slMode <-- "slMode"
        mapper <<< self._timezone <-- "timezone"
        mapper <<< self._fwVerInfo <-- "fwVerInfo"
        mapper <<< self._mcuVersion <-- "mcuVersion"
        mapper <<< self._csUpload <-- "csUpload"
        mapper <<< self._devCurNetMode <-- "devCurNetMode"
        mapper <<< self._mccInfo <-- "mccInfo"
    }
    
}

public enum PMHostState_v2: Int, HandyJSONEnum {
    /// 设备未在升级或者上个升级流程结束
    case idle
    /// 设备正在下载固件
    case downloadStarted = 10
    /// 设备下载固件完成
    case downloadFinished = 30
    /// 设备正在烧写flash
    case startUpFlash = 45
    /// 设备升级成功，保持一段时间后会变为0
    case updateFinished = 100
}

class PMBatteryInfo_v2: HandyJSON {
    var value: Int?
    var percent: Int?
    required init() {}
    func mapping(mapper: HelpingMapper) {
        mapper <<< self.value <-- "state"
    }
}
/// 人形检测配置
class PMSmdCfg_v2: HandyJSON {
    /// 检测框
    var osd = false
    /// 开关
    var on_off = false
    
    required init() {}
}

/// 推送配置
class PMMsgPushCfg_v2: HandyJSON {
    /// 呼叫推送
    var user_call = false
    /// 移动侦测推送
    var mov_call = false
    /// 低电推送
    var lowpower_call = false
    /// 报警推送
    var alarm_call = false
    
    required init() {}
}

/// 功耗管理
class PMPowerMode_v2: HandyJSON {
    /// 模式
    var mode: PMPowerMode?
    /// 低电保护
    var lp_proect = false
    
    required init() {}
}

/// WiFi 信息
class PMNetInfoWifi: HandyJSON {
    /// 当前连接的WIFI
    var ap_wifi: String?
    /// IPV4地址
    var ipAddr: String?
    /// MAC地址
    var macAddr: String?
    /// 信号强度，范围：0-100
    var signal: Int?
    /// MOD版本
    var version: String?
    
    required init() {}
}
class PMNetInfo4G: HandyJSON {
    var csq: Int?
    var iccid: String?
    var imei: String?
    var imsi: String?
    /// IPV4地址
    var ipAddr: String?
    /// MAC地址
    var macAddr: String?
    /// Mod版本
    var mod_name: String?
    /// 运营商
    var oper: String?
    /// 信号强度，范围：0-100
    var signal: Int?
    /// MOD版本
    var version: String?
    
    required init() {}
}
class PMNetInfoEth: HandyJSON {
    /// IPV4地址
    var ipAddr: String?
    /// MAC地址
    var macAddr: String?
    
    required init() {}
}


extension PropertyModel_v2: PropertyModel {
    
    var deviceNetMode: Int? {
        get {
            nil
        }
        set {
            
        }
    }
    
    func update(_ value: Int, path: PropertyModelPath) -> PropertyModel {
        return update(String(value), path: path.path_v2)
    }
    /// 通过更新字典 value 的方式来更新 model
    func update(_ json: String, path: String) -> PropertyModel {
        autoreleasepool {
            var valueToUpdate: Any = json
            if let value = Int(json), let propertyModelPath = PropertyModelPath.from(path: path) {
                switch propertyModelPath {
                case .humanoidTrackingSwitch:
                    if var json = SmdCfg.Value as? [String: Any] {
                        json["on_off"] = value
                        valueToUpdate = json
                    }
                case .humanoidIndicatorSwitch:
                    if var json = SmdCfg.Value as? [String: Any] {
                        json["osd"] = value
                        valueToUpdate = json
                    }
                case .pirPostCfgSwitch:
                    if var json = MsgPushCfg.Value as? [String: Any] {
                        json["mov_call"] = value
                        valueToUpdate = json
                    }
                case .lowPowerPostCfgSwitch:
                    if var json = MsgPushCfg.Value as? [String: Any] {
                        json["lowpower_call"] = value
                        valueToUpdate = json
                    }
                case .lowPowerProtectionSwitch:
                    if var json = PowerMode.Value as? [String: Any] {
                        json["lp_proect"] = value
                        valueToUpdate = json
                    }
                case .powerMode:
                    if var json = PowerMode.Value as? [String: Any] {
                        json["mode"] = value
                        valueToUpdate = json
                    }
                default: break
                }
            }
            // 0. 如果是字典字符串，则转成字典
            if let dataDict = json.stringValueDic() {
                valueToUpdate = dataDict
            }
            
            // 如果 path 为空，更新整个物模型
            if path.isEmpty {
                updateAllProperties(value: valueToUpdate)
                return self
            }
            // 1. 拆分 path，对应字典每个层级 key
            let paths = path.split(separator: ".").map { String($0) }
            // 2. 根据路径更新对应的属性
            updateProperty(paths: paths, value: valueToUpdate)
            return self
        }
    }
    
    private func updateAllProperties(value: Any) {
        guard let dict = value as? [String: Any] else { return }
        
        // 使用 Mirror 反射获取所有属性
        let mirror = Mirror(reflecting: self)
        for child in mirror.children {
            guard let label = child.label else { continue }
            
            // 移除下划线前缀（如果有）
            var propertyName = label.hasPrefix("_") ? String(label.dropFirst()) : label
            if propertyName == "xp2pInfo" {
                propertyName = "_sys_xp2p_info"
            } else if propertyName == "cloudStorageDays" {
                propertyName = "_sys_cs_days"
            } else if propertyName == "cloudStorageStatus" {
                propertyName = "_sys_cs_status"
            } else if propertyName == "cloudStorageType" {
                propertyName = "_sys_cs_type"
            } else if propertyName == "aiStatus" {
                propertyName = "_sys_ai_status"
            }
            
            // 全部物模型更新是直接赋值
            if let propertyValue = dict[propertyName] as? [String: Any],
               let dataAttribute = DataAttribute.deserialize(from: propertyValue)
            {
                self.setValue(dataAttribute, forKey: label)
            }
        }
    }
    
    private func updateProperty(paths: [String], value: Any) {
        guard let firstPath = paths.first else { return }
        
        // 使用 Mirror 反射获取属性
        let mirror = Mirror(reflecting: self)
        for child in mirror.children {
            guard let label = child.label else { continue }
            
            // 移除下划线前缀（如果有）
            var propertyName = label.hasPrefix("_") ? String(label.dropFirst()) : label
            if propertyName == "xp2pInfo" {
                propertyName = "_sys_xp2p_info"
            } else if propertyName == "cloudStorageDays" {
                propertyName = "_sys_cs_days"
            } else if propertyName == "cloudStorageStatus" {
                propertyName = "_sys_cs_status"
            } else if propertyName == "cloudStorageType" {
                propertyName = "_sys_cs_type"
            } else if propertyName == "aiStatus" {
                propertyName = "_sys_ai_status"
            }
            
            if propertyName == firstPath {
                if paths.count > 1 {
                    // 如果还有子路径，继续递归更新
                    if let dataAttribute = child.value as? DataAttribute,
                       let subValue = value as? [String: Any] {
                        let newPaths = Array(paths.dropFirst())
                        updateProperty(paths: newPaths, value: subValue)
                    }
                } else if let dataAttribute = DataAttribute.deserialize(from: ["Value": value]){
                    self.setValue(dataAttribute, forKey: label)
                }
                break
            }
        }
    }
    
    // MARK: - Custom
    
    var xp2pInfoStr: String? {
        get { xp2pInfo.Value as? String }
        set { xp2pInfo.Value = newValue }
    }
    
    // MARK: - Action
    
    var otaVersion: String? {
        get { nil }
        set {  }
    }
    var otaUpgrade: Int? {
        get { nil }
        set {  }
    }
    var supportReboot: Bool {
        get { true }
    }
    
    // MARK: - ProConst
    
    var versionInfo: PMVersionInfo? {
        get {
            guard let swVer = swVer.Value as? String else {
                return nil
            }
            let info = PMVersionInfo()
            info.swVer = swVer
            return info
        }
        set {
            swVer.Value = newValue?.swVer
        }
    }
    
    // MARK: - ProReadonly
    
    var onlineStatus: PMOnlineStatus? {
        get {
            guard let string = _onlineStatus.Value as? String,
                  let value = Int(string),
                  let status = PMOnlineStatus(rawValue: value) else {
                return nil
            }
            return status
        }
        set {
            guard let value = newValue?.rawValue else { return }
            _onlineStatus.Value = String(value)
        }
    }
    /// sd card 信息
    var sdCardStorage: PMSDCardStorage? {
        get {
            guard let value = storage.Value as? [String: Any],
                  let storage = PMSDCardStorage.deserialize(from: value) else {
                return nil
            }
            return storage
        }
        set {
            storage.Value = newValue?.toJSON()
        }
    }
    
    var batteryInfo: PMBatteryInfo? {
        get {
            guard let value = BatteryInfo.Value as? [String: Any],
                  let info = PMBatteryInfo_v2.deserialize(from: value) else {
                return nil
            }
            let batteryInfo = PMBatteryInfo()
            batteryInfo.value = info.value
            batteryInfo.BatteryPercent = info.percent
            return batteryInfo
        }
        set {
            let info = PMBatteryInfo_v2()
            info.value = newValue?.value
            info.percent = newValue?.BatteryPercent
            BatteryInfo.Value = info.toJSON()
        }
    }
    
    var EthInfo: PMEthInformation? {
        get {
            guard let value = netInfoEth.Value as? [String: Any],
                  let info = PMNetInfoEth.deserialize(from: value) else {
                return nil
            }
            let ethInfo = PMEthInformation()
            ethInfo.mac = info.macAddr
            ethInfo.ip = info.ipAddr
            return ethInfo
        }
        set {
            let info = PMNetInfoEth()
            info.macAddr = newValue?.mac
            info.ipAddr = newValue?.ip
            netInfoWiFi.Value = info.toJSON()
        }
    }
    
    var wirelessModInfo: PMWirelessModInfo? {
        get {
            guard let value = netInfoWiFi.Value as? [String: Any],
                  let info = PMNetInfoWifi.deserialize(from: value) else {
                return nil
            }
            let wirelessModInfo = PMWirelessModInfo()
            wirelessModInfo.mac = info.macAddr
            wirelessModInfo.rssi = info.signal
            wirelessModInfo.current_ap = info.ap_wifi
            wirelessModInfo.current_ip = info.ipAddr
            wirelessModInfo.version = info.version
//            if let value = netInfo4G?.Value as? [String: Any],
//               let info = PMNetInfo4G.deserialize(from: value) {
//                wirelessModInfo.imei = info.imei
//                wirelessModInfo.iccid = info.iccid
//            }
            return wirelessModInfo
        }
        set {
            let info = PMNetInfoWifi()
            info.macAddr = newValue?.mac
            info.signal = newValue?.rssi
            info.ap_wifi = newValue?.current_ap
            info.ipAddr = newValue?.current_ip
            info.version = newValue?.version
//            info.imei = newValue?.imei
//            info.iccid = newValue?.iccid
            netInfoWiFi.Value = info.toJSON()
        }
    }
    
    var net4GInfo: PMNetInfo4G? {
        guard let value = netInfo4G.Value as? [String: Any] else { return nil }
        return PMNetInfo4G.deserialize(from: value)
    }
    
    var mcuVersion: String? {
        get { _mcuVersion.Value as? String }
        set { _mcuVersion.Value = newValue }
    }
    
    var modVersion: String? {
        get { nil }
        set {  }
    }
    
    var NID: String? {
        get { _NID.Value as? String }
        set { _NID.Value = newValue }
    }
    
    var fwVerInfo: String? {
        get { _fwVerInfo.Value as? String }
        set { _fwVerInfo.Value = newValue }
    }
    
    var funcSet: PMFunctionSet? {
        get {
            guard let hexString = funcList.Value as? String,
                  let value = Int(hexString, radix: 16) else {
                NIotLogger.error("无法将能力集字符串转换为UInt64: \(String(describing: funcList.Value))")
                return nil
            }
            return PMFunctionSet(rawValue: value)
        }
        set {
            guard let value = newValue?.rawValue else { return }
            let hexString = String(format: "0x%llx", value)
            funcList.Value = String(hexString)
        }
    }
    
    // MARK: - ProWritable
    
    /// 对讲音量
    var talkVolume: Int? {
        get { TalkVolume.Value as? Int }
        set { TalkVolume.Value = newValue }
    }
    
    /// 全彩/夜视
    var nightFillMode: PMNightFillMode? {
        get { nil }
        set {  }
    }
    /// 指示灯开关
    var ledIndicatorSwitch: PMSwitch? {
        get {
            guard let value = LedIndicator.Value as? Int,
                  let status = PMSwitch(rawValue: value) else {
                return nil
            }
            return status
        }
        set {
            LedIndicator.Value = newValue?.rawValue
        }
    }
    /// 抗闪烁
    var antiFlickerSwitch: PMAntiFlickerSwitch? {
        get {
            guard let value = AntiFlicker.Value as? Int,
                  let status = PMAntiFlickerSwitch(rawValue: value) else {
                return nil
            }
            return status
        }
        set {
            AntiFlicker.Value = newValue?.rawValue
        }
    }
    /// 时区
    var timezone: String? {
        get { _timezone.Value as? String }
        set { _timezone.Value = newValue }
    }
    /// 画面上下翻转
    var videoFlipSwitch: PMSwitch? {
        get {
            guard let value = VideoFlip.Value as? Int,
                  let status = PMSwitch(rawValue: value) else {
                return nil
            }
            return status
        }
        set {
            VideoFlip.Value = newValue?.rawValue
        }
    }
    /// pir侦测范围
    var pirSensitisationSwitch: PMPirSensitisationSwitch? {
        get {
            guard let value = PirSensit.Value as? Int,
                  let status = PMPirSensitisationSwitch(rawValue: value) else {
                return nil
            }
            return status
        }
        set {
            PirSensit.Value = newValue?.rawValue
        }
    }
    ///  人形追踪
    var humanoidTrackingSwitch: PMSwitch? {
        get {
            guard let value = SmdCfg.Value as? [String: Any],
                  let smdCfg = PMSmdCfg_v2.deserialize(from: value) else {
                return nil
            }
            return smdCfg.on_off ? PMSwitch.on : PMSwitch.off
        }
        set {
            guard let value = SmdCfg.Value as? [String: Any],
                  let cfg = PMSmdCfg_v2.deserialize(from: value) else {
                return
            }
            cfg.on_off = newValue == .on
            SmdCfg.Value = cfg.toJSON()
        }
    }
    ///  人形标识
    var humanoidIndicatorSwitch: PMSwitch? {
        get {
            guard let value = SmdCfg.Value as? [String: Any],
                  let smdCfg = PMSmdCfg_v2.deserialize(from: value) else {
                return nil
            }
            return smdCfg.osd ? PMSwitch.on : PMSwitch.off
        }
        set {
            guard let value = SmdCfg.Value as? [String: Any],
                  let cfg = PMSmdCfg_v2.deserialize(from: value) else {
                return
            }
            cfg.osd = newValue == .on
            SmdCfg.Value = cfg.toJSON()
        }
    }
    /// 按键推送开关
    var keyPostCfgSwitch: PMSwitch? {
        get {
            guard let value = MsgPushCfg.Value as? [String: Any],
                  let msgPushCfg = PMMsgPushCfg_v2.deserialize(from: value) else {
                return nil
            }
            return msgPushCfg.user_call ? PMSwitch.on : PMSwitch.off
        }
        set {
            guard let value = MsgPushCfg.Value as? [String: Any],
                  let cfg = PMMsgPushCfg_v2.deserialize(from: value) else {
                return
            }
            cfg.user_call = newValue == .on
            MsgPushCfg.Value = cfg.toJSON()
        }
    }
    /// pir侦测推送
    var pirPostCfgSwitch: PMSwitch? {
        get {
            guard let value = MsgPushCfg.Value as? [String: Any],
                  let msgPushCfg = PMMsgPushCfg_v2.deserialize(from: value) else {
                return nil
            }
            return msgPushCfg.mov_call ? PMSwitch.on : PMSwitch.off
        }
        set {
            guard let value = MsgPushCfg.Value as? [String: Any],
                  let cfg = PMMsgPushCfg_v2.deserialize(from: value) else {
                return
            }
            cfg.mov_call = newValue == .on
            MsgPushCfg.Value = cfg.toJSON()
        }
    }
    /// 低电报警推送
    var lowPowerPostCfgSwitch: PMSwitch? {
        get {
            guard let value = MsgPushCfg.Value as? [String: Any],
                  let msgPushCfg = PMMsgPushCfg_v2.deserialize(from: value) else {
                return nil
            }
            return msgPushCfg.lowpower_call ? PMSwitch.on : PMSwitch.off
        }
        set {
            guard let value = MsgPushCfg.Value as? [String: Any],
                  let cfg = PMMsgPushCfg_v2.deserialize(from: value) else {
                return
            }
            cfg.lowpower_call = newValue == .on
            MsgPushCfg.Value = cfg.toJSON()
        }
    }
    /// 全时录像
    var allDayRecordSwitch: PMSwitch? {
        get { nil }
        set {  }
    }
    /// 低电保护
    var lowPowerProtectionSwitch: PMSwitch? {
        get {
            guard let value = PowerMode.Value as? [String: Any],
                  let powerMode = PMPowerMode_v2.deserialize(from: value) else {
                return nil
            }
            return powerMode.lp_proect ? PMSwitch.on : PMSwitch.off
        }
        set {
            guard let value = PowerMode.Value as? [String: Any],
                  let powerMode = PMPowerMode_v2.deserialize(from: value) else {
                return
            }
            powerMode.lp_proect = newValue == .on
            PowerMode.Value = powerMode.toJSON()
        }
    }
    /// 功耗模式
    var powerMode: PMPowerMode? {
        get {
            guard let value = PowerMode.Value as? [String: Any],
                  let powerMode = PMPowerMode_v2.deserialize(from: value) else {
                return nil
            }
            return powerMode.mode
        }
        set {
            guard let value = PowerMode.Value as? [String: Any],
                  let powerMode = PMPowerMode_v2.deserialize(from: value) else {
                return
            }
            powerMode.mode = newValue
            PowerMode.Value = powerMode.toJSON()
        }
    }
    
    /// 摇头机预置位信息
    var presetPosInfo: [String: Any]? {
        get { return PresetPos.Value as? [String: Any] }
        set { PresetPos.Value = newValue }
    }
    /// 声光模式
    var slMode: PMSoundLightMode? {
        get {
            guard let setVal = _slMode.Value as? Int,
                  let mode = PMSoundLightMode.init(byteValue: setVal) else {
                return nil
            }
            return mode
        }
        set {
            _slMode.Value = newValue?.byteValue
        }
    }
    
    /// 4g设备用户私卡情况下，值为 1 时，系统设置新增云存上传开关，默认隐藏
    var csUpload: Int? {
        get { _csUpload.Value as? Int }
        set { _csUpload.Value = newValue }
    }
    ///
    var MccInfo: String? {
        get { _mccInfo.Value as? String }
        set { _mccInfo.Value = newValue }
    }
    
    var devCurNetMode: String? {
        get { _devCurNetMode.Value as? String }
        set { _devCurNetMode.Value = newValue }
    }
}
