//
//  IVMonitorPlayer+IoT.swift
//  NiView
//
//  Created by apple on 2022/1/12.
//  Copyright © 2022 nice. All rights reserved.
//

import UIKit
//import IoTVideo.IVMonitorPlayer

// 需要明确重载 IVPlayer 遵守的 IoTPlayer 协议内容，否则运行时期间类型推断会有问题，估计是嵌套太多了
extension IVMonitorPlayer: IoTMonitorPlayer {
    
    override var playerDelegateProxy: IoTPlayerDelegateProxy {
        get {
            objc_getAssociatedObject(self, &IoTAssociatedKeys.kPlayerDelegateProxy) as! IoTPlayerDelegateProxy
        }
        set {
            objc_setAssociatedObject(self, &IoTAssociatedKeys.kPlayerDelegateProxy, newValue, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
        }
    }
    
    override var playerDelegate: IoTPlayerDelegate? {
        get {
            playerDelegateProxy.playerDelegate
        }
        set {
            playerDelegateProxy = IoTPlayerDelegateProxy(player: self)
            playerDelegateProxy.playerDelegate = newValue
            delegate = playerDelegateProxy
        }
    }
    
    override var playerDefinition: IoTVideoDefinition {
        get {
            IoTVideoDefinition(rawValue: definition.rawValue) ?? .high
        }
        set {
            definition = IVVideoDefinition(rawValue: newValue.rawValue) ?? .high
        }
    }
    
    override func setPlayerDefinition(_ definition: IoTVideoDefinition, completionHandler: IoTSettingCallback?) {
        setDefinition(IVVideoDefinition(rawValue: definition.rawValue) ?? .high, completionHandler: { error in
            completionHandler?(error)
        })
    }
    
    override var playerStatus: IoTPlayerStatus {
        return IoTPlayerStatus(rawValue: status.rawValue) ?? .stopped
    }
    
    override func startPlay(forceRefresh: Bool) {
        play()
    }
    
    override func stop(cancelReconnect: Bool = false) {
        stop()
    }
    
    override func send(data: Data) -> Bool {
        return send(data)
    }
    
    override func send(data: Data?, sequence: Bool, multiCallback: @escaping IotMsgMultiRspCallback) {
        send(data, sequence: sequence, multiCallback: multiCallback)
    }
    
    override func stopService(_ willDisappear: Bool = false) {
       
    }
}

//@objc protocol IoTPlayer: IoTConnection {
//    /// 视频清晰度
//    var playerDefinition: IoTVideoDefinition { get set }
//}
//    
//IVMonitorPlayer类 继承自IVPlayer类：
//protocol IVMonitorPlayer: IVPlayer { }
//
//IoTMonitorPlayer协议继承了IoTPlayer协议
//protocol IoTMonitorPlayer: IoTPlayer { }
//IVMonitorPlayer遵守IoTMonitorPlayer协议
//extension IVMonitorPlayer: IoTMonitorPlayer { }
//IVPlayer遵守IoTPlayer协议
//extension IVPlayer: IoTPlayer {
//    
//    var playerDefinition: IoTVideoDefinition {
//        get {
//            IoTVideoDefinition(rawValue: definition.rawValue) ?? .high
//        }
//        set {
//            definition = IVVideoDefinition(rawValue: newValue.rawValue) ?? .high
//        }
//    }
//}
//现在有个问题，为什么IVMonitorPlayer访问不了 playerDefinition属性
