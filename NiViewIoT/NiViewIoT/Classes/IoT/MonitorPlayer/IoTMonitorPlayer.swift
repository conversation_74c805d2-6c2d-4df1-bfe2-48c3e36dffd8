//
//  IoTMonitorPlayer.swift
//  NiView
//
//  Created by apple on 2022/1/12.
//  Copyright © 2022 nice. All rights reserved.
//

import UIKit


protocol IoTMonitorPlayer: IoTPlayer {
    
    /// 当前与设备对讲的客户端数量
    var talkerNum: UInt { get }
    
    /// 是否正在对讲
    var isTalking: Bool { get }
    
    /// 开启对讲
    func startTalking()
     
    /// 开启对讲
    func startTalking(_ completionHandler: IoTSettingCallback?)
    
    /// 结束对讲
    func stopTalking()
     
    /// 结束对讲
    func stopTalking(_ completionHandler: IoTSettingCallback?)
}
