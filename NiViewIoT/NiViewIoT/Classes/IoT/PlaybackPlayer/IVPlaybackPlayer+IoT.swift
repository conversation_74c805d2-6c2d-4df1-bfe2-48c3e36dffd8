//
//  IVPlaybackPlayer+IoT.swift
//  NiView
//
//  Created by apple on 2022/1/12.
//  Copyright © 2022 nice. All rights reserved.
//

import UIKit
//import IoTVideo.IVPlaybackPlayer

extension IVPlaybackPlayer: IoTPlaybackPlayer {
    
    var iotPlaybackStrategy: IoTPlaybackStrategy {
        get {
            IoTPlaybackStrategy(rawValue: playbackStrategy.rawValue) ?? .single
        }
        set {
            playbackStrategy = IVPlaybackStrategy(rawValue: newValue.rawValue) ?? .single
        }
    }
    
    func setPlaybackStrategy(_ strategy: IoTPlaybackStrategy, completionHandler: IoTSettingCallback?) {
        guard let ivStrategy = IVPlaybackStrategy(rawValue: strategy.rawValue) else { return }
        setPlaybackStrategy(ivStrategy, completionHandler: completionHandler)
    }
    
    var iotPlaybackItem: PlaybackItemRepresentable? {
        return playbackItem
    }
    
    func setIoTPlaybackItem(_ item: PlaybackItemRepresentable, seekToTime: TimeInterval) {
        guard let playbackItem = item as? IVPlaybackItem else {
            assertionFailure("确保 playbackItem 是 IVPlaybackItem 类型")
            return
        }
        setPlaybackItem(playbackItem, seekToTime: seekToTime)
    }
    
    func seekToTime(_ time: TimeInterval, playbackItem: PlaybackItemRepresentable) {
        guard let item = playbackItem as? IVPlaybackItem else {
            assertionFailure("确保 playbackItem 是 IVPlaybackItem 类型")
            return
        }
        seek(toTime: time, playbackItem: item)
    }
    
    func seekToTime(_ time: TimeInterval, playbackItem: PlaybackItemRepresentable, completionHandler: IoTSettingCallback?) {
        guard let item = playbackItem as? IVPlaybackItem else {
            assertionFailure("确保 playbackItem 是 IVPlaybackItem 类型")
            return
        }
        seek(toTime: time, playbackItem: item, completionHandler: completionHandler)
    } 
}

// 需要明确重载 IVPlayer 遵守的 IoTPlayer 协议内容，否则运行时期间类型推断会有问题，估计是嵌套太多了
extension IVPlaybackPlayer {
    
    override var playerDelegate: IoTPlayerDelegate? {
        get {
            playerDelegateProxy.playerDelegate
        }
        set {
            playerDelegateProxy = IoTPlayerDelegateProxy(player: self)
            playerDelegateProxy.playerDelegate = newValue
            delegate = playerDelegateProxy
        }
    }
    
    override var playerDefinition: IoTVideoDefinition {
        get {
            IoTVideoDefinition(rawValue: definition.rawValue) ?? .high
        }
        set {
            definition = IVVideoDefinition(rawValue: newValue.rawValue) ?? .high
        }
    }
    
    override func setPlayerDefinition(_ definition: IoTVideoDefinition, completionHandler: IoTSettingCallback?) {
        setDefinition(IVVideoDefinition(rawValue: definition.rawValue) ?? .high, completionHandler: { error in
            completionHandler?(error)
        })
    }
    
    override var playerStatus: IoTPlayerStatus {
        return IoTPlayerStatus(rawValue: status.rawValue) ?? .stopped
    }
    
    override func startPlay(forceRefresh: Bool) {
        play()
    }
    
    override func stop(cancelReconnect: Bool = false) {
        stop()
    }
    
    override func send(data: Data) -> Bool {
        return send(data)
    }
    
    override func send(data: Data?, sequence: Bool, multiCallback: @escaping IotMsgMultiRspCallback) {
        send(data, sequence: sequence, multiCallback: multiCallback)
    }
    
    override func stopService(_ willDisappear: Bool = false) {
       
    }
}
