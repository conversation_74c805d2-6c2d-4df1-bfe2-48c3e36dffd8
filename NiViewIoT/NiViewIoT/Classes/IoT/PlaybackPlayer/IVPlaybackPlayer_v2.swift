//
//  IVPlaybackPlayer_v2.swift
//  NiView
//
//  Created by apple on 2022/8/29.
//  Copyright © 2022 nice. All rights reserved.
//

import UIKit
import TIoTLinkVideo
//import TIoTLinkKit

class IVPlaybackPlayer_v2: IVPlayer_v2 {
    
    let kPlaybackCmd = "ipc.flv?action=playback"
    /// 进度条计时器
    var timer: DispatchSourceTimer?
    var isTimerSuspend = false
    
    // playback
    var iotPlaybackItem: PlaybackItemRepresentable?
    var iotPlaybackStrategy: IoTPlaybackStrategy = .single
    
    var startTimeWhenSeeking: TimeInterval = 0
    
    init(uid: Int64, deviceModel: NVDeviceModel) {
        super.init(uid: uid, deviceModel: deviceModel, playerType: .sdPlayback)
    }
    /// playback
    func setVideoPlayerStartPlayback(_ startTime: String, _ endTime: String) {
        DispatchQueue.main.async {
            var params = ""
            let realStartTime = self.startTimeWhenSeeking > 0
            ? String(format: "%.0f", self.startTimeWhenSeeking)
            : startTime
            if self.isNVR {
                params = "\(self.kPlaybackCmd)&channel=\(self.channel)&start_time=\(realStartTime)&end_time=\(endTime)"
            } else {
                params = "\(self.kPlaybackCmd)&channel=0&start_time=\(realStartTime)&end_time=\(endTime)"
            }
            
            let urlStr = TIoTCoreXP2PBridge.sharedInstance().getUrlForHttpFlv(self.deviceId)
            self.videoUrl = String(format: "%@%@", urlStr, params)
            NIotLogger.info("[IVPlayer_v2][Playback] setVideoPlayerStartPlayback url:\(self.videoUrl ?? "")")
            self.configVideo()
            self.player?.prepareToPlay()
            self.player?.play()
        }
    }
    
    func startPlaybackTimer() {
        if let timer = timer {
            if isTimerSuspend {
                timer.resume()
                isTimerSuspend = false
            }
        }
        if !isTimerSuspend {
            timer?.cancel()
            timer = nil
        }
        
        timer = DispatchSource.makeTimerSource(flags: [], queue: .global())
        timer?.schedule(deadline: .now(), repeating: 1, leeway: .milliseconds(0))
        timer?.setEventHandler { [weak self] in
            guard let self = self, let item = self.iotPlaybackItem else { return }
            DispatchQueue.main.async {
                let currentPlaybackTime = self.player?.currentPlaybackTime ?? 0
                let pts = self.startTimeWhenSeeking > 0
                ? self.startTimeWhenSeeking + currentPlaybackTime
                : item.startTime + currentPlaybackTime
                self.playerDelegate?.player?(self, didUpdatedPTS: pts)
                NIotLogger.debug("[IVPlayer_v2][Playback] didUpdatedPTS:\(currentPlaybackTime)")
            }
        }
        timer?.resume()
        isTimerSuspend = false
    }
    
    override func stop(cancelReconnect: Bool = false) {
//        super.stop()
        timer?.cancel()
        timer = nil
        player?.stop()
    }
    
    
    override func playbackDidFinish() {
        guard let item = self.iotPlaybackItem else { return }
        timer?.cancel()
        playerDelegate?.player?(self, didEndOfFile: item.startTime, error: nil)
    }
    
    override func refushVideo(_ notification: Notification) {
        guard let name = notification.userInfo?["id"] as? String,
              deviceId == name,
              let item = iotPlaybackItem
        else { return }
        NIotLogger.info("[IVPlayer_v2][Playback] refushVideo")
        setVideoPlayerStartPlayback(
            String(format: "%.0f", item.startTime),
            String(format: "%.0f", item.endTime)
        )
    }
    
//    private var _hasTryToReconnect = false
//
//    override func responseP2PDisconnect(_ noti: Notification) {
//        guard let deviceName = noti.userInfo?["id"] as? String,
//              self.deviceId == deviceName
//        else { return }
//        NIotLogger.error("[IVPlayer_v2]\(self) responseP2PDisconnect userInfo:\(noti.userInfo)")
//        guard !_hasTryToReconnect else { return }
//        _hasTryToReconnect = true
//        TIoTCoreXP2PBridge.sharedInstance().stopService(self.deviceId)
//        makeXp2pInfoRequest { [weak self] xp2pInfoString in
//            self?.reconnectXP2PWithDeviceName(deviceName, xp2pInfo: xp2pInfoString)
//        }
//
//    }
//
//    override func reconnectXP2PWithDeviceName(_ name: String, xp2pInfo: String) {
//        let env = TIoTCoreAppEnvironment.share()
//        let errCode = TIoTCoreXP2PBridge.sharedInstance().startApp(with: productId, dev_name: deviceId)
//        NIotLogger.info("[IVPlayer_v2]\(self) startApp errCode:\(errCode)")
//        TIoTCoreXP2PBridge.sharedInstance().setXp2pInfo(deviceId, sec_id: env.cloudSecretId, sec_key: env.cloudSecretKey, xp2pinfo: xp2pInfo)
//        if errCode == .init(rawValue: -1007) {
//            // APP SDK 版本与设备端 SDK 版本号不匹配，版本号需前两位保持一致
//        }
//    }
    
    override func moviePlaybackStateDidChange(_ notification: Notification) {
        super.moviePlaybackStateDidChange(notification)
        if player?.playbackState == .playing {
            startPlaybackTimer()
        }
    }
}


extension IVPlaybackPlayer_v2: IoTPlaybackPlayer {
    
    func setPlaybackStrategy(_ strategy: IoTPlaybackStrategy, completionHandler: IoTSettingCallback?) {
        iotPlaybackStrategy = strategy
    }
    
    var playbackSpeed: Float {
        get {
            0
        }
        set {
            
        }
    }
    
    func setPlaybackSpeed(_ speed: Float, completionHandler: IoTSettingCallback?) {
        
    }
    
    func setIoTPlaybackItem(_ item: PlaybackItemRepresentable, seekToTime: TimeInterval) {
        iotPlaybackItem = item
        setVideoPlayerStartPlayback(
            String(format: "%.0f", item.startTime),
            String(format: "%.0f", item.endTime)
        )
    }
    
    func seekToTime(_ time: TimeInterval, playbackItem: PlaybackItemRepresentable) {
        seekToTime(time, playbackItem: playbackItem, completionHandler: nil)
    }
    
    func seekToTime(_ time: TimeInterval, playbackItem: PlaybackItemRepresentable, completionHandler: IoTSettingCallback?) {
        if let timer = timer {
            if isTimerSuspend {
                timer.resume()
                isTimerSuspend = false
            }
        }
        if !isTimerSuspend {
            timer?.cancel()
            timer = nil
        }
        NIotLogger.debug("[IVPlayer_v2][Playback] seekToTime: \(time), startTime: \(playbackItem.startTime), endTime: \(playbackItem.endTime)")
        startTimeWhenSeeking = time
        iotPlaybackItem = playbackItem
        setVideoPlayerStartPlayback(
            String(format: "%.0f", playbackItem.startTime),
            String(format: "%.0f", playbackItem.endTime)
        )
        playerDelegate?.player?(self, didUpdatedPTS: time)
        completionHandler?(nil)
    }
    
    
    func pause() {
        pauseOrResume(true) { [weak self] error in
            guard error == nil else {
//                NVHud.showMessage(error!.localizedDescription)
                return
            }
            self?.player?.pause()
            self?.timer?.suspend()
            self?.isTimerSuspend = true
        }
    }
    
    func pause(_ completionHandler: IoTSettingCallback?) {
        pauseOrResume(true) { [weak self] error in
            guard error == nil else {
                completionHandler?(error)
                return
            }
            self?.player?.pause()
            self?.timer?.suspend()
            self?.isTimerSuspend = true
            completionHandler?(nil)
        }
    }
    
    func resume() {
        pauseOrResume(false) { [weak self] error in
            guard error == nil else {
//                NVHud.showMessage(error!.localizedDescription)
                return
            }
            self?.player?.play()
            self?.timer?.resume()
            self?.isTimerSuspend = false
        }
    }
    
    func resume(_ completionHandler: IoTSettingCallback?) {
        pauseOrResume(false) { [weak self] error in
            guard error == nil else {
                completionHandler?(error)
                return
            }
            self?.player?.play()
            self?.timer?.resume()
            self?.isTimerSuspend = false
            completionHandler?(nil)
        }
    }
    
    private func pauseOrResume(_ isPause: Bool, completionHandler: @escaping (Error?) -> Void) {
        var channel = "0"
        if isNVR { channel = channel_v2 }
        var cmd = "action=inner_define&channel=\(channel)&cmd="
        cmd += (isPause ? "playback_pause" : "playback_resume")
        TIoTCoreXP2PBridge.sharedInstance().getCommandRequest(withAsync: deviceId, cmd: cmd, timeout: 2_000_000) { jsonStr in
            guard let model = TIoTDeviceStatusModel.deserialize(from: jsonStr) else { return }
            if model.status == .success {
                completionHandler(nil)
            } else {
                NIotLogger.error("[IVPlayer_v2][Playback] send cmd: \(cmd), received: \(jsonStr), error: \(model.status?.desc ?? "")")
                completionHandler(NSError.init(error: model.status?.desc ?? "信令操作失败"))
            }
        }
    }
}
