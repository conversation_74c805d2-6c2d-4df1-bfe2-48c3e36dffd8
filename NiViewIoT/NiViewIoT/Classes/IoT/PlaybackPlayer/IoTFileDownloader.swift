//
//  IVFileDownloader.swift
//  NiView
//
//  Created by apple on 2022/8/31.
//  Copyright © 2022 nice. All rights reserved.
//

import UIKit
import NiViewIoT.NIot_IoTVideo_Private
import TIoTLinkVideo

protocol IoTFileDownloader {
    var downloaderStatus: IVDownloaderStatus { get set }
    var fileName: String? { get }
    var fileTime: UInt64 { get }
    var fileSize: UInt32 { get }
    var rcvSize: UInt32 { get }
    
    func downloadPlaybackFile(
        _ fileTime: UInt64,
        offset: UInt32,
        ready: @escaping (UInt32) -> Void,
        progress: @escaping (Data) -> Void,
        canceled: @escaping () -> Void,
        success: @escaping () -> Void,
        failure: @escaping (Error) -> Void
    )
    func cancel()
    func stop()
}


extension IVFileDownloader: IoTFileDownloader {
    var downloaderStatus: IVDownloaderStatus {
        get { status }
        set {}
    }
}


class IVFileDownloader_v2: NSObject, IoTFileDownloader {
    var downloaderStatus: IVDownloaderStatus = .idle
    
//    var fileName: String? { item.fileName }
//
//    var fileTime: UInt64 { item.fileTime }
//
//    var fileSize: UInt32 { UInt32(item.fileSize.value) }
//
//    var rcvSize: UInt32 = 0
//
//    private var deviceName: String { item.deviceId }
//    private let item: IVDownloadItem
//
//    init(item: IVDownloadItem) {
//        self.item = item
//        super.init()
//        TIoTCoreXP2PBridge.sharedInstance().delegate = self
//    }
    
    var fileName: String?
    var fileTime: UInt64
    var fileSize: UInt32
    var rcvSize: UInt32 = 0
    private var _downloadedSize: UInt32 = 0
    private var deviceName: String
    
    init(deviceId: String, fileName: String?, fileTime: UInt64, fileSize: UInt32) {
        self.deviceName = deviceId
        self.fileName = fileName
        self.fileTime = fileTime
        self.fileSize = fileSize
        super.init()
         TIoTCoreXP2PBridge.sharedInstance().delegate = self
    }
    
    deinit {
        stopAvRecvService("cancel")
    }
    
    private var _isDownloading = false
    
    private var progress: ((Data) -> Void)?
    private var canceled: (() -> Void)?
    private var success: (() -> Void)?
    private var failure: ((Error) -> Void)?
    
    func downloadPlaybackFile(
        _ fileTime: UInt64,
        offset: UInt32,
        ready: @escaping (UInt32) -> Void,
        progress: @escaping (Data) -> Void,
        canceled: @escaping () -> Void,
        success: @escaping () -> Void,
        failure: @escaping (Error) -> Void
    ) {
        rcvSize = 0
        _downloadedSize = 0
        _isDownloading = true
        let cmd = "action=download&channel=0&file_name=\(fileName)&offset=\(offset)"
        NIotLogger.info("[IVFileDownloader_v2] start download file: \(fileName), cmd: \(cmd)")
        TIoTCoreXP2PBridge.sharedInstance().startAvRecvService(deviceName, cmd: cmd)
        
        ready(UInt32(fileSize))
        self.progress = progress
        self.canceled = canceled
        self.success = success
        self.failure = failure
    }
    
    func cancel() {
        canceled?()
        stopAvRecvService("cancel")
    }
    
    func stop() {
        stopAvRecvService("stop")
    }
    
    private func stopAvRecvService(_ step: String) {
        guard _isDownloading else {
            NIotLogger.info("[IVFileDownloader_v2] 当前任务未进行下载，file: \(fileName ?? "")")
            return
        }
        _isDownloading = false
        NIotLogger.info("[IVFileDownloader_v2] download \(step), file: \(fileName ?? ""), downloaded: \(_downloadedSize)")
        TIoTCoreXP2PBridge.sharedInstance().stopAvRecvService(deviceName)
    }
    
}

extension IVFileDownloader_v2: TIoTCoreXP2PBridgeDelegate {
    
    func getVideoPacket(withID dev_name: String, data: UnsafeMutablePointer<UInt8>, len: Int) {
        _downloadedSize += UInt32(len)
        NIotLogger.info("[IVFileDownloader_v2] downloading file: \(fileName ?? ""), downloaded: \(_downloadedSize), current: \(len)")
        let buffer = UnsafeBufferPointer(start: data, count: len)
        let data = Data(buffer: buffer)
        rcvSize += UInt32(data.count)
        progress?(data)
    }
    
    func reviceDeviceMsg(withID dev_name: String, data: Data) -> String {
        NIotLogger.info("[IVFileDownloader_v2] device: \(dev_name) revice msg: \(data.string(with: .ascii) ?? "")")
        return "responseMES"
    }
    
    func reviceEventMsg(withID dev_name: String, eventType: XP2PType, msg: UnsafePointer<CChar>) {
        if eventType == .init(rawValue: 1009) { // XP2PTypeDownloadEnd
            if _isDownloading {
                stopAvRecvService("finished")
                success?()
            }
        } else if eventType == .init(rawValue: 1003) { // XP2PTypeDisconnect
            if _isDownloading {
                failure?(NSError.init(error: "download failed"))
                stopAvRecvService("failed")
            }
        }
    }
}

