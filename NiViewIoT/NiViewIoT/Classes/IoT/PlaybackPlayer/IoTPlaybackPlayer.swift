//
//  IoTPlaybackPlayer.swift
//  NiView
//
//  Created by apple on 2022/1/12.
//  Copyright © 2022 nice. All rights reserved.
//

import UIKit


/// 回放文件切换策略
@objc
public enum IoTPlaybackStrategy: UInt {
    /// 按文件开始时间从小到大（升序）自动播放，默认值
    case ascending = 0
    /// 按文件开始时间从大到小（降序）自动播放
    case descending = 1
    /// 播放单个文件，播完自动暂停
    case single = 2
}

protocol IoTPlaybackPlayer: IoTPlayer {
    
    /// 回放策略，默认 ascending
    var iotPlaybackStrategy: IoTPlaybackStrategy { get set }
    
    ///  设置回放策略
    func setPlaybackStrategy(_ strategy: IoTPlaybackStrategy, completionHandler: IoTSettingCallback?)
    
    /// 回放倍速
    ///
    /// 默认1.0, 一般取值范围[0.5~16.0], SDK允许传参范围(0.0~32.0]，开发者应视设备性能而定!
    /// - note:  超过2倍速后设备是不会发音频的，并且视频只有关键帧
    var playbackSpeed: Float { get set }
    
    /// 设置回放倍速
    func setPlaybackSpeed(_ speed: Float, completionHandler: IoTSettingCallback?)
     
    /// 当前回放的文件。
    /// - note: 当前回放时间通过`-[IoTPlayer pts]`获取
    var iotPlaybackItem: PlaybackItemRepresentable? { get }
    
    /// (未播放前)设置回放参数.
    /// - note:  应在文件尚未播放时使用，需手动调用`play`开始播放.
    /// - Parameters:
    ///   - item: 播放的文件(可跨文件).
    ///   - seekToTime: 指定播放起始时间点（秒），取值范围`playbackItem.startTime >= time <= playbackItem.endTime`.
    func setIoTPlaybackItem(_ item: PlaybackItemRepresentable, seekToTime: TimeInterval)
    
    /// (已播放后)跳到指定文件和时间播放
    /// - note:  应在文件正在播放时使用，无需再手动调用`play`开始播放.
    /// - Parameters:
    ///   - time: 指定播放起始时间点（秒），取值范围`playbackItem.startTime >= time <= playbackItem.endTime`
    ///   - playbackItem: 播放的文件(可跨文件)
    func seekToTime(_ time: TimeInterval, playbackItem: PlaybackItemRepresentable)
    
    /// (已播放后)跳到指定文件和时间播放
    /// - note:  应在文件正在播放时使用，无需再手动调用`play`开始播放.
    /// - Parameters:
    ///   - time: 指定播放起始时间点（秒），取值范围`playbackItem.startTime >= time <= playbackItem.endTime`
    ///   - playbackItem: 播放的文件(可跨文件)
    ///   - completionHandler: 回调
    func seekToTime(_ time: TimeInterval, playbackItem: PlaybackItemRepresentable, completionHandler: IoTSettingCallback?)
    
    func pause()
    
    func pause(_ completionHandler: IoTSettingCallback?)
    
    func resume()
    
    func resume(_ completionHandler: IoTSettingCallback?)
}
