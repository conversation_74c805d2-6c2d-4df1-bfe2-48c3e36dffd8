//
//  IVPlayer+IoTPlayer.swift
//  NiView
//
//  Created by apple on 2022/1/12.
//  Copyright © 2022 nice. All rights reserved.
//

import UIKit
//import IoTVideo.IVPlayer

extension IVPlayer: IoTPlayer {
    
    @objc var playerDelegateProxy: IoTPlayerDelegateProxy {
        get {
            objc_getAssociatedObject(self, &IoTAssociatedKeys.kPlayerDelegateProxy) as! IoTPlayerDelegateProxy
        }
        set {
            objc_setAssociatedObject(self, &IoTAssociatedKeys.kPlayerDelegateProxy, newValue, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
        }
    }
    
    var playerDelegate: IoTPlayerDelegate? {
        get {
            playerDelegateProxy.playerDelegate
        }
        set {
            playerDelegateProxy = IoTPlayerDelegateProxy(player: self)
            playerDelegateProxy.playerDelegate = newValue
            delegate = playerDelegateProxy
        }
    }
    
    var playerDefinition: IoTVideoDefinition {
        get {
            IoTVideoDefinition(rawValue: definition.rawValue) ?? .high
        }
        set {
            definition = IVVideoDefinition(rawValue: newValue.rawValue) ?? .high
        }
    }
    
    func setPlayerDefinition(_ definition: IoTVideoDefinition, completionHandler: IoTSettingCallback?) {
        setDefinition(IVVideoDefinition(rawValue: definition.rawValue) ?? .high, completionHandler: { error in
            completionHandler?(error)
        })
    }
    
    func takeScreenshot(scale: CGFloat, _ completionHandler: @escaping (UIImage?) -> Void) {
        takeScreenshot(completionHandler)
    }
    
    var playerStatus: IoTPlayerStatus {
        return IoTPlayerStatus(rawValue: status.rawValue) ?? .stopped
    }
    
    func startPlay(forceRefresh: Bool) {
        play()
    }
    
    func stop(cancelReconnect: Bool = false) {
        stop()
    }
    
    func send(data: Data) -> Bool {
        return send(data)
    }
    
    func send(data: Data?, sequence: Bool, multiCallback: @escaping IotMsgMultiRspCallback) {
        send(data, sequence: sequence, multiCallback: multiCallback)
    }
    
    func stopService(_ willDisappear: Bool = false) {
       
    }
     
}
