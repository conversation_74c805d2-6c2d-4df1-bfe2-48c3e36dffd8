//
//  IVPlayer_v2.swift
//  NiView
//
//  Created by apple on 2022/3/8.
//  Copyright © 2022 nice. All rights reserved.
//

import UIKit
import NiViewIoT.NIot_TIoTLinkKit_Private
import TIoTLinkVideo
import IJKMediaFramework
import HandyJSON

public enum NVPlayerType: Int {
    case none
    case live = 1
    case cloudPlayback
    case sdPlayback
}

class IVPlayer_v2: NSObject {
    
    struct Command {
        static let action_live = "live"
        static let action_voice = "voice"
    }
    
    enum Quality: UInt {
        case standard, high, `super`
        var cmd: String {
            switch self {
            case .standard: return "ipc.flv?action=live&quality=standard"
            case .high: return "ipc.flv?action=live&quality=high"
            case .super: return "ipc.flv?action=live&quality=super"
            }
        }
    }
    
    var player: IJKFFMoviePlayerController?
    weak var playerDelegate: IoTPlayerDelegate?
    
    let uid: Int64
    var deviceId: String { isNVR ? deviceNameNVR : deviceModel.device.tid }
    var productId: String { deviceModel.device.sdk2ProductId ?? "" }
    var xp2pInfo: String?
    var deviceNameNVR = ""
    var isNVR = false
    
    private var _is_ijkPlayer_stream = true
    var videoUrl: String?
    
    private var quality = Quality.standard
    
    private let imageView = UIImageView()
    
    private var statusModel: TIoTDeviceStatusModel? {
        didSet {
            playerDelegate?.player?(self, didUpdatedViewerNum: Int(audience))
        }
    }
    var onlineStatusForV2: PMOnlineStatus { DeviceMgr.getDeviceOnlineStatus(tid: deviceModel.device.tid) }
    var previousOnlineStatusForV2: PMOnlineStatus = .offline
    
    // 打洞时间
    private var ipcP2PStartTime: CFTimeInterval = 0
    private var ipcP2PEndTime: CFTimeInterval = 0
    // 播放时间
    private var playStartTime: CFTimeInterval = 0
    private var playEndTime: CFTimeInterval = 0
    /// 录像下载路径
    private var saveFilePath: String?
    private var _isRecording: Bool = false
    typealias RecordingCompletionHandler = (String?, Error?) -> Void
    private var _recordingCompletionHandler: RecordingCompletionHandler?
    private var _isTalking: Bool = false
    
    private var _isServiceStarted = false
    /// p2p链接是否成功连接过，如果未成功过（目前设备离线 SDK 没有办法判断是否离线），则p2p 链接断开（1003）时提示错误，而不是重连
    private var _hasP2PEverConnected = false
    /// 直播断开时是否需要自动重连，默认 true，4G 设备 5 分钟观看提示时断开直播情况下不需要自动重连
    var shouldAutoReconnect = true
    private var _hasRetryAfter1003 = false
    private var _hasStartConnectNumPolling = false
    
    /// 5s 发送信令 timer
    private var _signalTimer: Timer?
    /// wifi设备显示码率
    private var bitrateUpdateTimerFor2_0: Timer?
    private var lastBytesFor2_0: Int64 = 0

    
    /// 唤醒时间记录，用于判断是否处于“刚休眠立马唤醒时会返回离线”的情况
    private var _wakeUpTime: Date?
    private var _playAfterReceiveXp2pInfoForV2 = false
    /// 2.0设备15s内player.status没有变成.ready，则静默重连
    private var _retryPlayV2DeviceLiveTime: Timer?
    /// 是否处于 2.0 设备直播准备流程
    private var _isPreparingPlayV2DeviceLive = false {
        didSet {
            NIotLogger.info("[IVPlayer_v2] _isPreparingPlayV2DeviceLive: \(_isPreparingPlayV2DeviceLive)")
            _retryPlayV2DeviceLiveTime?.invalidate()
            _retryPlayV2DeviceLiveTime = nil
            if _isPreparingPlayV2DeviceLive && !isPlaying {
                _retryPlayV2DeviceLiveTime = Timer.nv_scheduledTimer(timeInterval: 15, repeats: true) { [weak self] _ in
                    guard let self = self, self.playerType == .live, !self.isPlaying else { return }
                    if self._isPreparingPlayV2DeviceLive {
                        NIotLogger.error("[IVPlayer_v2] live still not playing after 15s, retry...♻️♻️♻️")
                        self.startPlayV2DeviceLive(forceRequestXp2pInfo: true)
                    }
                }
            }
        }
    }
    
    let playerType: NVPlayerType
    let deviceModel: NVDeviceModel
    
    init(uid: Int64, deviceModel: NVDeviceModel, playerType: NVPlayerType = .live) {
        self.uid = uid
        self.deviceModel = deviceModel
        self.playerType = playerType
        super.init()
        imageView.isUserInteractionEnabled = true
//        TIoTCoreXP2PBridge.sharedInstance().delegate = self
        XP2PDelegateForwarder.shared.registerDelegate(self, forDeviceId: deviceId)
        TIoTCoreXP2PBridge.sharedInstance().logEnable = false
        try? AVAudioSession.sharedInstance().setCategory(.playback)
        try? AVAudioSession.sharedInstance().setActive(true)
        addNotification()
        NIotLogger.info("[IVPlayer_v2]\(self) alloc")
        if deviceModel.isV2Device {
            previousOnlineStatusForV2 = onlineStatusForV2
        }

    }
    
    deinit {
        NIotLogger.error("[IVPlayer_v2]\(self) deinit")
        XP2PDelegateForwarder.shared.unregisterSpecificDelegate(self, forDeviceId: deviceId)
        // 如果在 deinit 中停止服务，内存泄漏时 deinit 不调用，会导致服务没有停止，然后再次 deinit 的时候连续停止两次服务会造成闪退
        // stopService()
    }
    
    /// 退出直播后 vc 过一会才被释放，此时如果立马又点击观看直播，会导致未释放的 IVPlayer_v2 也发起直播流程，造成混乱问题
    /// 所以定义一个 _willDisappear，用于规避这个问题
    private var _willDisappear = false {
        didSet {
            if _willDisappear {
                NIotLogger.error("[IVPlayer_v2]\(self) vc disappear, player will be relesse")
                clearObserver()
            }
        }
    }
    
    func clearObserver() {
        NIotLogger.info("[IVPlayer_v2]\(self) clearObserver")
        player?.nativeInvokeDelegate = nil
        NotificationCenter.default.removeObserver(self)
    }
    
    func stopService(_ willDisappear: Bool = false) {
        _willDisappear = willDisappear
        if !isNVR {
            NIotLogger.error("[IVPlayer_v2]\(self) ❗️❗️❗️stopService❗️❗️❗️")
            TIoTCoreXP2PBridge.sharedInstance().stopService(deviceId)
        }
        _isServiceStarted = false
        sendStopFlag()
    }
    
    private func sendStopFlag() {
        guard _signalTimer != nil else { return }
        _signalTimer?.invalidate()
        _signalTimer = nil
        NIotLogger.info("[IVPlayer_v2]\(self) sending stop flag...")
        NIotSignalMessager.shared.sendAction(.sendFlag(self.uid, 0), completionHandler: { _, _ in })
    }
    
    func setupXp2pInfo(_ xp2pInfo: String) {
        stopService()
        startService(xp2pInfo)
    }
    
    var startAppTime: TimeInterval = 0
    func startService(_ xp2pInfo: String) {
        if isNVR {
            getDeviceStatusWithType(Command.action_live, quality: quality)
        } else {
            let env = TIoTCoreAppEnvironment.share()
            startAppTime = Date().timeIntervalSince1970
            let errCode = TIoTCoreXP2PBridge.sharedInstance().startApp(with: productId, dev_name: deviceId)
            _isServiceStarted = true
            NIotLogger.info("[IVPlayer_v2]\(self) ▶️▶️▶️startApp▶️▶️▶️ errCode:\(errCode), tid: \(deviceId) xp2pInfo: \(xp2pInfo)")
            if errCode == .init(rawValue: -1007) {
                // APP SDK 版本与设备端 SDK 版本号不匹配，版本号需前两位保持一致
            } else if errCode == .init(rawValue: -1011) {
                // FIXME: 某种情况下会开启 2 个直播，这里尝试关闭再开启
                NIotLogger.error("[IVPlayer_v2]\(self) 🚫🚫🚫already playing live🚫🚫🚫")
                stopService()
            } else {
                TIoTCoreXP2PBridge.sharedInstance().setXp2pInfo(deviceId, sec_id: env.cloudSecretId, sec_key: env.cloudSecretKey, xp2pinfo: xp2pInfo)
                ipcP2PStartTime = CACurrentMediaTime()
            }
        }
    }
    
    func reconnectXP2PWithDeviceName(_ name: String, xp2pInfo: String) {
        let env = TIoTCoreAppEnvironment.share()
        NIotLogger.info("[IVPlayer_v2]\(self) reconnectXP2PWithDeviceName")
        TIoTCoreXP2PBridge.sharedInstance().setXp2pInfo(deviceId, sec_id: env.cloudSecretId, sec_key: env.cloudSecretKey, xp2pinfo: xp2pInfo)
        setVideoPlayerStartPlay(quality)
    }
    
//    private var channel: String { self.selectedModel?.Channel ?? "0" }
    var channel_v2: String { "0" }
    
    /// xp2p 连接成功后开始直播
    func setVideoPlayerStartPlay(_ quality: Quality) {
        DispatchQueue.main.async {
            var qualityId = ""
            if self.isNVR {
                qualityId = "\(quality.cmd)&channel=\(self.channel_v2)"
            } else {
                qualityId = "\(quality.cmd)&channel=0"
            }
            
            let urlStr = TIoTCoreXP2PBridge.sharedInstance().getUrlForHttpFlv(self.deviceId)
            self.videoUrl = String(format: "%@%@", urlStr, qualityId)
            
            NIotLogger.info("[IVPlayer_v2]\(self) setVideoPlayerStartPlay url:\(self.videoUrl)")
            self.configVideo()
            self.player?.prepareToPlay()
            self.player?.play()
            
            self.playStartTime = CACurrentMediaTime()
        }
    }
    
    /// 获取ipc/nvr设备状态，是否可以推流
    /// - Parameters:
    ///   - type: 直播 or 对讲
    ///   - checkConnectNumOnly: 是否开启轮询，更新直播在线人数时用到
    ///   - quality: 分辨率
    func getDeviceStatusWithType(
        _ type: String,
        checkConnectNumOnly: Bool = false,
        quality: Quality,
        completionHandler: ((Error?) -> Void)? = nil
    ) {
        var actionStr = ""
        let qualityTypeStr = quality.cmd.split(separator: "&").last ?? ""
        if isNVR {
            actionStr = "action=inner_define&channel=\(channel_v2)&cmd=get_device_st&type=\(type)&\(qualityTypeStr)"
        } else {
            actionStr = "action=inner_define&channel=0&cmd=get_device_st&type=\(type)&\(qualityTypeStr)"
        }
        
        TIoTCoreXP2PBridge.sharedInstance().getCommandRequest(withAsync: deviceId, cmd: actionStr, timeout: 2*1000*1000) { [weak self] jsonStr in
            
            guard let self = self,
                  let arr = [TIoTDeviceStatusModel].deserialize(from: jsonStr),
                  let model = arr.first as? TIoTDeviceStatusModel
            else {
                NIotLogger.info("[IVPlayer_v2]\(self) getDeviceStatusWithType jsonStr: \(jsonStr)")
                if checkConnectNumOnly {
                    guard self?.playerType == .live, // 回放不轮询
                          self?.player?.playbackState == .playing // 不是正在直播不轮询
                    else {
                        self?._hasStartConnectNumPolling = false
                        NIotLogger.info("[IVPlayer_v2] 1停止轮询 Connect Number")
                        return
                    }
                    DispatchQueue.main.asyncAfter(deadline: .now()+3) {
                        self?.getDeviceStatusWithType(type, checkConnectNumOnly: true, quality: quality)
                    }
                }
                completionHandler?(NSError(error: NVLang("common_send_cmd_error1")))
                return
            }
            self.statusModel = model
            if model.status == .success {
                if type == Command.action_live {
                    if checkConnectNumOnly {
                        guard self.playerType == .live, // 回放不轮询
                              self.player?.playbackState == .playing // 不是正在直播不轮询
                        else {
                            self._hasStartConnectNumPolling = false
                            NIotLogger.info("[IVPlayer_v2] 停止轮询 Connect Number")
                            return
                        }
                        if let str = model.appConnectNum, let num = UInt(str) {
                            NIotLogger.info("[IVPlayer_v2] viewers num: \(num)")
                        }
                        DispatchQueue.main.asyncAfter(deadline: .now()+3) {
                            self.getDeviceStatusWithType(type, checkConnectNumOnly: true, quality: quality)
                        }
                    } else {
                        self.setVideoPlayerStartPlay(quality)
                    }
                } else {
                    var channel = "channel=0"
                    if self.isNVR {
                        channel = "channel=\(channel)"
                    }
                    do {
                        try AVAudioSession.sharedInstance().setCategory(.playAndRecord, options: .defaultToSpeaker)
                        try AVAudioSession.sharedInstance().setActive(true)
                        TIoTCoreXP2PBridge.sharedInstance().sendVoice(toServer: self.deviceId, channel: channel)
                    } catch {
                        
                    }
                }
                completionHandler?(nil)
            } else {
                // error TIoTDeviceStatus
                if !checkConnectNumOnly {
                    completionHandler?(NSError(error: NVLang("common_send_cmd_error1")))
                }
            }
        }
        
        
    }
    
    func stopPlayMovie() {
        if let player = player {
            NIotLogger.info("[IVPlayer_v2]\(self) stopPlayMovie, player:\(player)")
            sendStopFlag()
        }
        player?.stop()
        player?.shutdown()
        player?.view.removeFromSuperview()
        player = nil
    }
    
    
    func configVideo() {
        if _is_ijkPlayer_stream {
            // 1.通过播放器发起的拉流
            guard let videoUrl = videoUrl else {
                return
            }
            TIoTCoreXP2PBridge.sharedInstance().writeFile = true
            TIoTCoreXP2PBridge.recordstream(deviceId)
            
            stopPlayMovie()
            
            #if DEBUG
            IJKFFMoviePlayerController.setLogReport(true)
            IJKFFMoviePlayerController.setLogLevel(.init(rawValue: 3))
            #else
            IJKFFMoviePlayerController.setLogReport(false)
            IJKFFMoviePlayerController.setLogLevel(.init(rawValue: 4))
            #endif
            
//            IJKFFMoviePlayerController.checkIfFFmpegVersionMatch(true)
            
            player = IJKFFMoviePlayerController.init(contentURL: URL(string: videoUrl), with: IJKFFOptions.byDefault())
            imageView.addSubview(player!.view)
            player?.view.autoresizingMask = [.flexibleWidth, .flexibleHeight]
            player?.view.frame = imageView.bounds
            player?.scalingMode = .aspectFit
            player?.shouldAutoplay = true
            player?.playbackVolume = mute ? 0 : 1
            player?.nativeInvokeDelegate = self
//            player?.shouldShowHudView = true
            
//            player?.setOptionIntValue(100, forKey: "analyzemaxduration", of: kIJKFFOptionCategoryFormat)
            player?.setOptionIntValue(10 * 1024, forKey: "probesize", of: kIJKFFOptionCategoryFormat)
//            player?.setOptionIntValue(1024, forKey: "max-buffer-size", of: kIJKFFOptionCategoryPlayer)
            player?.setOptionIntValue(0, forKey: "packet-buffering", of: kIJKFFOptionCategoryPlayer)
            player?.setOptionIntValue(1, forKey: "start-on-prepared", of: kIJKFFOptionCategoryPlayer)
            player?.setOptionIntValue(2, forKey: "threads", of: kIJKFFOptionCategoryCodec)
            player?.setOptionIntValue(0, forKey: "sync-av-start", of: kIJKFFOptionCategoryPlayer)
            player?.setOptionIntValue(1, forKey: "videotoolbox", of: kIJKFFOptionCategoryPlayer)
            
            updateStatus(.ready)
        } else {
            // 2.通过裸流服务拉流
            TIoTCoreXP2PBridge.sharedInstance().writeFile = true
            if !isNVR {
                TIoTCoreXP2PBridge.sharedInstance().startAvRecvService(deviceId, cmd: "action=live")
            }
        }
    }
    
    var mute: Bool = false {
        didSet { player?.playbackVolume = mute ? 0 : 1 }
    }
    
    func playbackDidFinish() {
        
    }
}

// MARK: - Event
extension IVPlayer_v2 {
    
    func triggerTalking(_ talk: Bool, completionHandler: ((Error?) -> Void)? = nil) {
        if talk {
            guard !_isTalking else { return }
            _isTalking = true
            getDeviceStatusWithType(Command.action_voice, quality: quality, completionHandler: completionHandler)
        } else {
            guard _isTalking else { return }
            _isTalking = false
            let errCode = TIoTCoreXP2PBridge.sharedInstance().stopVoiceToServer()
            var error: NSError? = errCode == .init(0) ? nil : NSError(error: NVLang("common_send_cmd_error1"))
            completionHandler?(error)
        }
    }
     
    /// 切换 live 清晰度
    func resetVideoPlayer(_ quality: Quality, completionHandler: (() -> Void)? = nil) {
        NIotLogger.info("[IVPlayer_v2] resetVideoPlayer quality:\(quality.rawValue)")
        player?.stop()
        player?.shutdown()
        player = nil
        
        if !isNVR {
            if TIoTCoreXP2PBridge.sharedInstance().writeFile {
                TIoTCoreXP2PBridge.sharedInstance().stopAvRecvService(deviceId)
            }
        }
        
        var qualityId = ""
        if isNVR {
            qualityId = String(format: "%@&channel=%@", quality.cmd, channel_v2)
        } else {
            qualityId = String(format: "%@&channel=0", quality.cmd)
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            let urlStr = TIoTCoreXP2PBridge.sharedInstance().getUrlForHttpFlv(self.deviceId)
            guard !urlStr.isEmpty else { return }
            self.videoUrl = String(format: "%@%@", urlStr, qualityId)
            self.configVideo()
            self.player?.prepareToPlay()
            self.player?.play()
            completionHandler?()
            
            self.playStartTime = CACurrentMediaTime()
        }
    }
    
    func takeScreenshot(scale: CGFloat = 1) -> UIImage? {
        guard let view = player?.view,
              let width = player?.naturalSize.width,
              let height = player?.naturalSize.height
        else { return nil }
        let videoWidth = width * scale
        let videoHeight = height * scale
        NIotLogger.info("[takeScreenshot] \(videoWidth) x \(videoHeight)")
        let rect = CGRect(x: 0, y: 0, width: videoWidth, height: videoHeight)
        UIGraphicsBeginImageContextWithOptions(rect.size, false, 1)
        view.drawHierarchy(in: rect, afterScreenUpdates: false)
        let image = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        return image
    }
}
 

// MARK: - Notification

extension IVPlayer_v2 {
    
    func addNotification() {
        NIotLogger.info("[IVPlayer_v2]\(self) addNotification")
        // 避免重复添加监听
        NotificationCenter.default.removeObserver(self)
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(devicePropertyDidUpdate(_:)),
            name: .NIot_DevicePropertyDidUpdate,
            object: nil
        )
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(moviePlaybackStateDidChange(_:)),
            name: .IJKMPMoviePlayerPlaybackStateDidChange,
            object: nil
        )
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(moviePlayerLoadStateDidChange(_:)),
            name: .IJKMPMoviePlayerLoadStateDidChange,
            object: nil
        )
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(moviePlayerPlaybackDidFinish(_:)),
            name: .IJKMPMoviePlayerPlaybackDidFinish,
            object: nil
        )
        if !isNVR {
            NotificationCenter.default.addObserver(
                self,
                selector: #selector(refushVideo(_:)),
                name: .TIoTCoreXP2PBridgeNotificationReady,
                object: nil
            )
        }
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(responseP2PDisconnect(_:)),
            name: .TIoTCoreXP2PBridgeNotificationDisconnect,
            object: nil
        )
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(receiveDeviceMessage(_:)),
            name: .TIoTCoreXP2PBridgeNotificationDeviceMsg,
            object: nil
        )
    }
    
    @objc func moviePlayerLoadStateDidChange(_ notification: Notification) {
        guard let player = player else { return }
        NIotLogger.info("[IVPlayer_v2]\(self) moviePlayerLoadStateDidChange loadState: \(player.loadState)")
    }
    
    @objc func moviePlayerPlaybackDidFinish(_ notification: Notification) {
        guard player != nil else { return }
        let reason = notification.userInfo![IJKMPMoviePlayerPlaybackDidFinishReasonUserInfoKey] as! Int
        NIotLogger.info("[IVPlayer_v2]\(self) moviePlayerPlaybackDidFinish reason: \(reason)")
        switch reason {
        case IJKMPMovieFinishReason.playbackEnded.rawValue:
            playbackDidFinish()
            break
        case IJKMPMovieFinishReason.playbackError.rawValue:
            stop()
            if playerType == .live {
                DispatchQueue.main.asyncAfter(deadline: .now()+3) {
                    NIotLogger.error("[IVPlayer_v2]\(self) play failed, retry...")
                    NotificationCenter.default.post(name: .retryPlayLiveFor2_0, object: nil, userInfo: ["deviceId": self.deviceId])
                }
            }
            break
        case IJKMPMovieFinishReason.userExited.rawValue:
            break
        default:
            break
        }
    }
    
    @objc func moviePlaybackStateDidChange(_ notification: Notification) {
        guard let player = player else { return }
        NIotLogger.info("[IVPlayer_v2]\(self) moviePlaybackStateDidChange playbackState: \(player.playbackState.rawValue)")
        switch player.playbackState {
        case .stopped, .interrupted:
            updateStatus(.stopped)
        case .playing:
            _signalTimer?.invalidate()
            _signalTimer = nil
            _signalTimer = Timer.nv_scheduledTimer(timeInterval: 5, repeats: true) { [weak self] _ in
                guard let self = self else { return }
                NIotSignalMessager.shared.sendAction(.sendFlag(self.uid, 1), completionHandler: { _, _ in })
            }
            
            bitrateUpdateTimerFor2_0?.invalidate()
            bitrateUpdateTimerFor2_0 = nil
            // 计算速率：通过相邻两次读取的字节数差值，除以时间间隔，得到实时的传输速率
            let interval: Int64 = 2
            bitrateUpdateTimerFor2_0 = Timer.scheduledTimer(withTimeInterval: TimeInterval(interval), repeats: true) { [weak self, weak player] _ in
                guard let self = self else { return }
                let currentBytes = player?.trafficStatistic() ?? 0
                if currentBytes != 0, self.lastBytesFor2_0 != 0 {
                    let bytesDelta = (currentBytes - self.lastBytesFor2_0)/interval
                    if bytesDelta > 0 {
                        self.playerDelegate?.connection?(self, didUpdatedSpeed: UInt32(bytesDelta))
                    }
                }
                self.lastBytesFor2_0 = currentBytes
            }

            NIotLogger.info("[IVPlayer_v2][Time] \(Date().timeIntervalSince1970 - startAppTime)")
            playEndTime = CACurrentMediaTime()
            updateStatus(.playing)
            _hasP2PEverConnected = true
            shouldAutoReconnect = true
            _hasRetryAfter1003 = false
            
            if !_hasStartConnectNumPolling, !(self is IVPlaybackPlayer_v2) {
                NIotLogger.info("[IVPlayer_v2] 开启轮询 Connect Number")
                _hasStartConnectNumPolling = true
                getDeviceStatusWithType(Command.action_live, checkConnectNumOnly: true, quality: quality)
            }
        case .paused:
            updateStatus(.paused)
        case .seekingForward, .seekingBackward:
            updateStatus(.seeking)
        default: break
        }
    }
    
    @objc func refushVideo(_ notification: Notification) {
        guard let name = notification.userInfo?["id"] as? String,
              deviceId == name
        else { return }
        NIotLogger.info("[IVPlayer_v2]\(self) refushVideo")
        ipcP2PEndTime = CACurrentMediaTime()
        setVideoPlayerStartPlay(quality)
    }
     
    @objc func responseP2PDisconnect(_ noti: Notification) {
        guard let deviceName = noti.userInfo?["id"] as? String,
              self.deviceId == deviceName,
              player != nil,
              _hasP2PEverConnected,
              shouldAutoReconnect
        else { return }
        NIotLogger.error("[IVPlayer_v2]\(self) responseP2PDisconnect userInfo:\(noti.userInfo)")
        DispatchQueue.main.asyncAfter(deadline: .now()+3) {
            guard !self._hasRetryAfter1003,
                  self.shouldAutoReconnect
            else { return }
            NotificationCenter.default.post(name: .retryPlayLiveFor2_0, object: nil, userInfo: ["deviceId": self.deviceId])
        }
    }
    
    @objc func receiveDeviceMessage(_ notification: Notification) {
        NIotLogger.info("[IVPlayer_v2]\(self) receiveDeviceMessage, userInfo:\(notification.userInfo)")
    }
    
}

extension IVPlayer_v2: IoTPlayer {
    
    var playerDefinition: IoTVideoDefinition {
        get {
            IoTVideoDefinition(rawValue: quality.rawValue) ?? .high
        }
        set {
            guard quality.rawValue != newValue.rawValue else { return }
            quality = Quality(rawValue: newValue.rawValue) ?? .super
            resetVideoPlayer(quality)
        }
    }

    func setPlayerDefinition(_ definition: IoTVideoDefinition, completionHandler: IoTSettingCallback?) {
        quality = Quality(rawValue: definition.rawValue) ?? .high
        resetVideoPlayer(quality) {
            completionHandler?(nil)
        }
    }

    var handsFree: Bool {
        set {}
        get { return false }
    }

    var videoView: UIView { imageView }

    var audience: UInt {
        if let str = statusModel?.appConnectNum, let num = UInt(str) {
            return num
        } else {
            return 0
        }
    }

    var playerStatus: IoTPlayerStatus {
        /** IJKMPMoviePlaybackState
         MPMoviePlaybackStateStopped,
         MPMoviePlaybackStatePlaying,
         MPMoviePlaybackStatePaused,
         MPMoviePlaybackStateInterrupted,
         MPMoviePlaybackStateSeekingForward,
         MPMoviePlaybackStateSeekingBackward
         */
        /** IoTPlayerStatus
         case stopping = -1
         case stopped = 0
         case preparing = 1
         case ready = 2
         case loading = 3
         case playing = 4
         case paused = 5
         case fastForward = 6
         case seeking = 7
         */
        
        // TODO: 这里只是 playback 的状态
        guard let status = player?.playbackState else { return .stopped }
        // p2p链接未成功连接过，此时 playbackState 为 paused, 这里手动返回 stopped，方便点击重试时 2.0sdk 播放器可以重试
        if !_hasP2PEverConnected, status == .paused {
            return .stopped
        }
        switch status {
        case .stopped, .interrupted: return .stopped
        case .playing: return .playing
        case .paused: return .paused
        case .seekingForward: return .seeking
        case .seekingBackward: return .seeking
        @unknown default:
            assertionFailure("未知的播放状态")
            return .stopped
        }
    }

    var pts: TimeInterval { player?.currentPlaybackTime ?? 0 }

    var isPlaying: Bool { player?.isPlaying() ?? false }
    
    var isRecording: Bool { _isRecording }

    func prepare() {
        player?.prepareToPlay()
    }
    
    
    func startPlay(forceRefresh: Bool) {
        if isPlaying {
            NIotLogger.error("[IVPlayer_v2] player is already playing, abort opt...")
            return
        }
        updateStatus(.preparing)
        startPlayV2DeviceLive(forceRequestXp2pInfo: forceRefresh)
    }
    
    func play() {
        if let player = player {
            if _hasP2PEverConnected {
                player.play()
            } else {
                NIotLogger.info("[IVPlayer_v2]\(self) 手动点击或者 appDidBecomeActive 重连")
                NIotNetworking.shared.getXp2pInfo(tid: deviceModel.device.tid) { [weak self] xp2pInfo in
                    guard let self = self, let info = xp2pInfo else { return }
                    self.reconnectXP2PWithDeviceName(self.deviceId, xp2pInfo: info)
                }
            }
        } else if _isServiceStarted {
            setVideoPlayerStartPlay(quality)
        }
        
    }

    func stop(cancelReconnect: Bool = false) {
        stopPlayMovie()
        updateStatus(.stopped)
        if cancelReconnect {
            NIotLogger.info("[IVPlayer_v2] stop manually.")
            shouldAutoReconnect = false
        }
    }

    func takeScreenshot(scale: CGFloat = 1, _ completionHandler: @escaping (UIImage?) -> Void) {
        let image = takeScreenshot(scale: scale)
        completionHandler(image)
    }

    func startRecording(_ savePath: String, completionHandler: @escaping RecordingCompletionHandler) {
        guard !_isRecording else { return }
        _isRecording = true
        // 外部拼接了吗？
        saveFilePath = savePath
        player?.startRecord(withFileName: savePath)
        _recordingCompletionHandler = completionHandler
    }

    func stopRecording() {
        guard let saveFilePath = saveFilePath else {
            assertionFailure("没有存储路径却可以点击停止录像，流程有问题")
            return
        }
        _isRecording = false
        player?.stopRecord()
        _recordingCompletionHandler?(saveFilePath, nil)
    }

    var connDelegate: IoTConnectionDelegate? {
        set {}
        get { return nil }
    }

    var sourceId: UInt16 { 0 }

    var channel: UInt32 {
        return UInt32(channel_v2) ?? 0
    }

    var iConnType: IoTConnType { .live }

    var iConnStatus: IoTConnStatus { .connected }

    
    
    func send(data: Data) -> Bool {
        guard let cmd = String(data: data, encoding: .utf8) else {
            return false
        }
        let fullCmd = "action=user_define&channel=\(channel)&cmd=\(cmd)"
        TIoTCoreXP2PBridge.sharedInstance().getCommandRequest(withAsync: deviceId, cmd: fullCmd, timeout: 2*1000*1000) { json in
            NIotLogger.info("[IVPlayer_v2]\(self) \(cmd) send success!")
            
        }
        return true
    }
    
    func send(data: Data?, sequence: Bool, multiCallback: @escaping IotMsgMultiRspCallback) {
        var shouldContinue = ObjCBool(false)
        guard let data = data,
              let cmd = String(data: data, encoding: .utf8) else {
            multiCallback(nil, NSError(error: "invalid cmd"), &shouldContinue)
            return
        }
        let fullCmd = "action=user_define&channel=\(channel)&cmd=\(cmd)"
        TIoTCoreXP2PBridge.sharedInstance().getCommandRequest(withAsync: deviceId, cmd: fullCmd, timeout: 2*1000*1000) { json in
            NIotLogger.info("[IVPlayer_v2] \(cmd) send success!")
            multiCallback(json.data(using: .utf8), nil, &shouldContinue)
        }
    }
}


extension IVPlayer_v2: IoTMonitorPlayer {
    
    /// 当前与设备对讲的客户端数量
    var talkerNum: UInt {
        0
    }
    
    /// 是否正在对讲
    var isTalking: Bool { _isTalking }
    
    /// 开启对讲
    func startTalking() {
        triggerTalking(true)
    }
     
    /// 开启对讲
    func startTalking(_ completionHandler: IoTSettingCallback?)  {
        triggerTalking(true, completionHandler: completionHandler)
    }
    
    /// 结束对讲
    func stopTalking() {
        triggerTalking(false)
    }
     
    /// 结束对讲
    func stopTalking(_ completionHandler: IoTSettingCallback?)  {
        triggerTalking(false, completionHandler: completionHandler)
    }
}


extension IVPlayer_v2: TIoTCoreXP2PBridgeDelegate {
    
    func getVideoPacket(withID dev_name: String, data: UnsafeMutablePointer<UInt8>, len: Int) {
        NIotLogger.info("[IVPlayer_v2]\(self) getVideoPacket length: \(len)")
    }
    
    func reviceDeviceMsg(withID dev_name: String, data: Data) -> String {
        NIotLogger.info("[IVPlayer_v2]\(self) reviceDeviceMsg: \(data.string(with: .utf8) ?? "")")
        // data 是 json 字符串:"{\"msg\":\"tf_format\", \"result\":\"success\"}"
        // result: success, failed, can't start
        if let json = try? JSONSerialization.jsonObject(with: data, options: []) as? [String: Any],
              let msg = json["msg"] as? String, msg == "tf_format",
              let result = json["result"] as? String {
            NotificationCenter.default.post(name: .formatTFCardResult, object: nil, userInfo: ["result": result])
        }
        return ""
    }
    
    func reviceEventMsg(withID dev_name: String, eventType: XP2PType, msg: UnsafePointer<CChar>) {
        guard player != nil else { return }
        if eventType.rawValue != 1001 {
            NIotLogger.info("[IVPlayer_v2]\(self) reviceEventMsg: \(eventType.rawValue)")
        }
        switch eventType {
        case .init(1001): break
        case .init(1003):
            NIotLogger.error("[IVPlayer_v2]\(self) p2p链路断开")
            if _hasRetryAfter1003 {
                playerDelegate?.player?(self, didReceivedError: NSError(error: "p2p链路断开"))
            } else {
                guard shouldAutoReconnect else { return }
                _hasRetryAfter1003 = true
                stopService()
                NotificationCenter.default.post(name: .retryPlayLiveFor2_0, object: nil, userInfo: ["deviceId": deviceId])
            }
        default: break
            
        }
    }
}

// MARK: - IoTPlayerDelegate

extension IVPlayer_v2 {
    
    func updateStatus(_ status: IoTPlayerStatus) {
        switch status {
        case .ready, .stopped:
            _isPreparingPlayV2DeviceLive = false
        case .playing:
            playerDelegate?.connection?(self, didUpdatedStatus: .connected)
        default: break
        }
        playerDelegate?.player?(self, didUpdatedStatus: status)
    }
    
    func startPlayV2DeviceLive(forceRequestXp2pInfo: Bool = false) {
        func setupAndPlay(xp2pInfo: String) {
            deviceModel.propertyModel?.xp2pInfoStr = xp2pInfo
            if isPlaying {
                NIotLogger.error("[IVPlayer_v2] player is already playing, abort opt...")
                return
            }
            setupXp2pInfo(xp2pInfo)
            // 2.0 sdk 设置了 xp2pinfo 之后会自动播放，不用再次调用 play()
            // livePlayerView.startPlay()
            if playerType == .live {
//                livePlayerView.updateUIWhenStarting()
            }
        }
        _isPreparingPlayV2DeviceLive = true
        switch onlineStatusForV2 {
        case .online:
            if IotMessager.shared.isMQTTConnected,
                !forceRequestXp2pInfo,
                let xp2pInfo = deviceModel.propertyModel?.xp2pInfoStr {
                setupAndPlay(xp2pInfo: xp2pInfo)
            } else {
                NIotNetworking.shared.getXp2pInfo(tid: deviceModel.device.tid) { xp2pInfo in
                    if let info = xp2pInfo {
                        NIotLogger.info("[IVPlayer_V2] forceRequestXp2pInfo")
                        setupAndPlay(xp2pInfo: info)
                    } else {
                        NIotLogger.error("[IVPlayer_V2] getXp2pInfo failed.")
                    }
                }
            }
        case .offline:
            NIotNetworking.shared.getXp2pInfo(tid: deviceModel.device.tid) { xp2pInfo in
                if let info = xp2pInfo {
                    NIotLogger.info("[IVPlayer_V2] forceRequestXp2pInfo")
                    setupAndPlay(xp2pInfo: info)
                } else {
                    NIotLogger.error("[IVPlayer_V2] getXp2pInfo failed.")
                }
            }
        case .dormant:
            NIotLogger.info("[IVPlayer_v2] device waking up...")
            _wakeUpTime = Date()
            IotMessager.shared.wakeDeviceUp(deviceModel.device.tid, productId: productId) { [weak self] tid, isSuccess in
                guard let self = self, tid == self.deviceModel.device.tid else { return }
                if isSuccess {
                    // 2.0设备唤醒后，等待物模型主动推过来的 xp2pInfo，主动推过来的才是正确可用的
                    self._playAfterReceiveXp2pInfoForV2 = true
                    self._wakeUpTime = nil
                    NIotLogger.info("[IVPlayer_v2] device wake up, waiting for xp2p info.")
                } else {
                    if let time = self._wakeUpTime, Date().timeIntervalSince(time) < 2 {
                        NIotLogger.error("[IVPlayer_v2] device wake up failed immediately, retry.")
                        DispatchQueue.main.asyncAfter(deadline: .now()+1) {
                            self.startPlayV2DeviceLive(forceRequestXp2pInfo: forceRequestXp2pInfo)
                        }
                    } else {
//                        self.livePlayerView.showTipsView(type: .connectError, errorCode: 8000)
                    }
                }
            }
        }
    }
    
    /// 接收到状态消息（ProReadonly）
    @objc func devicePropertyDidUpdate(_ notification: Notification) {
        guard let userInfo = notification.userInfo as? [String: String],
              userInfo["deviceId"] == deviceModel.device.tid,
              let json = userInfo["json"],
              let path = userInfo["path"]
        else { return }
        deviceModel.updatePropertyModel(json: json, path: path)
        if let propertyModel = deviceModel.propertyModel {
            
             // 如果界面停留在5分钟流量提示界面，则设备被唤醒情况下不进行直播
            let isFlowWarning = playerDelegate?.shouldWarn4GDataUsage?(for: self) ?? false
            if path == "_sys_xp2p_info",
               !isFlowWarning,
               _playAfterReceiveXp2pInfoForV2,
               playerType == .live {
                NIotLogger.info("[IVPlayer_v2] xp2p info received, start play.")
                _playAfterReceiveXp2pInfoForV2 = false
                if let xp2pInfo = propertyModel.xp2pInfoStr {
                    setupXp2pInfo(xp2pInfo)
                }
            }
            
            if deviceModel.isV2Device,
               path == "hostState",
               let hostStateValue = json.integerValue {
                // 直播时设备进入 OTA 升级（分享用户观看直播时，主用户开启 OTA 升级），停止直播
                if let hostState = PMHostState_v2(rawValue: hostStateValue),
                   hostState == .downloadStarted {
                    NIotLogger.info("[IVPlayer_v2] ⚠️⚠️⚠️device start upgrading.⚠️⚠️⚠️")
                    playerDelegate?.player?(self, didInterrupted: .otaUpgrade)
                }
            }
            
            if deviceModel.isV2Device, playerType == .live, path == PropertyModelPath.onlineStatus.path_v2, previousOnlineStatusForV2 != onlineStatusForV2 {
                if _isPreparingPlayV2DeviceLive,
                   onlineStatusForV2 == .dormant {
                    // 直播连接过程中休眠了，重新走流程
                    NIotLogger.error("[IVPlayer_v2] device went into sleep when start living, retry.")
                    startPlayV2DeviceLive(forceRequestXp2pInfo: true)
                } else if onlineStatusForV2 == .offline, !isFlowWarning, !_playAfterReceiveXp2pInfoForV2 {
                    NIotLogger.error("[IVPlayer_v2] device offline when playing living")
                    stop()
                    playerDelegate?.player?(self, didInterrupted: .offline)
                }
            }
               
        }
    }
}

extension IVPlayer_v2: IJKMediaNativeInvokeDelegate {
    func invoke(_ event: IJKMediaEvent, attributes: [AnyHashable : Any]! = [:]) -> Int32 {
        return 0
    }
    
    func onRemoteUserAudioFrame(_ pcmdata: UnsafeMutableRawPointer!, len pcmlen: Int32) {
        TIoTCoreXP2PBridge.sharedInstance().setRemoteAudioFrame(pcmdata, len: pcmlen)
    }
}
