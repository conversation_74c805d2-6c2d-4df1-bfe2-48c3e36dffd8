//
//  IoTExploreOrVideoDeviceModel.swift
//  NiView
//
//  Created by apple on 2022/3/16.
//  Copyright © 2022 nice. All rights reserved.
//

import UIKit
import HandyJSON
//import TIoTLinkKit

//
enum TIoTDeviceStatus: String, HandyJSONEnum {
    case success = "0"
    case deny = "1"
    case cmdNotFound = "2"
    case maxConnection = "3"
    case unsupport = "4"
    
    var desc: String {
        switch self {
        case .success: return "success"
        case .deny: return "拒绝请求"
        case .cmdNotFound: return "错误请求"
        case .maxConnection: return "连接APP数量超过最大连接数"
        case .unsupport: return "信令不支持"
        }
    }
}

class TIoTDeviceStatusModel: NSObject, HandyJSON {
    
    var status: TIoTDeviceStatus?
    var appConnectNum: String?
    var video_list: String?
    var video: TIoTDeviceVideoModel?
    var audio: TIoTDeviceAudioModel?
    
    required override init() {}
}

class TIoTDeviceAudioModel: NSObject, HandyJSON {
    
    var codecid: String?
    var samplerate: String?
    var channels: String?
    var bitwidth: String?
    
    required override init() {}
}

class TIoTDeviceVideoModel: NSObject, HandyJSON {
    
    var codecid: String?
    
    required override init() {}
}

public class TIoTLocalFileModel: NSObject, HandyJSON {
    
    public enum FileType: String, HandyJSONEnum {
        case pir = "10"
        case live = "11"
        case alltime = "12"
        case doorbell = "13"
        case all = "88"
        
        var string: String {
            switch self {
            case .pir: return "pir"
            case .live: return "live"
            case .alltime: return "alltime"
            case .doorbell: return "key"
            case .all: return ""
            }
        }
        
        public static func type(with string: String) -> FileType? {
            var type: FileType?
            switch string {
            case "pir": type = .pir
            case "live": type = .live
            case "alltime": type = .alltime
            case "key": type = .doorbell
            case "": type = .all
            default: break
            }
            return type
        }
    }
    
    var file_type: FileType?
    var file_name: String?
    var file_size: String?
    var start_time: String?
    var end_time: String?
    var extra_info: [String: Any]?
     
    public required override init() {}
}


class TIoTCloudEventsInfo: NSObject, HandyJSON {
    var _events: [TIoTCloudEventModel]?
    /// 视频播放URL
    var videoURL: String?
    /// 拉取结果是否已经结束
    var listover: Bool?
    /// 请求上下文, 用作查询游标
    var context: String?
    required override init() {}
    func mapping(mapper: HelpingMapper) {
        mapper <<< self._events <-- "events"
    }
}

public class TIoTCloudEventModel: NSObject, HandyJSON {
    var deviceId: String?
    public var _startTime: String?
    public var _endTime: String?
    var thumbnail: String?
    var EventId: String?
    
    public var downloadStatus: NVCloudDownloadStatus = .normal
    
    /// 供外部修改，TIoTCloudEventModel 作为占位符时用得到
    public var isV2_Modifiable = true
    public var VideoURL_v2: String?
    
    public required override init() {}
    public func mapping(mapper: HelpingMapper) {
        mapper <<< self._startTime <-- "startTime"
        mapper <<< self._endTime <-- "endTime"
        mapper <<< self.EventId <-- "eventId"
    }
}

/// 云存模型协议
public protocol CloudEventInfoRepresentable {
    var VideoURL_v2: String? { get }
    var events: [CloudEventRepresentable] { get }
}
extension TIoTCloudEventsInfo: CloudEventInfoRepresentable {
    var VideoURL_v2: String? { videoURL }
    var events: [CloudEventRepresentable] { _events ?? [] }
}
extension IVCSEventsInfo: CloudEventInfoRepresentable {
    var VideoURL_v2: String? { nil }
    var events: [CloudEventRepresentable] { list ?? [] }
}


/// 云存事件模型协议
public protocol CloudEventRepresentable {
    var is_v2: Bool { get }
    var VideoURL_v2: String? { get }
    var did: String? { get set }
    var tid: String? { get set }
    var eventId: String { get }
    var type: Int { get }
    var startTime: Int { get }
    var endTime: Int { get }
    var thumbnailURL: URL? { get }
    var downloadStatus: NVCloudDownloadStatus { get set }
    var service: IoTPlaybackService { get }
}

extension TIoTCloudEventModel: CloudEventRepresentable {
    public var is_v2: Bool { isV2_Modifiable }
    public var did: String? {
        get { deviceId }
        set { deviceId = newValue }
    }
    public var tid: String? {
        get { deviceId }
        set { deviceId = newValue }
    }
    public var eventId: String { EventId ?? "" }
    public var type: Int { 0 }
    public var startTime: Int { _startTime?.integerValue ?? 0 }
    public var endTime: Int { _endTime?.integerValue ?? 0 }
    public var thumbnailURL: URL? {
        if let url = thumbnail {
            return URL(string: url)
        }
        return nil
    }
    public var service: IoTPlaybackService {
        return is_v2 ? IoT.sdk_2.playbackService :  IoT.sdk_1.playbackService
    }
}

extension IVCSEvent: CloudEventRepresentable {
    var is_v2: Bool { false }
    var VideoURL_v2: String? { nil }
    var eventId: String { alarmId }
    var type: Int { alarmType }
    var thumbnailURL: URL? { thumbnailUrl }
    public var service: IoTPlaybackService { IoT.sdk_1.playbackService }
}

struct IVCSPlayListInfo: Codable {
    var list: [NVCSPlaybackItem]?
}

public struct IVCSPlayInfo: Codable {
    public var startTime: Int = 0
    public var endTime: Int = 0
    public var url: String?
    public var endflag: Bool = true
}

struct NVCSPlaybackItem: Codable {
    var start: Int = 0
    var end: Int = 0
}

class IVCSEventsInfo: Codable {
    var imgUrlPrefix: String? // 图片下载地址前缀
    var thumbUrlSuffix: String? //缩略图下载地址后缀
    var list: [IVCSEvent]?
}

class IVCSEvent: Codable {
    var alarmId: String    // 事件id
    var firstAlarmType: Int // 告警触发时的告警类型
    var alarmType: Int // 告警有效时间内触发过的告警类型
    var startTime: Int
    var endTime: Int
    var imgUrlSuffix: String? // 告警图片下载地址后缀
    var thumbUrlSuffix: String? // 告警图片下载地址后缀
    var downloadStatus: NVCloudDownloadStatus = .normal
    var did: String?            //设备id
    var tid: String?            //设备tid
    
    var thumbnailUrl: URL? //缩略图下载地址
    
    func configThumbnailURL(prefix: String, suffix: String) {
        guard let imgUrlSuffix = imgUrlSuffix else { return }
        thumbnailUrl = URL.init(string: prefix + imgUrlSuffix + suffix)
    }
    
    enum CodingKeys: String, CodingKey {
        case alarmId
        case firstAlarmType
        case alarmType
        case startTime
        case endTime
        case imgUrlSuffix
        // 如果不需要解码的字段可以直接忽略
    }
    
    required init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        self.alarmId = try container.decode(String.self, forKey: .alarmId)
        self.firstAlarmType = try container.decode(Int.self, forKey: .firstAlarmType)
        self.alarmType = try container.decode(Int.self, forKey: .alarmType)
        self.startTime = try container.decode(Int.self, forKey: .startTime)
        self.endTime = try container.decode(Int.self, forKey: .endTime)
        self.imgUrlSuffix = try container.decodeIfPresent(String.self, forKey: .imgUrlSuffix)
        // 可以直接忽略未解码的字段，或者做其他处理
    }
}

struct NVCSMarkItems: Codable {
    var list: [NVCSMarkItem] = []
}

typealias NVCSMarkItem = Int

public enum NVCloudDownloadStatus: Int, Codable {
    case normal = 0     //未下载
    case downloading    //下载中
    case downloaded     //下载完成
}
