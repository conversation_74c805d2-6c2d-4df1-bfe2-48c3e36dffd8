//
//  IoTConnection.swift
//  NiView
//
//  Created by apple on 2022/1/11.
//  Copyright © 2022 nice. All rights reserved.
//

import UIKit
//import IoTVideo

@objc
public enum IoTConnType: UInt {
    case live = 0
    case fileDownload = 6
    case thumbDownload = 7
}

@objc
public enum IoTConnStatus: Int {
    case disconnecting = -1
    case disconnected = 0
    case connecting = 1
    case connected = 2
}

/// 连接过程子状态
/// - note:  部分设备/场景是没有子状态的
@objc
public enum IoTConnSubstate: Int {
    /// 服务器已收到连接请求，正在唤醒设备
    case wakingUpDev = 1
    /// 设备已收到唤醒通知，开始握手过程
    case handshaking = 2
    /// 握手过程完成，连接通道已就绪
    case connectReady = 3
}

@objc
protocol IoTConnectionDelegate {
    /// 状态更新
    /// - Parameters:
    ///   - connection: 连接实例
    ///   - status: 状态
    @objc optional func connection(_ connection: IoTConnection, didUpdatedStatus status: IoTConnStatus)
    
    /// 连接过程子状态更新
    /// - note:  部分设备/场景是没有子状态的
    /// - Parameters:
    ///   - connection: 连接实例
    ///   - substate: 连接过程子状态
    @objc optional func connection(_ connection: IoTConnection, didUpdatedSubstate substate: IoTConnSubstate)
    
    /// 数据接收速率
    /// - Parameters:
    ///   - connection: 连接实例
    ///   - speed: 接收速率(字节/秒)
    @objc optional func connection(_ connection: IoTConnection, didUpdatedSpeed speed: UInt32)
    
    /// 收到错误
    /// - Parameters:
    ///   - connection: 连接实例
    ///   - error: 错误
    @objc optional func connection(_ connection: IoTConnection, didReceivedError error: Error)
    
    /// 收到数据
    /// - Parameters:
    ///   - connection: 连接实例
    ///   - data: 数据
    @objc optional func connection(_ connection: IoTConnection, didReceivedData data: Data)
}
 
class IoTConnectionDelegateProxy: NSObject, IVConnectionDelegate {
    
    unowned var _connection: IoTConnection
    weak var connDelegate: IoTConnectionDelegate?
    
    init(connection: IoTConnection) {
        _connection = connection
    }
       
    func connection(_ connection: IVConnection, didUpdate status: IVConnStatus) {
        guard let status = IoTConnStatus(rawValue: status.rawValue) else {
            return
        }
        connDelegate?.connection?(_connection, didUpdatedStatus: status)
    }
    
    func connection(_ connection: IVConnection, didUpdate substate: IVConnectingSubstate) {
        guard let substate = IoTConnSubstate(rawValue: substate.rawValue) else {
            return
        }
        connDelegate?.connection?(_connection, didUpdatedSubstate: substate)
    }
    
    func connection(_ connection: IVConnection, didUpdateSpeed speed: UInt32) {
        connDelegate?.connection?(_connection, didUpdatedSpeed: speed)
    }
    
    func connection(_ connection: IVConnection, didReceiveError error: Error) {
        connDelegate?.connection?(_connection, didReceivedError: error)
    }
    
    func connection(_ connection: IVConnection, didReceive data: Data) {
        connDelegate?.connection?(_connection, didReceivedData: data)
    }
}

@objc protocol IoTConnection {
    
    weak var connDelegate: IoTConnectionDelegate? { get set }
    /// 设备ID
    var deviceId: String { get }
    /// 源ID（一个设备 可以对应 多个源），默认为0
    var sourceId: UInt16 { get }
    /// 通道ID，连接成功该值才有效（一个设备+一个源 对应 唯一通道），无效值为-1
    var channel: UInt32 { get }
    /// 连接类型
    var iConnType: IoTConnType { get }
    /// 连接状态
    var iConnStatus: IoTConnStatus { get }
}

struct IoTAssociatedKeys {
    static var kPlayerDelegateProxy = "kPlayerDelegateProxy"
    static var kConnDelegateProxy = "kConnDelegateProxy"
}

extension IVConnection: IoTConnection {
    
    var connDelegateProxy: IoTConnectionDelegateProxy {
        get {
            objc_getAssociatedObject(self, &IoTAssociatedKeys.kConnDelegateProxy) as! IoTConnectionDelegateProxy
        }
        set {
            objc_setAssociatedObject(self, &IoTAssociatedKeys.kConnDelegateProxy, newValue, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
        }
    }
    weak var connDelegate: IoTConnectionDelegate? {
        get {
            connDelegateProxy.connDelegate
        }
        set {
            connDelegateProxy = IoTConnectionDelegateProxy(connection: self)
            connDelegateProxy.connDelegate = newValue
            delegate = connDelegateProxy
        }
    }
    
    var iConnType: IoTConnType {
        return IoTConnType(rawValue: connType.rawValue) ?? .live
    }
    
    var iConnStatus: IoTConnStatus {
        return IoTConnStatus(rawValue: connStatus.rawValue) ?? .disconnected
    }
}
