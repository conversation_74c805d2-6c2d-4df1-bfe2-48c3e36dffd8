//
//  IoTMonitorPlayable.swift
//  NiView
//
//  Created by apple on 2022/1/10.
//  Copyright © 2022 nice. All rights reserved.
//

import UIKit
//import IoTVideo

public typealias IoTSettingCallback = (Error?) -> Void
/// 多次数据响应回调     ⚠️不需要响应或结束响应后由APP开发者设置 `*finished = YES`，SDK内部就会移除响应监听！
public typealias IotMsgMultiRspCallback = (Data?, Error?, UnsafeMutablePointer<ObjCBool>) -> Void

@objc
public enum IoTVideoDefinition: UInt {
    case low = 0
    case mid = 1
    case high = 2
}

@objc
public enum IoTPlayerStatus: Int {
    case stopping = -1
    case stopped = 0
    case preparing = 1
    case ready = 2
    case loading = 3
    case playing = 4
    case paused = 5
    case fastForward = 6
    case seeking = 7
}

@objc protocol IoTPlayerDelegate: IoTConnectionDelegate {
    
    /// 播放器状态回调
    /// - Parameters:
    ///   - player: 播放器实例
    ///   - status: 状态值
    @objc optional func player(_ player: IoTPlayer, didUpdatedStatus status: IoTPlayerStatus)
    
    /// 播放时间回调
    /// - Parameters:
    ///   - player: 播放器实例
    ///   - PTS: 时间戳
    @objc optional func player(_ player: IoTPlayer, didUpdatedPTS PTS: TimeInterval)
    
    /// SD卡回放文件即将播放
    /// - Parameters:
    ///   - player: 播放器实例
    ///   - fileTime: 时间戳
    @objc optional func player(_ player: IoTPlayer, willBeginOfFile fileTime: TimeInterval)
    
    /// SD卡回放文件播放结束
    /// - Parameters:
    ///   - player: 播放器实例
    ///   - fileTime: 时间戳
    @objc optional func player(_ player: IoTPlayer, didEndOfFile fileTime: TimeInterval, error: Error?)
    
    /// 观看人数变更
    /// - Parameters:
    ///   - player: 播放器实例
    ///   - viewerNum: 观看人数
    @objc optional func player(_ player: IoTPlayer, didUpdatedViewerNum viewerNum: Int)
    
    /// 对讲人数变更
    /// - Parameters:
    ///   - player: 播放器实例
    ///   - talkerNum: 对讲人数
    @objc optional func player(_ player: IoTPlayer, didUpdatedTalkerNum talkerNum: Int)
    
    /// 播放错误回调
    ///
    /// 播放器捕获到的一些错误（不一定会导致播放中断），用于辅助开发者定位和发现问题，不要在此处stop()
    /// - Parameters:
    ///   - player: 播放器实例
    ///   - error: 错误实例
    @objc optional func player(_ player: IoTPlayer, didReceivedError error: Error)
    
    @objc optional func player(_ player: IoTPlayer, didReceivedAVHeader avHeader: Any)
    
    @objc optional func shouldWarn4GDataUsage(for player: IoTPlayer) -> Bool
    
    @objc optional func player(_ player: IoTPlayer, didInterrupted reason: NIotPlayerInterruptedReason)
}

/// 播放器层
@objc protocol IoTPlayer: IoTConnection {
    
    weak var playerDelegate: IoTPlayerDelegate? { get set }
    
    /// 视频清晰度
    var playerDefinition: IoTVideoDefinition { get set }
    
    /// 设置视频清晰度
    func setPlayerDefinition(_ definition: IoTVideoDefinition, completionHandler: IoTSettingCallback?)
    
    /// 静音，  默认 false（即允许播放声音）
    var mute: Bool { get set }
    
    /// 免提， 默认 true, 有外设时无效
    ///
    /// true 没有外设时外放声音,
    /// false  没有外设时听筒处播放声音
    var handsFree: Bool { get set }
    
    /// 视频画面
    var videoView: UIView { get }
    
    /// 当前设备观众人数
    var audience: UInt { get }
    
    /// 播放器状态
    var playerStatus: IoTPlayerStatus { get }
    
    /// 当前播放时间戳（秒）
    var pts: TimeInterval { get }
    
    /// 是否正在播放
    var isPlaying: Bool { get }
    
    /// 是否正在录像
    var isRecording: Bool { get } 
    
    /// 开始播放
    /// - note: 设备会发送流媒体信息头，接着发送音视频数据
    func startPlay(forceRefresh: Bool)
    
    /// 播放
    func play()
    
    /// 停止播放
    /// - note: 该操作APP将与设备断开连接
    func stop(cancelReconnect: Bool)
    
    /// 截图
    /// - Parameter completionHandler: 截图回调
    func takeScreenshot(scale: CGFloat, _ completionHandler: @escaping (UIImage?) -> Void)
    
    /// 开始录像
    /// - Parameters:
    ///   - savePath: 录像文件路径
    ///   - completionHandler: 完成回调
    func startRecording(_ savePath: String, completionHandler: @escaping (_ savePath: String?, _ error: Error?) -> Void)
    
    /// 停止录像
    func stopRecording()
    
    /// 发送自定义数据
    func send(data: Data) -> Bool
     
    /// 发送自定义数据
    /// - Parameters:
    ///   - data: 要发送的数据，data.length不能超过`MAX_PKG_BYTES`
    ///   - sequence: 是否串行发送
    ///   - multiCallback: callback非nil则通过callback回调；callback为nil则通过代理回调；
    func send(data: Data?, sequence: Bool, multiCallback: @escaping IotMsgMultiRspCallback)
    
    func stopService(_ willDisappear: Bool)
}

class IoTPlayerDelegateProxy: IoTConnectionDelegateProxy, IVPlayerDelegate {
   
    unowned var _player: IoTPlayer
    
    weak var playerDelegate: IoTPlayerDelegate? {
        didSet {
            connDelegate = playerDelegate
        }
    }
    
    init(player: IoTPlayer) {
        _player = player
        super.init(connection: player)
    }
    
    func player(_ player: IVPlayer, didUpdate status: IVPlayerStatus) {
        guard let status = IoTPlayerStatus(rawValue: status.rawValue) else {
            return
        }
        playerDelegate?.player?(_player, didUpdatedStatus: status)
    }
    
    func player(_ player: IVPlayer, didUpdatePTS PTS: TimeInterval) {
        playerDelegate?.player?(_player, didUpdatedPTS: PTS)
    }
    
    func player(_ player: IVPlayer, willBeginOfFile fileTime: TimeInterval) {
        playerDelegate?.player?(_player, willBeginOfFile: fileTime)
    }
    
    func player(_ player: IVPlayer, didEndOfFile fileTime: TimeInterval, error: Error?) {
        playerDelegate?.player?(_player, didEndOfFile: fileTime, error: error)
    }
    
    func player(_ player: IVPlayer, didUpdateViewerNum viewerNum: Int) {
        playerDelegate?.player?(_player, didUpdatedViewerNum: viewerNum)
    }
    
    func player(_ player: IVPlayer, didUpdateTalkerNum talkerNum: Int) {
        playerDelegate?.player?(_player, didUpdatedTalkerNum: talkerNum)
    }
    
    func player(_ player: IVPlayer, didReceiveError error: Error) {
        playerDelegate?.player?(_player, didReceivedError: error)
    }
    
    func player(_ player: IVPlayer, didReceive avHeader: IVAVHeader) {
        playerDelegate?.player?(_player, didReceivedAVHeader: avHeader)
    }
}
