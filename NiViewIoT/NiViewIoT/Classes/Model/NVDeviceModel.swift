//
//  NVDeviceModel.swift
//  NiView
//
//  Created by nice on 2020/8/7.
//  Copyright © 2020 nice. All rights reserved.
//

import UIKit
import HandyJSON

//WIFI :
//   J1D1 , J1C1 , J4C2P0 , J1C3 , J3C1 , J3C5P1 , J3C5P2 , J4C1P0 , J4C1P1
//4G:
//   J1C2 , J4C2P1 , J2C4 , J3C2 , J3C5P3 , J4C1P2 , J2C2P0
//POE:
//   J3C5P0
//上下双目：
//   J3C1 , J3C2
//拼接双目：
//   J3C5P0 , J3C5P1 , J3C5P2 , J3C5P3


public
enum NVDeviceType: String, CaseIterable {
    case C0 = "C0"      //
    case J1C1 = "J1C1"  //WiFi XTP 摄像机
    case H1C1 = "H1C1"  //海思
    case C2 = "J1C2"    //4G低功耗 摇头机
    case C3 = "J1C3"    //WiFi 摇头机 去MCU版本
    case C4 = "J2C4"    //4G常上电 摇头机
    case D1 = "J1D1"    //WiFi XTP 门铃
    case J3C2 = "J3C2"  //双摄设备
    case J3C1 = "J3C1"  //双摄设备
    case J3C3 = "J3C3"  // WIFI双摄
    case J3C4 = "J3C4"  // 4G双摄
    case J3C3P0 = "J3C3P0" // Z2R wifi+4g
    case J3C5P0 = "J3C5P0" // 拼接双摄 有线
    case J3C5P1 = "J3C5P1" // 拼接双摄
    case J3C5P2 = "J3C5P2" // 拼接双摄
    case J3C5P3 = "J3C5P3" // 拼接双摄 4G
    case J3C6P0 = "J3C6P0" // T6 wifi
    case J3C6Q0 = "J3C6Q0" // T6 wifi 2.0sdk
    case J3C7P1 = "J3C7P1" // Z6R wifi+4g
    case J3C8P0 = "J3C8P0" // M6 wifi+4g
    case J4C1P0 = "J4C1P0"
    case J4C1P1 = "J4C1P1"
    case J4C1P2 = "J4C1P2" // 4G
    case J4C1Q1 = "J4C1Q1" // T5
    case J4C2P0 = "J4C2P0"
    case J4C2P1 = "J4C2P1" // 4G
    case J4C3P1 = "J4C3P1" // T5B wifi
    case J4C3P2 = "J4C3P2" // Z5B 4g
    case J2C2P0 = "J2C2P0" // 4G
    case J1T1C0 = "J1T1C0" // 4G
    case J1T2C0 = "J1T2C0"
    case J1C4G0 = "J1C4G0"
    case J6C2Q0 = "J6C2Q0" // G1 单目低功耗4G球机
    case J6C3Q0 = "J6C3Q0" // G2 双目低功耗4G定焦枪球
    case J6C4Q0 = "J6C4Q0" // G3 双目低功耗4G长短焦枪球
    case J6C7Q0 = "J6C7Q0" // W3 双目低功耗WiFi长短焦枪球
    case J7C1Q0 = "J7C1Q0" // 双目AOV 400W+400W黑光 wiif+4G球
    
    
    var imageName: String {
        var name = ""
        switch self {
        case .C0, .C2, .C3, .C4, .J2C2P0, .J6C2Q0:
            name = "device_icon_t2_z1_m1"
        case .J1C1, .H1C1:
            name = "device_icon_j1c1"
        case .D1:
            name = "device_icon_d1"
        case .J3C1, .J3C2, .J3C3, .J3C3P0, .J3C4, .J4C2P0, .J4C2P1:
            name = "device_icon_t3a_z2"
        case .J3C5P0, .J3C5P1, .J3C5P2, .J3C5P3:
            name = "device_icon_series_4"
        case .J3C6P0, .J3C6Q0, .J3C7P1, .J3C8P0, .J6C3Q0, .J6C4Q0, .J6C7Q0, .J7C1Q0:
            name = "device_icon_series_6"
        case .J4C1P0, .J4C1P1, .J4C1P2, .J4C1Q1, .J4C3P1, .J4C3P2:
            name = "device_icon_series_5" // 4G
        case .J1T1C0, .J1T2C0:
            name = "device_icon_t1_z3"
        case .J1C4G0:
            name = "device_icon_t10"
        }
        return name
    }
}

public
enum DeviceStatus: Int {
    case offline = 0, online, suspend
}

open
class DeviceMccInfo: HandyJSON {
    /// 设备的MCC地区，空值为未匹配上
    public var deviceMccRegion: String = ""
    /// 请求接口获取的MCC地区，空值为未匹配上
    public var simMccRegion: String = ""
    /// 所使用套餐包的MCC地区，空值为未匹配上
    public var packageMccRegion: String = ""
    
    required public init() {}
    /**
     AUS    澳洲
     RH    日韩
     XMT    新马泰
     EUR    欧洲
     CAN    加拿大
     USA    美国
     NF    不在服务区
          未能成功获取
     */
}
open
class NVDeviceInfo: HandyJSON {
    public var id: Int64!
    public var tid: String!
    public var devName: String?
    public var devType: String!
    public var freeCloud: Int?   //"1"：可以免费领取
    // "1"表示免费云存广告，主用户+freeCloud==1+isShowAds==1 进直播页面弹窗
    public var isShowAds: Int?
    /// 具体的免费云存套餐，值为 free-ye1w7d、free-ye1m3d、free-ye1m7d、free-ye1m30d、free-ye1y3d、free-ye1y7d、free-ye1y30d
    public var freeContent: String?
    public var nid: Int64?
    public var lastCloudEndTime: Double?
    public var iccid: String?
    /// SDK 版本
    public var sdkVersion = 1
    public var sdk2ProductId: String?
    /// v2
    public var channel = 0
    
    public var password = ""
    /// 设备网络模式，0：默认，1：WiFi，2：4G
    /// 监听物模型ProReadonly.DeviceNetMode（0：wifi，1：4G），判断与netmode状态是否不同，不同时刷新设备状态和UI
    public var netMode: Int?
    /// 手机卡类型
    ///
    /// 0：表示当前4g设备的iccid卡为自购卡，不显示云存购买按钮和云存相关列表
    /// 1：表示当前4g设备的iccid卡为用户私卡，显示云存购买按钮和云存相关列表
    public var iccidCardType: Int?
    public var canBuyCloud: Int?
    
    // 海外卡=奈斯非无限流量卡（五兴）
    // 国内卡=奈斯无限流量卡（量讯）
    
    /// SIM卡的所属平台，参考下表
    /// 0    用户第三方卡
    /// 1    量讯国内
    /// 2    联通
    /// 3    千鸟
    /// 4    斯麦特
    /// 5    广和通_联通
    /// 6    广和通_移动
    /// 7    广和通_电信
    /// 8    广和通_国际
    /// 9   量讯海外
    /// 20  五兴海外
    /// 21  五兴联通
    /// 22  五兴移动
    /// 23  五兴电信
    public var platform: Int?
    
    /// 国内卡
    public var isInternalCard: Bool {
        return platform == 1
        || platform == 5
        || platform == 6
        || platform == 7
        || platform == 21
        || platform == 22
        || platform == 23
    }
    /// 国外卡
    public var isOversea4GSIMCard: Bool {
        return platform == 8 || platform == 9
    }
    
    public var mccInfo: DeviceMccInfo?
    /// 延保选项，1=是 0=否
    public var isExtendedWarranty = 0
    
    public var isCustomerCard: Bool {
        if let iccidCardType = iccidCardType {
            return iccidCardType == 1
        } else {
            return false // 没有值时，走原来逻辑，所以返回 false
        }
    }
    
    public var devImageStr: String {
        guard let devType = devType else { return "" }
        let type = NVDeviceType.init(rawValue: devType)
        var imageStr = type?.imageName ?? ""
        if imageStr.isEmpty {
            imageStr = NVDeviceType.allCases
                .filter { devType.contains($0.rawValue) }
                .first.map { $0.imageName } ?? ""
        }
        return imageStr
    }
    
    required public init() {}
    public func mapping(mapper: HelpingMapper) {
        mapper <<< self.iccidCardType <-- "ownership"
        mapper <<< self.sdkVersion <-- "tVersion"
        mapper <<< self.sdk2ProductId <-- "productId"
    }
}

public class NVDeviceSharedAuthConfig: HandyJSON {
    public var sd_play = 0
    public var cloud_play = 0
    public var push = 0
    public var ptz = 0
    public var talk = 0
    public var setting = 0
    required public init() {}
}
/// 设备当前操作状态
public
enum DeviceOperationStatus {
    case none
    /// 正在自动标定
    case duringAutoDemarcating
    /// 正在变焦校准
    case duringZoomCalibration
}

open
class NVDeviceModel: HandyJSON, Equatable {
    public var isV2Device: Bool { device.sdkVersion == 2 }
    public var isAdmin: Bool { role == 0 }
    
    /// 是否已被取消分享
    public var isUnbound = false
    public var did: Int64!
    public var id:  Int64!
    public var uid: Int64!
    public var role: Int!
    public var sort: Int?
    public var device: NVDeviceInfo!
    public var iccid: String? { device.iccid ?? propertyModel?.wirelessModInfo?.iccid }
    /// 摇头机预置位和看守位信息
    public var presetPosInfo: [NVDevicePresetInfo] = []
    /// 物模型实例
    public var propertyModel: PropertyModel?
    /// 设备当前操作状态，目前仅考虑自动标定、变焦校准两个操作（用于判断显示 4G 设备 5 分钟观看提示、点击自动对焦时判断）
    public var operationStatus = DeviceOperationStatus.none
    
    public class OperationValue {
        public var talkerNum = 0
        /// 锁焦时间，默认 2 分钟
        public var lockFocusTimeout = 2
    }
    
    public let operationValue = OperationValue()
    
    /// 分享用户的操作权限
    public var authConfig: NVDeviceSharedAuthConfig?
    public var isSupportSDPlay: Bool { isAdmin || authConfig?.sd_play == 1 }
    public var isSupportCloudPlay: Bool { isAdmin || authConfig?.cloud_play == 1 }
    public var isSupportPushNotification: Bool { isAdmin || authConfig?.push == 1 }
    public var isSupportPTZ: Bool { isAdmin || authConfig?.ptz == 1 }
    public var isSupportTalk: Bool { isAdmin || authConfig?.talk == 1 }
    public var isSupportSettings: Bool { isAdmin || authConfig?.setting == 1 }
    
    public func updatePropertyModel(json: String, path: String) {
        var json = json
        if isV2Device {
            var isUsingLiveChannelToCheckSd = false
            if propertyModel?.funcSet?.isValid ?? false {
                isUsingLiveChannelToCheckSd = propertyModel?.funcSet?.contains(.isUsingLiveChannelToCheckSd) ?? false
            } else if path.isEmpty, let pm = PropertyModel_v2.deserialize(from: json) {
                isUsingLiveChannelToCheckSd = pm.funcSet?.contains(.isUsingLiveChannelToCheckSd) ?? false
            }
            if isUsingLiveChannelToCheckSd {
                // 如果设备支持使用直播通道检查SD卡，则不更新物模型
                if path == "storage" {
                    NIotLogger.info("[PropertyModel] 不更新物模型，因为设备支持使用直播通道检查SD卡")
                    return
                } else if path.isEmpty, var jsonDict = json.stringValueDic() {
                    // 从 json 中移除 storage 字段
                    NIotLogger.info("[PropertyModel] 从 json 中移除 storage 字段")
                    jsonDict.removeValue(forKey: "storage")
                    json = jsonDict.toString() ?? ""
                }
            }
        }
        var old = propertyModel
        if old == nil {
            old = isV2Device ? PropertyModel_v2() : PropertyModel_v1()
        }
        let oldPresetPosInfo = propertyModel?.presetPosInfo
        
        // 2.0sdk的全部物模型里没有 onlineStatus，onlineStatus 是通过 MQTT 异步推送过来的，所以全部物模型更新时，会把原有的 onlineStatus 覆盖掉，这里进行处理
        var oldOnlineStatus: PMOnlineStatus?
        if isV2Device, path.isEmpty, let tid = device.tid {
            oldOnlineStatus = propertyModel?.onlineStatus ?? DeviceMgr.getDeviceOnlineStatus(tid: tid)
        }
        var new = old?.update(json, path: path)
        if isV2Device, let oldOnlineStatus = oldOnlineStatus {
            new?.onlineStatus = oldOnlineStatus
        }
        propertyModel = new
        // TODO: 暂时先在这里处理，后续移至 PropertyModel_v1 内
        if let _ = oldPresetPosInfo {
            // 如果是物模型更新操作，不更新预置位的信息（旧版本就是这么处理的），更新了会导致预置位的添加删除出数据不同步
        } else if let info = propertyModel?.presetPosInfo {
            // 如果是初始化，则更新数据
            presetPosInfo = NVDevicePresetInfo.setDevicePresetInfos(info, isV2Device: isV2Device)
        } else if isV2Device {
            presetPosInfo = NVDevicePresetInfo.defaultPresetInfosV2()
        }
    }
    
    public var flowExpirationTime: Int?
    public var remainFlowData: Double?
    public var portraitVideoScale: CGFloat?     //竖屏下播放器的缩放比例
    
    /// 是否是4G设备(先通过服务器返回的数值判断，再能力集判断，能力集为空则用C2C3C4判断
    public var is4gDevice: Bool {
        if let netMode = device.netMode {
            switch netMode {
            case 1: return false
            case 2: return true
            default: break
            }
        }
        if let funcSet = propertyModel?.funcSet, funcSet.isValid {
            return funcSet.contains(.cellular)
        }
        let devType: String = device.devType
        return devType.contains(NVDeviceType.C2.rawValue) || devType.contains(NVDeviceType.C4.rawValue) || devType.contains(NVDeviceType.J3C2.rawValue) || devType.contains(NVDeviceType.J3C5P3.rawValue) || devType.contains(NVDeviceType.J4C1P2.rawValue) || devType.contains(NVDeviceType.J4C2P1.rawValue) || devType.contains(NVDeviceType.J4C3P2.rawValue) || devType.contains(NVDeviceType.J2C2P0.rawValue) || devType.contains(NVDeviceType.J1T1C0.rawValue) || devType.contains(NVDeviceType.J6C2Q0.rawValue) || devType.contains(NVDeviceType.J6C3Q0.rawValue) || devType.contains(NVDeviceType.J6C4Q0.rawValue) || devType.contains(NVDeviceType.J7C1Q0.rawValue)
    }
    
    // 是否双摄
    public var isDoubleCamera: Bool {
        return device.devType.contains(NVDeviceType.J3C1.rawValue)
        || device.devType.contains(NVDeviceType.J3C2.rawValue)
        || device.devType.contains(NVDeviceType.J3C3.rawValue)
        || device.devType.contains(NVDeviceType.J3C3P0.rawValue)
        || device.devType.contains(NVDeviceType.J3C4.rawValue)
        || device.devType.contains(NVDeviceType.J3C6P0.rawValue)
        || device.devType.contains(NVDeviceType.J3C6Q0.rawValue)
        || device.devType.contains(NVDeviceType.J3C7P1.rawValue)
        || device.devType.contains(NVDeviceType.J3C8P0.rawValue)
        || device.devType.contains(NVDeviceType.J6C3Q0.rawValue)
        || device.devType.contains(NVDeviceType.J6C4Q0.rawValue)
        || device.devType.contains(NVDeviceType.J6C7Q0.rawValue)
//        || device.devType.contains(NVDeviceType.J7C1Q0.rawValue)
    }
    /// 分辨率操作是否由信令控制
    public var isResolutionOptBySignal: Bool {
        return device.devType.contains(NVDeviceType.J4C1P0.rawValue)
        || device.devType.contains(NVDeviceType.J4C1P1.rawValue)
        || device.devType.contains(NVDeviceType.J4C1P2.rawValue)
        || device.devType.contains(NVDeviceType.J4C3P1.rawValue)
        || device.devType.contains(NVDeviceType.J4C3P2.rawValue)
        || device.devType.contains(NVDeviceType.J3C7P1.rawValue)
        || device.devType.contains(NVDeviceType.J3C8P0.rawValue)
    }
    
    /// 刻度数字 UI 显示 20x
    /// 需求：T5/Z5最大倍数改为20，只修UI改对应刻度数字显示，实际逻辑不用修改，包括传给设备的倍数信息
    public var shouldFake20x: Bool {
        return device.devType.contains(NVDeviceType.J4C1P1.rawValue)
        || device.devType.contains(NVDeviceType.J4C1P2.rawValue)
        || device.devType.contains(NVDeviceType.J4C3P1.rawValue)
        || device.devType.contains(NVDeviceType.J4C3P2.rawValue)
    }
    /// 水平拼接设备
    public var isHorizontalDualCamera: Bool {
        return device.devType.contains(NVDeviceType.J3C5P0.rawValue) || device.devType.contains(NVDeviceType.J3C5P1.rawValue) || device.devType.contains(NVDeviceType.J3C5P2.rawValue) || device.devType.contains(NVDeviceType.J3C5P3.rawValue)
    }
    
    /// 是否支持追踪标定：人形追踪+云台+双摄+手指追踪，可支持追踪标定
    public var isSupportTrackingDemarcating: Bool {
        guard let funcSet = propertyModel?.funcSet else { return false }
        return funcSet.contains(.shaking)
        && funcSet.contains(.personTrack)
        && funcSet.contains(.fingerTrack)
        && !funcSet.contains(.fingerTrackNoCali)
        && isDoubleCamera
    }
    
    public var isSeries5Device: Bool {
        return device.devType.contains(NVDeviceType.J4C1P0.rawValue)
        || device.devType.contains(NVDeviceType.J4C1P1.rawValue)
        || device.devType.contains(NVDeviceType.J4C1P2.rawValue)
        || device.devType.contains(NVDeviceType.J4C3P1.rawValue)
        || device.devType.contains(NVDeviceType.J4C3P2.rawValue)
    }
    
    public var isSeries6Device: Bool {
        return device.devType.contains(NVDeviceType.J3C6P0.rawValue)
        || device.devType.contains(NVDeviceType.J3C7P1.rawValue)
        || device.devType.contains(NVDeviceType.J3C8P0.rawValue)
    }
    
    /// 4K图标和文案改成2K
    public var shouldTurn4KintoUHD: Bool {
        return isSeries5Device
        || isSeries6Device
        || device.devType.contains(NVDeviceType.J6C3Q0.rawValue)
        || device.devType.contains(NVDeviceType.J6C4Q0.rawValue)
    }
    
    public var shouldShowFreeCloudPkgAds: Bool {
        let shouldRemindToday: Bool = {
            if let mappings = UserDefaults.standard.object(forKey: "FreeCloudPkgReminder") as? [String: Date], let tid = device.tid, let previousRemindDate = mappings[tid] {
                return !Calendar.current.isDateInToday(previousRemindDate)
            } else {
                return true
            }
        }()
        return isAdmin
        && device.freeCloud == 1
        && device.isShowAds == 1
        && shouldRemindToday
    }
    
    public func dontRemindFreeCloudPkgToday() {
        guard let tid = device.tid else { return }
        var mappings = UserDefaults.standard.object(forKey: "FreeCloudPkgReminder") as? [String: Date]
        if mappings == nil {
            mappings = [:]
        }
        mappings![tid] = Date()
        UserDefaults.standard.set(mappings!, forKey: "FreeCloudPkgReminder")
        UserDefaults.standard.synchronize()
    }
    
    //
    public var newVersion: String?
    
    required public init() {}
    
    public static func == (lhs: NVDeviceModel, rhs: NVDeviceModel) -> Bool {
        lhs.device.id == rhs.device.id
    }
    
    public func shouldUpdateUIWhenUpdatingProperty(_ json: String, path: String) -> Bool {
        if isV2Device {
            return path == PropertyModelPath.batteryInfo.path_v2
            || path == PropertyModelPath.onlineStatus.path_v2
        } else {
            if path.length > 0 {
                return path == kProReadonly + kProBatteryInfo
                || path == kProReadonly + kProOnline
                || path == "ProReadonly"
                || path == PropertyModelPath.onlineStatus.path_v2
            } else {
                return json.stringValueDic()?["ProReadonly"] != nil
            }
        }
    }
    
    var is4GV2Device: Bool { isV2Device && is4gDevice }
    public var imeiFromPropertyModel: String? {
        let propertiesV2 = propertyModel as? PropertyModel_v2
        return is4GV2Device ? propertiesV2?.net4GInfo?.imei : propertyModel?.wirelessModInfo?.imei
    }
    public var iccidFromPropertyModel: String? {
        let propertiesV2 = propertyModel as? PropertyModel_v2
        return is4GV2Device ? propertiesV2?.net4GInfo?.iccid : propertyModel?.wirelessModInfo?.iccid
    }
    public var operatorFromPropertyModel: String? {
        let propertiesV2 = propertyModel as? PropertyModel_v2
        return is4GV2Device ? propertiesV2?.net4GInfo?.oper : propertyModel?.wirelessModInfo?._operator
    }
    public var currentIPFromPropertyModel: String? {
        let propertiesV2 = propertyModel as? PropertyModel_v2
        return is4GV2Device ? propertiesV2?.net4GInfo?.ipAddr : propertyModel?.wirelessModInfo?.current_ip
    }
    public var rssiFromPropertyModel: Int? {
        let propertiesV2 = propertyModel as? PropertyModel_v2
        return is4GV2Device ? propertiesV2?.net4GInfo?.signal : propertyModel?.wirelessModInfo?.rssi
    }
    public var modVersionFromPropertyModel: String? {
        let propertiesV2 = propertyModel as? PropertyModel_v2
        return is4GV2Device ? propertiesV2?.net4GInfo?.version : propertyModel?.wirelessModInfo?.version
    }
    
    public var deviceOnlineStatus: PMOnlineStatus? {
        return isV2Device ? DeviceMgr.getDeviceOnlineStatus(tid: device.tid) : propertyModel?.onlineStatus
    }
}

let kUserDefaultsDeviceFuncList = "funcList"
let kUserDefaultsDeviceTFCardState = "tfcardState"

open
class DeviceMgr {
    
    ///MARK - 缓存设备的配置项
    public static func writeDeviceOption(devID: String, key: String, value: Any) {
        if var devDict = UserDefaults.standard.value(forKey: devID) as? [String: Any],
           var optionsDict = devDict[devID] as? [String: Any] {
            optionsDict[key] = value
            devDict[devID] = optionsDict
            UserDefaults.standard.setValue(devDict, forKey: devID)
            UserDefaults.standard.synchronize()
        } else {
            let devDict = [devID: [key: value]]
            UserDefaults.standard.setValue(devDict, forKey: devID)
            UserDefaults.standard.synchronize()
        }
    }

    public static func readDeviceOption(devID: String, key: String) -> Any? {
        if let devDict = UserDefaults.standard.value(forKey: devID) as? [String: Any],
           let optionsDict = devDict[devID] as? [String: Any] {
            return optionsDict[key]
        }
        
        return nil
    }
    public static func removeDeviceOption(devID: String, key: String) {
        guard devID.length > 0 else { return }
        if var devDict = UserDefaults.standard.value(forKey: devID) as? [String: Any],
           var optionsDict = devDict[devID] as? [String: Any] {
            optionsDict.removeValue(forKey: key)
            devDict[devID] = optionsDict
            UserDefaults.standard.setValue(devDict, forKey: devID)
            UserDefaults.standard.synchronize()
        }
    }
    /// 设备在线状态临时映射
    ///
    /// 因为获取设备列表后，会保存设备列表数据到NVEnvironment.shared.devices，获取列表的同时服务端 MQTT 会自动推送设备在线状态给App
    /// 所以当启动 App 获取设备列表时，服务端推送的时机与获取物模型时机先后顺序无法保证，当推送先于物模型获取时，此时NVEnvironment.shared.devices没有设备数据缓存，MQTT 推送过来的设备状态无法缓存，导致设备状态都是离线
    /// 所以需要这个临时映射来临时缓存设备在线状态
    static var tmpDeviceOnlineStatusMapping = [String: PMOnlineStatus]()
    public static func storeDeviceOnlineStatus(tid: String, status: String) {
        if let value = Int(status), let status = PMOnlineStatus(rawValue: value) {
//            NIotLogger.info("[onlineStatus]<\(tid)> store online status: \(status)")
            tmpDeviceOnlineStatusMapping[tid] = status
        }
    }
    static func getDeviceOnlineStatus(tid: String) -> PMOnlineStatus {
        if tmpDeviceOnlineStatusMapping[tid] == nil {
            NIotLogger.error("[onlineStatus]<\(tid)> online status is nil")
        }
        return tmpDeviceOnlineStatusMapping[tid] ?? .offline
    }
}

open
class NVDevicePresetInfo {
    public var typeName: String!
    public var enable: Int!
    public var nickName: String!
    // 这个变量处理kingfisher 的图片缓存问题
    public var needRefreshImage = true
     
    public init(typeName: String, enable: Int?, nickName: String?) {
        self.typeName = typeName
        self.enable = enable ?? 0
        self.nickName = nickName ?? ""
    }
    public static func setDevicePresetInfos(_ presetPosInfo: [String: Any], isV2Device: Bool) -> [NVDevicePresetInfo] {
        if isV2Device {
            return setDevicePresetInfosV2(presetPosInfo)
        }
        return setDevicePresetInfos(presetPosInfo)
    }
    
    static func setDevicePresetInfos(_ presetPosInfo: [String: Any]) -> [NVDevicePresetInfo] {
        var presetInfoArr: [NVDevicePresetInfo] = []
        for keyStr in presetPosInfo.keys {
            if let posDict = presetPosInfo[keyStr] as? [String: Any] {
                let presetInfo = NVDevicePresetInfo(typeName: keyStr, enable: posDict["enable"] as? Int, nickName: posDict["nickname"] as? String)
                presetInfoArr.append(presetInfo)
            }
            //把看守位也当成预置位中的一项，然后通过排序放到第一位
            if let guardPos = presetPosInfo[keyStr] as? Int {
                let guardInfo = NVDevicePresetInfo(typeName: keyStr, enable: guardPos, nickName: "guard")
                presetInfoArr.append(guardInfo)
            }
        }
        return presetInfoArr.sorted(by: { $0.typeName < $1.typeName })
    }
    
    /// 2.0 设置物模型
    static func setDevicePresetInfosV2(_ presetPosInfo: [String: Any]) -> [NVDevicePresetInfo] {
        var presetInfoArr: [NVDevicePresetInfo] = []
        for keyStr in presetPosInfo.keys {
            if let nickname = presetPosInfo[keyStr] as? String {
                // 2.0 没有 enable 字段，根据 nickname 是否有效来判断
                let presetInfo = NVDevicePresetInfo(typeName: keyStr, enable: 1, nickName: nickname)
                presetInfoArr.append(presetInfo)
            }
            //把看守位也当成预置位中的一项，然后通过排序放到第一位
            if let guardPos = presetPosInfo[keyStr] as? Int {
                let guardInfo = NVDevicePresetInfo(typeName: keyStr, enable: guardPos, nickName: "guard")
                presetInfoArr.append(guardInfo)
            }
        }
        return presetInfoArr.sorted(by: { $0.typeName < $1.typeName })
    }
    
    public static func defaultPresetInfosV2() -> [NVDevicePresetInfo] {
        var presetInfoArr = [NVDevicePresetInfo(typeName: "guard", enable: 0, nickName: "guard")]
        for i in 1...9 {
            let pos = "pos" + "\(i)"
            let presetInfo = NVDevicePresetInfo(typeName: pos, enable: 1, nickName: "")
            presetInfoArr.append(presetInfo)
        }
        return presetInfoArr
    }
    
    /// 2.0 数组转为字典
    static func presetInfosToDictV2(_ presetInfos: [NVDevicePresetInfo]) -> [String: Any] {
        var presetDict = [String: Any]()
        for presetInfo in presetInfos {
            if presetInfo.nickName == "guard" {
                presetDict[presetInfo.typeName] = presetInfo.enable
            } else {
                presetDict[presetInfo.typeName] = presetInfo.nickName
            }
        }
        return presetDict
    }
    
}
