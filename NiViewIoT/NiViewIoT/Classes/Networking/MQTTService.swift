//
//  MQTTService.swift
//  NiView
//
//  Created by apple on 2023/8/25.
//  Copyright © 2023 nice. All rights reserved.
//

import UIKit
import CocoaMQTT
import HandyJSON


struct MQTTConfig: HandyJSON {
    var username: String?
    var password: String?
    var urls: String?
    public init() {}
}

class IotMessager: MQTTServiceDelegate {
    
    enum MessageType: Int, HandyJSONEnum {
        case wakeUp = 30
        case wakeUpSuccess = 31
        case wakeUpNotChannelOnline = 35
        case wakeUpNotChannelOffline = 36
        case deviceUpdateModel = 40
        case deviceStatusOnline = 51
        case deviceStatusSleep = 52
        case deviceStatusOffline = 53
        
        var typeName: String {
            switch self {
            case .wakeUp: return "Wake Up"
            case .wakeUpSuccess: return "Wake Up Success"
            case .wakeUpNotChannelOnline: return "Wake Up Not Channel Online"
            case .wakeUpNotChannelOffline: return "Wake Up Not Channel Offline"
            case .deviceUpdateModel: return "Device Update Model"
            case .deviceStatusOnline: return "Device Status Online"
            case .deviceStatusSleep: return "Device Status Sleep"
            case .deviceStatusOffline: return "Device Status Offline"
            }
        }
    }
    
    struct Message: HandyJSON {
        var type: MessageType?
        var typeName = ""
        var content: Any?
        var requestId = ""
    }
    
    static let shared = IotMessager()
    
    var mqttConfig: MQTTConfig?
    
    var mqttService: MQTTService?
    
    var configPairs: [(String, UInt16)]?
    
    var isTesting = false
    /// 一个ip连接成功后断开，有 3 次重连机会，3 次都失败，切换 ip
    var reconnectRetryCount = 3
    
    var isMQTTConnected: Bool {
        guard let server = mqttService else { return false }
        return server.mqtt.connState == .connected
    }
    
    func setup() {
        guard let config = mqttConfig else {
            NIotLogger.error("[IotMessager] Missing MQTT config.")
            return
        }
        configPairs = config.urls?
            .split(separator: ",")
            .compactMap { return Utils.matchMQTTHostAndPort(from: String($0)) }
        runNetworkReachableTestingJob()
    }
    
    func runNetworkReachableTestingJob() {
        guard let config = mqttConfig,
              let username = config.username,
              let pwd = config.password,
              let pair = configPairs?.removeFirst()
        else {
            NIotLogger.error("[IotMessager] no valid MQTT config.")
            return
        }
        isTesting = true
        
        if configPairs?.isEmpty ?? true {
            // 队列清空后重新赋值，以便可以循环测试
            configPairs = config.urls?
                .split(separator: ",")
                .compactMap { return Utils.matchMQTTHostAndPort(from: String($0)) }
        }
        NIotLogger.info("[IotMessager] runNetworkReachableTestingJob, host: \(pair.0):\(pair.1)")
        // 连接 MQTT，订阅设备
        mqttService = MQTTService(username, pwd: pwd, host: pair.0, port: pair.1)
        mqttService?.delegate = self
        mqttService?.connect()
    }
    
    func disconnect() {
        mqttService?.disconnect()
        mqttService = nil
    }
    
    private let topicPrefix = "keepalive_"
    
    func startObserving() {
        guard _isConnectSuccess else {
            NIotLogger.info("[IotMessager] startObservingDevices: MQTT not connected.")
            return
        }
        // mqtt topic
        // niview app的不变, 还是keepalive_account
        // 后续wtwpro或者其他app有更新，改成keepalive_appname_account
        let appName = appIndicator == "niview" ? "" : appIndicator+"_"
        let topic = topicPrefix + appName + (NVEnvironment.shared.user?.userName ?? "")
        NIotLogger.info("[IotMessager] startObserving: \(topic)")
        mqttService?.subscribe(topic, qos: CocoaMQTTQoS.qos1)
    }
    
    // MARK: - IOT
    
    typealias WakeUpHandler = (_ tid: String, _ isSuccess: Bool) -> Void
    private var _wakeUpHandlerMapping = [String: WakeUpHandler]()
    
    ///  唤醒
    func wakeDeviceUp(_ tid: String, productId: String, wakeUpHandler: @escaping WakeUpHandler) {
        let wakeUpMsg = Message(
            type: .wakeUp,
            typeName: MessageType.wakeUp.typeName,
            content: ["deviceName": tid, "productId": productId],
            requestId: "4a5a5526-d61a-4518-9781-faba3696cc08"
        )
        guard let jsonStr = wakeUpMsg.toJSONString() else { return }
        NIotLogger.info("[IotMessager] wakeDeviceUp tid:\(tid)")
        // FIXME: 如果多次唤醒同一设备，handler会被覆盖
        _wakeUpHandlerMapping[tid] = wakeUpHandler
        sendData(jsonStr, to: tid)
    }
    
    func sendData(_ data: String, to tid: String) {
        let msg = CocoaMQTTMessage(topic: "keepalive_all", string: data)
        let retained = mqttService?.publish(msg) ?? 0
        NIotLogger.info("[IotMessager] sendData tid:\(tid), data:\(data), retained:\(retained)")
    }
    
    private func _loadDeviceStatus() {
        let tids = NVEnvironment.shared.devices
            .map { $0.device.tid }
            .joined(separator: ",")
        NIotNetworking.shared.getDeviceStatus(tids: tids) { json, error in
            
        }
    }
    
    // MARK: - MQTT
    
    private var _isConnectSuccess = false
    
    func mqttService(_ service: MQTTService, didConnectSuccess success: Bool) {
        NIotLogger.info("[IotMessager] didConnectSuccess: \(success)")
        _isConnectSuccess = success
        guard success, NVEnvironment.shared.isUserLogin else { return }
        // 连接成功，重置状态
        reconnectRetryCount = 3
        isTesting = false
        // 加载设备状态
        _loadDeviceStatus()
        // 连接成功自动订阅
        startObserving()
    }
    
    func mqttService(_ service: MQTTService, didReceiveMessage message: CocoaMQTTMessage) {
        NIotLogger.info("[IotMessager] didReceiveMessage Message received in topic \(message.topic) with payload \(message.string!)")
        guard let msg = Message.deserialize(from: message.string),
              message.topic.contains(topicPrefix),
              let jsonStr = msg.content as? String,
              let data = jsonStr.data(using: .utf8),
              let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any]
        else { return }
        
        switch msg.type {
        case .wakeUpSuccess, .wakeUpNotChannelOnline:
            if let tid = json["deviceName"] as? String,
               let wakeUpHandler = _wakeUpHandlerMapping[tid] {
                NIotLogger.info("[IotMessager] wakeDeviceUp tid:\(tid), SUCCESS")
                wakeUpHandler(tid, true)
                _wakeUpHandlerMapping.removeValue(forKey: tid)
            } else {
                
            }
        case .wakeUpNotChannelOffline:
            if let tid = json["deviceName"] as? String,
               let wakeUpHandler = _wakeUpHandlerMapping[tid] {
                NIotLogger.info("[IotMessager] wakeDeviceUp tid:\(tid), FAILED: \(msg.typeName)")
                wakeUpHandler(tid, false)
                _wakeUpHandlerMapping.removeValue(forKey: tid)
            } else {
                
            }
        case .deviceUpdateModel:
            guard let tid = json["tid"] as? String else { return }
            var property: [String: Any]?
            if let propertyStr = json["params"] as? String,
               let pData = propertyStr.data(using: .utf8),
               let propertyJSON = try? JSONSerialization.jsonObject(with: pData) as? [String: Any] {
                property = propertyJSON
            } else if let propertyJSON = json["params"] as? [String: Any] {
                property = propertyJSON
            }
            
            guard let property = property else { return }
            NIotLogger.info("[IotMessager] didReceiveMessage <\(tid)> json: \(property)")
            for path in property.keys {
                var result = ""
                if let value = property[path] as? [String: Any],
                   let valueStr = value.toString() {
                    result = valueStr
                } else if let value = property[path] as? String {
                    result = value
                } else {
                    result = "\(property[path] ?? "")"
                }
                NotificationCenter.default.post(
                    name: .NIot_DevicePropertyDidUpdate,
                    object: nil,
                    userInfo: ["json": result, "path": path, "deviceId": tid]
                )
            }
        default: break
        }
        
        // 处理设备在线状态
        var status: PMOnlineStatus?
        switch msg.type {
        case .deviceStatusSleep: status = .dormant
        case .deviceStatusOnline: status = .online
        case .deviceStatusOffline: status = .offline
        default: break
        }
        if let status = status, let tid = json["deviceName"] as? String {
            NIotLogger.info("[IotMessager] didReceiveMessage <\(tid)><onlineStatus> \(status)")
            NotificationCenter.default.post(
                name: .NIot_DevicePropertyDidUpdate,
                object: nil,
                userInfo: [
                    "json": String(status.rawValue),
                    "path": PropertyModelPath.onlineStatus.path_v2,
                    "deviceId": tid
                ]
            )
        }
    }
    func mqttService(_ service: MQTTService, didSubscribeTopics success: NSDictionary, failed: [String]) {
        if failed.isEmpty {
            NIotLogger.info("[IotMessager] didSubscribeTopics: \(success)")
        } else {
            NIotLogger.error("[IotMessager] didSubscribeTopics failed")
        }
    }
    
    func mqttService(_ service: MQTTService, didUnsubscribeTopics topics: [String]) {
        NIotLogger.info("[IotMessager] didUnsubscribeTopics: \(topics)")
    }
    
    func mqttServiceDidDisconnect(_ service: MQTTService, withError err: Error?) {
        NIotLogger.error("[IotMessager] mqttServiceDidDisconnect err: \(err?.localizedDescription ?? "")")
        var shouldRunJob = isTesting
        reconnectRetryCount -= 1
        if reconnectRetryCount == 0 {
            reconnectRetryCount = 3
            shouldRunJob = true
        }
        if shouldRunJob {
            // 断开之前的连接
            mqttService?.disconnect()
            isTesting = false
            DispatchQueue.main.asyncAfter(deadline: .now()+1) {
                self.runNetworkReachableTestingJob()
            }
        }
    }
}

@objc protocol MQTTServiceDelegate {
    func mqttService(_ service: MQTTService, didConnectSuccess success: Bool)
    func mqttService(_ service: MQTTService, didReceiveMessage message: CocoaMQTTMessage)
    func mqttService(_ service: MQTTService, didSubscribeTopics success: NSDictionary, failed: [String])
    func mqttService(_ service: MQTTService, didUnsubscribeTopics topics: [String])
    func mqttServiceDidDisconnect(_ service: MQTTService, withError err: Error?)
}

var appIndicator: String {
    var name = kAppDisplayName.lowercased()
    if name == "niviewdev" || name.contains("nisphere") {
        name = "niview"
    }
    return name
}

class MQTTService: NSObject {
    
    let mqtt: CocoaMQTT
    weak var delegate: MQTTServiceDelegate?
    
    func connect() {
        let _ = mqtt.connect()
    }
    func disconnect() {
        mqtt.disconnect()
    }
    func subscribe(_ topic: String, qos: CocoaMQTTQoS) {
        mqtt.subscribe(topic, qos: qos)
    }
    func publish(_ message: CocoaMQTTMessage) -> Int {
        return mqtt.publish(message)
    }
    
    init(_ username: String, pwd: String, host: String, port: UInt16) {
        let id = "\(appIndicator)_\(NVEnvironment.shared.userName ?? "")"
        // 登录连接，退出登录断开连接
        self.mqtt = CocoaMQTT(clientID: id, host: host, port: port)
        super.init()
        
        let clientKSCertArray = getClientCertFromP12File(certName: "client", ofType: "p12", certPassword: "sMMyBa^2lhG1kh^e")
        var sslSettings: [String: NSObject] = [:]
        sslSettings[kCFStreamSSLCertificates as String] = clientKSCertArray
        
        mqtt.username = username
        mqtt.password = pwd
        mqtt.keepAlive = 30
        mqtt.delegate = self
        mqtt.autoReconnect = true
        mqtt.enableSSL = true
        mqtt.sslSettings = sslSettings
        // 如果是自签名证书，需要设置allowUntrustCACertificate为true
//        mqtt.allowUntrustCACertificate = true
    }


    private func getClientCertFromP12File(
        certName: String,
        ofType type: String,
        certPassword: String
    ) -> CFArray? {
        // get p12 file path
        let resourcePath = Bundle.main.path(forResource: certName, ofType: type)
        guard let filePath = resourcePath, let p12Data = NSData(contentsOfFile: filePath) else {
            print("Failed to open the certificate file: \(certName).\(type)")
            return nil
        }
        // create key dictionary for reading p12 file
        let key = kSecImportExportPassphrase as String
        let options : NSDictionary = [key: certPassword]
        
        var items : CFArray?
        let securityError = SecPKCS12Import(p12Data, options, &items)
        
        guard securityError == errSecSuccess else {
            if securityError == errSecAuthFailed {
                print("ERROR: SecPKCS12Import returned errSecAuthFailed. Incorrect password?")
            } else {
                print("Failed to open the certificate file: \(certName).\(type)")
            }
            return nil
        }
        guard let theArray = items, CFArrayGetCount(theArray) > 0 else {
            return nil
        }
        let dictionary = (theArray as NSArray).object(at: 0)
        guard let identity = (dictionary as AnyObject).value(forKey: kSecImportItemIdentity as String) else {
            return nil
        }
        let certArray = [identity] as CFArray
        return certArray
    }
}


extension MQTTService: CocoaMQTTDelegate {
    func mqtt(_ mqtt: CocoaMQTT, didReceive trust: SecTrust, completionHandler: @escaping (Bool) -> Void) {
        NIotLogger.info("[IotMessager] didReceiveTrust")
        // 1. 加载 .cer 文件
//        guard let certPath = Bundle.main.path(forResource: "server", ofType: "pem"),
//              let certData = NSData(contentsOfFile: certPath),
//              let cert = SecCertificateCreateWithData(nil, certData)
//        else {
//            completionHandler(false)
//            return
//        }
//        // 2. 设置 SSL 策略，进行证书验证
//        let policy = SecPolicyCreateSSL(true, nil)
//        SecTrustSetPolicies(trust, policy)
//        // 3. 将加载的证书设置到信任对象中
//        SecTrustSetAnchorCertificates(trust, [cert] as CFArray)
//        // 4. 评估服务器信任，根据评估结果，决定是否信任证书
//        var error: CFError?
//        if #available(iOS 12.0, *) {
//            let evaluationSucceeded = SecTrustEvaluateWithError(trust, &error)
//            if evaluationSucceeded {
//                NIotLogger.info("[IotMessager] didReceiveTrust success")
//                completionHandler(true)
//            } else {
//                if let error = error {
//                    NIotLogger.info("[IotMessager] didReceiveTrust error: \(error.localizedDescription)")
//                }
//                completionHandler(false)
//            }
//        } else {
//            completionHandler(true)
//        }
    }

    
    func mqtt(_ mqtt: CocoaMQTT, didConnectAck ack: CocoaMQTTConnAck) {
        NIotLogger.info("[MQTT] didConnectAck: \(ack.description)")
        delegate?.mqttService(self, didConnectSuccess: ack == .accept)
    }
    
    func mqtt(_ mqtt: CocoaMQTT, didPublishMessage message: CocoaMQTTMessage, id: UInt16) {
        
    }
    
    func mqtt(_ mqtt: CocoaMQTT, didPublishAck id: UInt16) {
        
    }
    
    func mqtt(_ mqtt: CocoaMQTT, didReceiveMessage message: CocoaMQTTMessage, id: UInt16) {
        delegate?.mqttService(self, didReceiveMessage: message)
    }
    
    func mqtt(_ mqtt: CocoaMQTT, didSubscribeTopics success: NSDictionary, failed: [String]) {
        delegate?.mqttService(self, didSubscribeTopics: success, failed: failed)
    }
    
    func mqtt(_ mqtt: CocoaMQTT, didUnsubscribeTopics topics: [String]) {
        delegate?.mqttService(self, didUnsubscribeTopics: topics)
    }
    
    func mqttDidPing(_ mqtt: CocoaMQTT) {
        
    }
    
    func mqttDidReceivePong(_ mqtt: CocoaMQTT) {
        
    }
    
    func mqttDidDisconnect(_ mqtt: CocoaMQTT, withError err: Error?) {
        delegate?.mqttServiceDidDisconnect(self, withError: err)
    }
    
}
