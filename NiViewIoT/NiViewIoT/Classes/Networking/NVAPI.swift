//
//  NVAPI.swift
//  NiView
//
//  Created by nice on 2020/7/20.
//  Copyright © 2020 nice. All rights reserved.
//

import UIKit
import Alamofire
import HandyJSON

enum Domain: String {
    case testcn
    case release
    
    var host: String {
        switch self {
        case .testcn:
            return "https://test-nc.niceviewer.com:8463"
        case .release:
            return "https://\(NVEnvironment.shared.currentDomain):8463"
        }
    }
}

public class DeviceBindingConfig: HandyJSON {
    public var isBinding4gDevice: Bool { devtype != nil }
    public var uid: String?
    public var time: String?
    public var nid: String?
    /// 绑定 4G 才有 devtype 字段
    public var devtype: String?
    public var accessId: String?
    
    public required init() {}
    
    public init(uid: String? = nil, time: String? = nil, nid: String? = nil, devtype: String? = nil, accessId: String? = nil) {
        self.uid = uid
        self.time = time
        self.nid = nid
        self.devtype = devtype
        self.accessId = accessId
    }
}

public enum NVApi {
    
    static var baseUrl: String { Domain.release.host }
    
    /// 二级域名
    case userRegion(account: String)
    /// 邮箱验证码登录
    case loginWithEmailVerifyCode(email: String, verifyCode: String)
    /// 邮箱密码登录
    case loginWithEmailPwd(email: String, password: String)
    /// 手机验证码登录
    case loginWithPhoneVerifyCode(phone: String, verifyCode: String)
    /// 手机密码登录
    case loginWithPhonePwd(phone: String, password: String)
    /// token 免密登录
    case loginWithToken
    /// 退出登录
    case logout(uid: Int64)
    /// 发送邮箱验证码
    case emailVerifyCode(email: String)
    /// 发送手机验证码
    case phoneVerifyCode(phone: String, countryCode: String)
    /// 更新用户密码
    case updateLoginPwd(account: String, uid: Int64, password: String, verifyCode: String)
    /// 注销
    case unregister(account: String, uid: Int64, verifyCode: String)
    /// 更新用户头像
    case userImage(uid: Int64, image: UIImage)
    
    
    /// 获取设备列表
    case deviceList(uid: Int64)
    /// 绑定设备
    case bindDevice(uid: Int64, timestamp: Int64)
    case bindDevice_v2(config: DeviceBindingConfig)
    case bind4GDevice(config: DeviceBindingConfig)
//    /// 绑定设备
//    case bindDevice_v2(uid: Int64, timestamp: Int64)
//    /// 绑定设备
//    case bind4GDevice(uid: Int64, timestamp: Int64, devtype: String, nid: Int64, accessId: Int64)
    /// 解绑设备
    case unbindDevice(uid: Int64, accessId: Int64, did: Int64, tid: String, role: Int)
    /// 申请解绑设备
    case applyUnbindDevice(nid: String, account: String, verify: String)
    /// 更新设备名称
    case updateDeviceName(did: Int64, deviceName: String)
    /// 分享设备
    case shareDevice(uid: Int64, accountToShare: String, did: Int64, deviceName: String, authConfig: String?)
    /// 取消分享设备
    case unshareDevice(uid: Int64, accountToShare: String, did: Int64, deviceName: String)
    /// 修改分享设备的备注
    case editShareDeviceRemark(did: Int64, uid: Int64, shareUid: Int64, remark: String)
    
    /// 响应设备分享事件
    case responseDeviceSharing(sid: Int64, hostUid: Int64, shareUid: Int64, did: Int64, choose: Int)
    /// 获取分享消息列表
    case getShareMessage(uid: Int64)
    /// 获取最新分享消息
    case getLatestShareMessage(uid: Int64)
    /// 设备分享用户列表
    case getDeviceSharedUsersList(uid: Int64, did: Int64)
    
    case set_device_pwd_by_oldpwd(did: Int64, type: Int, pwd: String, oldpwd: String)
    case set_device_pwd_by_verify(did: Int64, type: Int, pwd: String, verify: String)
    /// 获取设备排序
    case sortForDevice(startSort: String, endSort: String)
    /// 清理设备腾讯云激活码
    case clearDeviceActiveCode(nid: Int64)
    
    /// 获取设备事件列表
    case getDeviceEventsList(uid: Int64, did: Int64, messageType: String, startDate: String, endDate: String, page: Int, limit: Int)
    /// 获取设备事件日历打点列表
    case getDeviceEventsDatesList(uid: Int64, did: Int64, messageType: Int)
    /// 删除设备事件
    case deleteDeviceEvents(jsonArray: [[String: Any]])
    
    /// 上传手机推送token
    case putPushToken(uid: Int64, pushToken: String)
    /// 设置推送勿扰模式
    case setDisturbTime(uid: Int64, delayPattern: Int)
    /// 获取当前设置的推送勿扰模式
    case getDisturbTime(uid: Int64)
    
    /// 检查app版本，获取是否有新版本
    case checkAppVersion(currentVersion: String)
    case getH5NewVersion
    case showFakeShopping(currentVersion: String)
    case uploadLogFile(uid: Int64, data: Data)
    case customerService(nid: Int64)
    
    // MARK: 云存
    
    case cloudPackages(type: String)
    case wxpay(params: [String: Any])
    case alipay(params: [String: Any])
    case paypal(params: [String: Any])
    case free(params: [String: Any])
    case paypalRes(params: [String: Any])
    case checkPayRes(params: [String: Any])
    /// 创建 PayPal 订阅
    case payPalPayCreateSubscription(params: [String: Any])
    /// 检查 PayPal 订阅状态
    case payPalCheckSubscription(uid: Int64, subscriptionId: String)
    /// 获取 PayPal 订阅列表
    case payPalGetSubscriptionList(uid: Int64)
    /// 取消 PayPal 订阅
    case payPalCancelSubscription(uid: Int64, subscriptionId: String)
    /// 创建 PayPal 订单
    case payPalCreateOrder(params: [String: Any])
    /// 检查 PayPal 订单状态
    case payPalCheckOrderResult(uid: Int64, orderId: String)
    /// 创建银行卡支付订单
    case payPalCreateCardOrder(params: [String: Any])
    /// 检查银行卡支付订单状态
    case payPalCheckCardOrderResult(orderId: String)
    case currentCloudPkg(uid: Int64, dids: String, type: String)
    case allOrderList(uid: Int64, type: String)
    case deviceOrderList(uid: Int64, did: Int64, type: String)
    /// 发送售后服务邮件
    case sendAfterSalesEmail(params: [String: Any])
    
    // MARK: 2.0云存
    
    /// 获取具有云存的日期 / 获取视频防盗链播放URL
    case describeCloudStorageDate(tid: String)
    /// 拉取云存事件列表
    case describeCloudStorageEvents(tid: String, size: String, eventId: String, startTime: String, endTime: String)
    /// 获取某一天云存时间轴
    case describeCloudStorageTime
    /// 获取视频防盗链播放URL
    case generateSignedVideoURL(tid: String, videoURL: String?, expireTime: String, startTime: String, endTime: String)
    /// 删除云存事件
    case deleteCloudStorageEvent(tid: String, jsonArray: [[String: Any]])
    
    // MARK: 2.0设备
    
    /// 获取设备在线状态，回调由服务器异步回调至 MQTT
    /// App 刚启动时，MQTT 建立连接后发送请求
    /// 下拉设备列表时的请求服务器也会异步回调，所以下拉列表时不需要再调用这个接口
    /// @see MQTTService.swift
    case getDeviceStatus(tids: String)
    /// 获取设备属性数据
    case describeDeviceData(tid: String, path: String?)
    /// 修改设备属性数据
    case controlDeviceData(tid: String, data: String)
    /// 设备操作 Action（异步）
    case callDeviceActionAsync
    /// 设备操作 Action（同步）
    case callDeviceActionSync(tid: String, actionId: String, inputParams: String)

    /// 查询有无可升级的固件版本
    case appCheckFirmwareUpdate(nid: Int64)
    /// 确认升级固件
    case appPublishFirmwareUpdateMessage(nid: Int64)
    
    
    /// 添加预置位
    case device_p_pos(tid: String, pos: String, image: UIImage)
    /// 删除预置位
    case device_d_pos(tid: String, pos: String)
    /// 修改分享用户权限配置
    case updateAuthConfig(uid: Int64, did: Int64, authConfig: String)
    
    
    /* 4G流量 */
    /// 获取可购买的4G流量套餐
    case get_package_manage(iccid: String)
    /// 单个设备的流量购买记录
    case get_device_renew_list(iccid: String)
    /// 当前套餐的用量数据
    case get_device_current_package_usage(iccid: String)
    /// 查询iccid指定月份每日使用量
    case getFlowUsageInMonth(iccid: String)
    /// 获取 SIM 卡的 MCC 地址
    case getMccRegion(iccid: String)
    /// 获取SIM套餐过期时间
    case getPackageQueueLastTime(iccid: String)
}

extension NVApi {
    
    var parameters: [String: Any]? {
        return _encodedParameters
    }
    
    private var _parameters: [String: Any]? {
        switch self {
        case .userRegion(let account):
            return ["account": account]
        case .loginWithEmailVerifyCode(let email, let verifyCode):
            return ["email": email, "verify": verifyCode]
        case .loginWithEmailPwd(let email, let password):
            return ["email": email, "pass": password]
        case .loginWithPhoneVerifyCode(let phone, let verifyCode):
            return ["phone": phone, "verify": verifyCode]
        case .loginWithPhonePwd(let phone, let password):
            return ["phone": phone, "pass": password]
        case .logout(let uid):
            return ["id": uid, "token": ""]
        case .emailVerifyCode(let email):
            return ["email": email]
        case .phoneVerifyCode(let phone, let countryCode):
            return ["phone": phone, "country": countryCode]
            
        case .bindDevice(let uid, let timestamp):
            return ["uid": uid, "time": timestamp]
            
        case let .bindDevice_v2(config), let .bind4GDevice(config):
            var dict = [String: String]()
            if let uid = config.uid { dict["uid"] = uid }
            if let time = config.time { dict["time"] = time }
            if let nid = config.nid { dict["nid"] = nid }
            if let devtype = config.devtype { dict["devtype"] = devtype }
            if let accessId = config.accessId { dict["accessId"] = accessId }
            return dict
            
        case let .shareDevice(uid, accountToShare, did, deviceName, authConfig):
            return ["uid": uid, "account": accountToShare, "did": did, "devName": deviceName, "authConfig": authConfig as Any]
            
        case .unshareDevice(let uid, let accountToShare, let did, let deviceName):
            return ["uid": uid, "account": accountToShare, "did": did, "devName": deviceName]
            
        case .deviceList(let uid),
             .getLatestShareMessage(let uid):
            return ["uid": uid]
            
        case .getShareMessage(let uid):
            return ["uid": uid, "limit": "1500", "page": "1"]
            
        case .clearDeviceActiveCode(let nid):
            return ["nid": nid]
            
        case .getDeviceSharedUsersList(let uid, let did):
            return ["uid": uid, "did": did]
            
        case let .getDeviceEventsList(uid, did, messageType, startDate, endDate, page, limit):
            return ["uid": uid, "did": did, "messType": messageType, "startDate": startDate, "endDate": endDate, "page": page, "limit": limit]
        case .getDeviceEventsDatesList(let uid, let did, let messageType):
            return ["uid": uid, "did": did, "messType": messageType]
        case .deleteDeviceEvents(let jsonArray):
            return ["deleteStr": jsonArray.toJson() ?? ""]
            
        case .setDisturbTime(let uid, let delayPattern):
            return ["uid": uid, "delayPattern": delayPattern]
        case .getDisturbTime(let uid):
            return ["uid": uid]
            
            
        case .checkAppVersion(let currentVersion), 
             .showFakeShopping(let currentVersion):
            return ["cur_version": currentVersion, "mob_platform": "ios"]
            
        case let .cloudPackages(type):
            return ["type": type]
            
        case let .wxpay(params),
             let .alipay(params),
             let .paypal(params),
             let .free(params),
             let .paypalRes(params),
             let .checkPayRes(params),
             .payPalPayCreateSubscription(let params),
             .payPalCreateOrder(let params),
             .payPalCreateCardOrder(let params),
             .sendAfterSalesEmail(let params):
            return params
            
        case .payPalCheckSubscription(let uid, let subscriptionId):
            return ["uid": uid, "subscriptionId": subscriptionId]
            
        case .payPalGetSubscriptionList(let uid):
            return ["uid": uid]
            
        case .payPalCheckOrderResult(let uid, let orderId):
            return ["uid": uid, "orderId": orderId]
        case .payPalCheckCardOrderResult(let orderId):
            return ["orderId": orderId]
            
        case let .currentCloudPkg(uid, dids, type):
            return ["uid": uid, "dids": dids, "type": type]

        case let .allOrderList(uid, type):
            return ["uid": uid, "type": type]

        case let .deviceOrderList(uid, did, type):
            return ["uid": uid, "did": did, "type": type]

        case let .describeCloudStorageDate(tid),
             let .getDeviceStatus(tid):
            return ["tid": tid]

        case let .describeCloudStorageEvents(tid, size, eventId, startTime, endTime):
            return ["tid": tid, "size": size, "eventId": eventId, "startTime": startTime, "endTime": endTime]

        case let .generateSignedVideoURL(tid, videoURL, expireTime, startTime, endTime):
            var params = ["tid": tid, "expireTime": expireTime, "startTime": startTime, "endTime": endTime]
            if let videoURL = videoURL { params["videoURL"] = videoURL }
            return params
            
        case let .deleteCloudStorageEvent(tid, jsonArray):
            return ["tid": tid, "data": jsonArray.toJson() ?? ""]

        case let .describeDeviceData(tid, path):
            var params = ["tid": tid]
            if let path = path { params["path"] = path }
            return params

        case let .controlDeviceData(tid, data):
            return ["tid": tid, "data": data]

        case let .callDeviceActionSync(tid, actionId, inputParams):
            return ["tid": tid, "actionId": actionId, "inputParams": inputParams]

        case let .appCheckFirmwareUpdate(nid),
             let .appPublishFirmwareUpdateMessage(nid),
             let .customerService(nid):
            return ["nid": nid]

        case let .device_p_pos(tid, pos, _),
             let .device_d_pos(tid, pos):
            return ["tid": tid, "pos": pos]

        case let .get_package_manage(iccid),
             let .get_device_renew_list(iccid),
             let .get_device_current_package_usage(iccid),
             let .getFlowUsageInMonth(iccid),
             let .getMccRegion(iccid),
             let .getPackageQueueLastTime(iccid):
            return ["iccid": iccid]

        case let .applyUnbindDevice(nid, account, verify):
            return ["nid": nid, "account": account, "verify": verify]
            
        case .loginWithToken: // token登录的token 放在 header 里
            return nil
        case .userImage, .uploadLogFile: // 文件上传
            return nil
        case .getH5NewVersion, .describeCloudStorageTime, .callDeviceActionAsync: // 无参数
            return nil
        case .unregister, .unbindDevice, .updateLoginPwd, .updateDeviceName, .responseDeviceSharing, .putPushToken, .set_device_pwd_by_oldpwd, .set_device_pwd_by_verify, .sortForDevice, .updateAuthConfig, .editShareDeviceRemark, .payPalCancelSubscription: // 参数拼接在 url 后面
            return nil
        }
    }
    
    private var _encodedParameters: [String: Any]? {
        guard let parameters = _parameters else {
            return nil
        }
        NVNetworkLog("🟡Parameters(Plain Text):\(parameters)")
        
        var encParams = parameters
        if method == "POST" {
            encParams = encodeParametersForPOST(parameters)
        } else if method == "GET" {
            encParams = encodeParametersForGET(parameters)
        }
        return encParams
    }
    
    private func encodeParametersForGET(_ parameters:[String: Any]) -> [String: Any] {
        // GET 请求，只加密 params.value
        var encParams = [String: Any]()
        for key in parameters.keys {
            let value = parameters[key]
            if value is [String: Any] || value is [Any] || value is Set<AnyHashable> {
                encParams[key] = value
            } else {
                if key == "role" {
                    encParams[key] = value
                } else {
                    if let valueStr = value as? String {
                        encParams[key] = valueStr.aesEncode()
                    } else if let valueData = value as? Data, let valueStr = String.init(data: valueData, encoding: .utf8) {
                        encParams[key] = valueStr.aesEncode()
                    } else if let value = value {
                        encParams[key] = "\(value)".aesEncode()
                    }
                }
            }
        }
        return encParams
    }
    
    private func encodeParametersForPOST(_ parameters:[String: Any]) -> [String: Any] {
        // 每个 value 都要转成 String 类型
        var tempParams = [String: String]()
        for key in parameters.keys {
            let value = parameters[key]
            if value is [String: Any] || value is [Any] || value is Set<AnyHashable> {
                // TODO: POST 请求参数中如果有非基本数据类型该如何处理？
                print("🔴 POST 请求参数中有非基本数据类型，目前未处理")
//                tempParams[key] = value
            } else {
                if let valueStr = value as? String {
                    tempParams[key] = valueStr
                } else if let valueData = value as? Data, let valueStr = String.init(data: valueData, encoding: .utf8) {
                    tempParams[key] = valueStr
                } else if let value = value {
                    tempParams[key] = "\(value)"
                }
            }
        }
        // POST 请求，加密整个 params
        return ["data": tempParams.toString()?.aesEncode() ?? ""]
    }
}

extension NVApi {
    
    /// 请求完整地址
    var url: String {
        return "\(NVApi.baseUrl)\(version)\(serviceName)\(path)"
    }
    
    var version: String {
        switch self {
        case .bindDevice_v2, .describeCloudStorageDate, .describeCloudStorageEvents, .describeCloudStorageTime, .generateSignedVideoURL, .deleteCloudStorageEvent, .getDeviceStatus, .describeDeviceData, .controlDeviceData, .callDeviceActionAsync, .callDeviceActionSync, .appCheckFirmwareUpdate, .appPublishFirmwareUpdateMessage:
            return "/v2"
        default:
            return "/v1"
        }
    }
    
    var serviceName: String {
        switch self {
        case .userRegion, .loginWithEmailVerifyCode, .loginWithEmailPwd, .loginWithPhoneVerifyCode, .loginWithPhonePwd, .loginWithToken, .logout, .updateLoginPwd, .userImage, .unregister, .putPushToken, .setDisturbTime, .getDisturbTime, .uploadLogFile:
            return "/user"
        case .emailVerifyCode, .phoneVerifyCode, .cloudPackages:
            return "/cloudService"
        case .currentCloudPkg, .allOrderList, .deviceOrderList, .describeCloudStorageDate, .describeCloudStorageEvents, .describeCloudStorageTime, .generateSignedVideoURL, .deleteCloudStorageEvent, .sendAfterSalesEmail:
            return "/cloudOrder"
        case .get_package_manage, .get_device_renew_list, .get_device_current_package_usage, .getFlowUsageInMonth, .getMccRegion, .getPackageQueueLastTime:
            return "/sim"
        case .wxpay, .alipay, .paypal, .free, .paypalRes, .checkPayRes, .payPalPayCreateSubscription, .payPalCheckSubscription, .payPalGetSubscriptionList, .payPalCancelSubscription, .payPalCreateOrder, .payPalCheckOrderResult, .payPalCreateCardOrder, .payPalCheckCardOrderResult:
            return "/pay"
        case .bindDevice, .bindDevice_v2, .bind4GDevice, .unbindDevice, .applyUnbindDevice, .updateDeviceName, .shareDevice, .unshareDevice, .editShareDeviceRemark, .deviceList, .getDeviceSharedUsersList, .set_device_pwd_by_oldpwd, .set_device_pwd_by_verify, .sortForDevice, .clearDeviceActiveCode, .customerService, .getDeviceStatus, .describeDeviceData, .controlDeviceData, .callDeviceActionAsync, .callDeviceActionSync, .appCheckFirmwareUpdate, .appPublishFirmwareUpdateMessage, .device_p_pos, .device_d_pos, .updateAuthConfig:
            return "/device"
        case .responseDeviceSharing, .getShareMessage, .getLatestShareMessage:
            return "/shareMess"
        case .getDeviceEventsList, .getDeviceEventsDatesList, .deleteDeviceEvents:
            return "/devMess"
        case .checkAppVersion, .showFakeShopping:
            return "/appVersion"
        case .getH5NewVersion:
            return "/h5Version"
        }
    }
    
    var path: String {
        switch self {
        case .userRegion: return "/userRegion"
        case .loginWithEmailVerifyCode, .loginWithEmailPwd: return "/user_e"
        case .loginWithPhoneVerifyCode, .loginWithPhonePwd: return "/user"
        case .loginWithToken: return "/user_t"
        case .logout: return "/user_out"
        case .emailVerifyCode: return "/sms_e"
        case .phoneVerifyCode: return "/sms_p"
        case .userImage: return "/user_img"
            
        case .bindDevice: return "/bind_d"
        case .bindDevice_v2: return "/bind_d_2"
        case .bind4GDevice: return "/bind_d_4g"
        case .shareDevice: return "/device_s"
        case .unshareDevice: return "/device_us"
        case .deviceList: return "/devices"
        case .clearDeviceActiveCode: return "/clearDeviceActiveCode"
            
        case .getShareMessage: return "/shareMess"
        case .getLatestShareMessage: return "/shareMess_li"
        case .getDeviceSharedUsersList: return "/shares"
            
        case .getDeviceEventsList, .deleteDeviceEvents: return "/devMess"
        case .getDeviceEventsDatesList: return "/devMessDates"
             
            
        case .setDisturbTime: return "/setDisturbTime"
        case .getDisturbTime: return "/getDisturbTime"
            
        case .checkAppVersion: return "/appversion"
        case .getH5NewVersion: return "/getNewVersion"
        case .showFakeShopping: return "/check_m"
        case .uploadLogFile: return "/app_lp"
        case .customerService: return "/seller"
            
        case .cloudPackages: return "/cloudServices"
        case .wxpay: return "/wxPayOrder"
        case .alipay: return "/aliPayOrder"
        case .paypal: return "/getPayPalToken"
        case .free: return "/free_cloud"
        case .paypalRes: return "/payPalPay"
        case .checkPayRes: return "/pay_result"
        case .payPalPayCreateSubscription: return "/payPalPayCreateSubscription"
        case .payPalCheckSubscription: return "/checkPayPalPaySubscription"
        case .payPalGetSubscriptionList: return "/getPayPalPaySubscriptionList"
        case .payPalCreateOrder: return "/payPalPayCreateOrder"
        case .payPalCheckOrderResult: return "/payPalPayGetOrderStatus"
        case .payPalCreateCardOrder: return "/payPalBankCardPayCreateOrder"
        case .payPalCheckCardOrderResult: return "/payPalBankCardPayGetOrderStatus"
        case .currentCloudPkg: return "/cloudOrder_l_t"
        case .allOrderList: return "/cloudOrders_t"
        case .deviceOrderList: return "/cloudOrders_d_t"
        case .sendAfterSalesEmail: return "/send_after_sales_email"
            
        case .describeCloudStorageDate: return "/describeCloudStorageDate"
        case .describeCloudStorageEvents: return "/describeCloudStorageEvents"
        case .describeCloudStorageTime: return "/describeCloudStorageTime"
        case .generateSignedVideoURL: return "/generateSignedVideoURL"
        case .deleteCloudStorageEvent: return "/deleteCloudStorageEvent"
            
        case .getDeviceStatus: return "/getDeviceStatus"
        case .describeDeviceData: return "/describeDeviceData"
        case .controlDeviceData: return "/controlDeviceData"
        case .callDeviceActionAsync: return "/callDeviceActionAsync"
        case .callDeviceActionSync: return "/callDeviceActionSync"
        case .appCheckFirmwareUpdate: return "/appCheckFirmwareUpdate"
        case .appPublishFirmwareUpdateMessage: return "/appPublishFirmwareUpdateMessage"
            
        case .device_p_pos: return "/device_p_pos"
        case .device_d_pos: return "/device_d_pos"
            
        case .get_package_manage: return "/get_package_manage"
        case .get_device_renew_list: return "/get_device_renew_list"
        case .get_device_current_package_usage: return "/get_device_current_package_usage"
        case .getFlowUsageInMonth: return "/usagelog"
        case .getMccRegion: return "/getMccRegion"
        case .getPackageQueueLastTime: return "/getPackageQueueLastTime"
            
        case .applyUnbindDevice: return "/applyUnbind"
            
        case .updateLoginPwd(let account, let uid, let password, let verifyCode):
            let params: [String : Any] = [
                "account": account,
                "id": uid,
                "verify": verifyCode,
                "pass": password,
            ]
            return "/user?" + formatQueryString(from: params)
            
        case .unregister(let account, let uid, let verifyCode):
            let params: [String : Any] = [
                "account": account,
                "uid": uid,
                "verify": verifyCode,
            ]
            return "/unregister?" + formatQueryString(from: params)
            
        case .unbindDevice(let uid, let accessId, let did, let tid, let role):
            let params: [String : Any] = [
                "did": did,
                "role": role,
                "uid": uid,
                "accessId": accessId,
                "tid": tid,
            ]
            return "/device?" + formatQueryString(from: params)
            
        case .updateDeviceName(let did, let deviceName):
            let params: [String : Any] = [
                "did": did,
                "devName": deviceName,
            ]
            return "/device?" + formatQueryString(from: params, needPercentEncoding: false)
            
        case .responseDeviceSharing(let sid, let hostUid, let shareUid, let did, let choose):
            let params: [String : Any] = [
                "did": did,
                "host_uid": hostUid,
                "sid": sid,
                "share_uid": shareUid,
                "choose": choose,
            ]
            return "/shareMess?" + formatQueryString(from: params)
            
        case .putPushToken(let uid, let pushToken):
            let params: [String : Any] = [
                "id": uid,
                "token": "{\"ios\":\"\(pushToken)\"}",
            ]
            return "/pushToken?" + formatQueryString(from: params, needPercentEncoding: false)
            
        case let .set_device_pwd_by_oldpwd(did, type, pwd, oldpwd):
            let params: [String : Any] = [
                "did": did,
                "type": type,
                "pwd": pwd,
                "oldpwd": oldpwd,
            ]
            return "/set_device_pwd_by_oldpwd?" + formatQueryString(from: params)
            
        case let .set_device_pwd_by_verify(did, type, pwd, verify):
            let params: [String : Any] = [
                "did": did,
                "type": type,
                "pwd": pwd,
                "verify": verify,
            ]
            return "/set_device_pwd_by_verify?" + formatQueryString(from: params)
        case let .sortForDevice(startSort, endSort):
            let params: [String : Any] = [
                "startSort": startSort,
                "endSort": endSort,
            ]
            return "/sortForDevice?" + formatQueryString(from: params)
        case let .updateAuthConfig(uid, did, authConfig):
            let params: [String : Any] = [
                "uid": uid,
                "did": did,
                "authConfig": authConfig,
            ]
            return "/updateAuthConfig?" + formatQueryString(from: params, needPercentEncoding: false)
        case .editShareDeviceRemark(let did, let uid, let shareUid, let remark):
            let params: [String : Any] = [
                "did": did,
                "uid": uid,
                "shareUid": shareUid,
                "remark": remark,
            ]
            return "/editShareDeviceRemark?" + formatQueryString(from: params)
        case let .payPalCancelSubscription(uid, subscriptionId):
            let params: [String : Any] = [
                "uid": uid,
                "subscriptionId": subscriptionId,
            ]
            return "/cancelPaypalSubscription?" + formatQueryString(from: params, needPercentEncoding: false)
            
            
        }
    }
    
    var method: String {
        switch self {
        case .userRegion, .loginWithEmailPwd, .loginWithPhonePwd, .loginWithToken, .deviceList, .logout, .getShareMessage, .getLatestShareMessage, .getDeviceSharedUsersList, .getDeviceEventsList, .getDeviceEventsDatesList, .setDisturbTime, .getDisturbTime, .checkAppVersion, .getH5NewVersion, .showFakeShopping, .customerService, .cloudPackages, .checkPayRes, .currentCloudPkg, .allOrderList, .deviceOrderList, .describeCloudStorageDate, .describeCloudStorageEvents, .describeCloudStorageTime, .generateSignedVideoURL, .getDeviceStatus, .describeDeviceData, .appCheckFirmwareUpdate, .get_package_manage, .get_device_renew_list, .get_device_current_package_usage, .getFlowUsageInMonth, .getMccRegion, .getPackageQueueLastTime, .payPalCheckSubscription, .payPalGetSubscriptionList, .payPalCheckOrderResult, .payPalCheckCardOrderResult:
            return "GET"
        case .emailVerifyCode, .phoneVerifyCode, .loginWithEmailVerifyCode, .loginWithPhoneVerifyCode, .bindDevice, .bindDevice_v2, .bind4GDevice, .shareDevice, .unshareDevice, .deleteDeviceEvents, .deleteCloudStorageEvent, .clearDeviceActiveCode, .wxpay, .alipay, .paypal, .free, .paypalRes, .controlDeviceData, .callDeviceActionAsync, .callDeviceActionSync, .appPublishFirmwareUpdateMessage, .device_d_pos, .applyUnbindDevice, .payPalPayCreateSubscription, .payPalCreateOrder, .payPalCreateCardOrder, .payPalCancelSubscription, .sendAfterSalesEmail:
            return "POST"
        case .updateLoginPwd, .updateDeviceName, .responseDeviceSharing, .putPushToken, .set_device_pwd_by_oldpwd, .set_device_pwd_by_verify, .sortForDevice, .updateAuthConfig, .editShareDeviceRemark:
            return "PUT"
        case .unregister, .unbindDevice:
            return "DELETE"
        case .userImage, .uploadLogFile, .device_p_pos: // 上传头像走上传接口，不需要 method 参数
            return "POST"
        }
    }
    /// 编码方式
    var encoding: ParameterEncoding {
        switch method {
        case "POST", "PUT", "DELETE":
            return JSONEncoding.default
        default:
            return URLEncoding(arrayEncoding: .noBrackets)
        }
    }
    /// 请求头
    var headers: [String: String] {
        guard var appName = NVEnvironment.shared.appName?.lowercased(),
              let appLanguage = NVEnvironment.shared.appLanguage,
              let appVersion = NVEnvironment.shared.appVersion,
              let secretId = NVEnvironment.shared.secretId,
              let secretKey = NVEnvironment.shared.secretKey else {
            print("NIot SDK environment missing required values: appName/appLanguage/secretId/secretKey, please setup these values with 'NIotNetworking.setup(appName:appLanguage:)'")
            return [:]
        }
        let timestamp = "\(Date().milliStamp)".aesEncode()
        
        let appLanguagueStr = appLanguage == .Chinese ? "zh" : "en"
        
        var headers = ["Content-type": "application/json;charset=utf-8",
                       "timestamp": timestamp,
                       "Accept": "application/json",
                       "app-name": appName,
                       "app-lang": getCurrentLanguage(),
                       "app-version": appVersion]
        
        if appName != "niview" {
            headers["secret-id"] = secretId.aesEncode()
            headers["secret-key"] = secretKey.aesEncode()
        }
        if let token = NVEnvironment.shared.user?.token, !token.isEmpty {
            headers["token"] = token.aesEncode()
        }
        return headers
    }
    
    func formatQueryString(from parameters: [String: Any], needEncrypt: Bool = true, needPercentEncoding: Bool = true) -> String {
        var parts: [String] = []
        
        for (key, value) in parameters {
            var keyString = key
            var valueString = "\(value)"
            if needPercentEncoding {
                keyString = key.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed)!
                valueString = valueString.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed)!
            }
            if needEncrypt { valueString = valueString.aesEncode() }
            parts.append("\(keyString)=\(valueString)")
        }
        
        return parts.joined(separator: "&")
    }
}

extension NVApi {
    var shouldCache: Bool {
        switch self {
        case .deviceList:
            return true
        default:
            return false
        }
    }
}
