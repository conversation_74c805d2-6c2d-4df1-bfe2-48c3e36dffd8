//
//  NVUserData.swift
//  NIot
//
//  Created by apple on 2021/11/23.
//

import UIKit
import HandyJSON
import NiViewIoT.NIot_IoTVideo_Private

public class User: HandyJSON {
    public var email: String?
    public var id: Int64?
    public var pass: String?
    public var phone: String?
    public var pushToken: String?
    public var tAccessId: Int64?
    public var tAccessToken: String?
    public var token: String?
    
    public var appName = "niview"
    public var avatarUrl: String?
    public var delayPattern = 0
    public var delayTime = 0
    public var ipAddr: String?
    public var isNewUserState = true
    public var logPush = true
    public var mqttConfig: String?
    required public init() {}
    
    public var userName: String? { phone ?? email }
}

public class NVEnvironment {
    
    struct Constant {
        static let kPrefix = "NIotPrivate_"
        static let kUserInfo = kPrefix + "userInfo"
        static let kDomainMapping = kPrefix + "domainMappings"
        static let kCurrentDomain = kPrefix + "currentDomain"
    }
    
    public static let shared = NVEnvironment()
    
    private(set) var appName: String?
    private(set) var appLanguage: NIotNetConfigLanguage?
    private(set) var secretId: String?
    private(set) var secretKey: String?
    private(set) var appVersion: String?
    
    public var user: User?
    private var _userName: String?
    public var userName: String? {
        get {
            user?.userName ?? _userName
        }
        set {
            _userName = newValue
        }
    }
    public var userId: String {
        if let id = NVEnvironment.shared.user?.id {
            return String(id)
        }
        return ""
    }
    /// 手机推送 token
    private(set) var deviceToken: String?
    /// 用户 token 内存缓存（不是磁盘缓存，调用登录或者 token 验证接口后赋值），用于判断是否登出
    private var userToken: String?
    public var currentDomain = "cloud-nc.niceviewer.com"
    
    public var devices: [NVDeviceModel] = []
    
    private var _domainMappings = [String: String]()
    
    
    init() {
        if let userInfo = UserDefaults.standard.dictionary(forKey: Constant.kUserInfo) {
            user = User.deserialize(from: userInfo)
        }
        if let mapping = UserDefaults.standard.dictionary(forKey: Constant.kDomainMapping) as? [String: String] {
            _domainMappings = mapping
        }
        if let domain = UserDefaults.standard.string(forKey: Constant.kCurrentDomain) {
            currentDomain = domain
        }
    }
    
    open func setup(appName: String, appLanguage: NIotNetConfigLanguage, appVersion: String, secretId: String, secretKey: String) {
        self.appName = appName
        self.appLanguage = appLanguage
        self.appVersion = appVersion
        self.secretId = secretId
        self.secretKey = secretKey
    }
    
    func update(appLanguage: NIotNetConfigLanguage) {
        self.appLanguage = appLanguage
    }
    
    func update(user: User?) {
        self.user = user
        userToken = user?.token
        persistData()
    }
    
    public var isUserLogin: Bool {
        if let token = userToken {
            return !token.isEmpty
        }
        return false
    }
    
    public func update(deviceToken: Data) {
        let deviceTokenStr = deviceToken
            .map { String(format:"%02.2hhx", arguments: [$0]) }
            .joined()
        self.deviceToken = deviceTokenStr
    }
    
    public func domain(for user: String) -> String? {
        return _domainMappings[user]
    }
    
    func update(domain: String, for user: String) {
        _domainMappings[user] = domain
        persistData()
    }
    
    public func update(currentDomain: String) {
        self.currentDomain = currentDomain
        persistData()
    }
    
    public func resetCurrentDomain() {
        update(currentDomain: "cloud-nc.niceviewer.com")
    }
    
    private func persistData() {
        UserDefaults.standard.set(user?.toJSON(), forKey: Constant.kUserInfo)
        UserDefaults.standard.set(currentDomain, forKey: Constant.kCurrentDomain)
        UserDefaults.standard.set(_domainMappings, forKey: Constant.kDomainMapping)
        UserDefaults.standard.synchronize()
    }
}


// MARK: 环境部署
extension NVEnvironment {
    
    func setupIoTVideo() {
        guard let id = user?.tAccessId, let token = user?.tAccessToken else { return }
        IoTVideo.sharedInstance.register(withAccessId: String(id), accessToken: token)
    }
    
    func registerRemoteNotifications() {
        guard let uid = user?.id, let token = deviceToken, !token.isEmpty else { return }
        NIotLogger.info("[DEBUG] update push token")
        NIotNetworking.shared.putPushToken(uid: uid, pushToken: token) { json, error in
            if error == nil {
                NIotLogger.info("[DEBUG] update push token success")
            } else {
                NIotLogger.error("[DEBUG] update push token failed")
            }
        }
    }
    
}

extension NVEnvironment {
    
    public var isChineseDomain: Bool { currentDomain == "cloud-cn1.niceviewer.com" }
    
    public var imageBaseURL: String {
        var imageBaseUrl = ""
        if currentDomain.contains("cloud-ap") {
            imageBaseUrl = "https://niview-prod-as-pic-1302374016.cos.ap-singapore.myqcloud.com"
        } else if currentDomain.contains("cloud-cn") {
            imageBaseUrl = "https://niview-prod-cn-pic-1302374016.cos.ap-guangzhou.myqcloud.com"
        } else if currentDomain.contains("cloud-eur") {
            imageBaseUrl = "https://niview-prod-eu-pic-1302374016.cos.eu-frankfurt.myqcloud.com"
        } else if currentDomain.contains("cloud-us") {
            imageBaseUrl = "https://niview-prod-na-pic-1302374016.cos.na-siliconvalley.myqcloud.com"
        }
        if currentDomain.contains("test-cn") {
            imageBaseUrl = "https://niview-pic-1302374016.cos.ap-guangzhou.myqcloud.com"
        }
        return imageBaseUrl
    }
}
