//
//  NVJson.swift
//  NiView
//
//  Created by nice on 2020/8/26.
//  Copyright © 2020 nice. All rights reserved.
//

import UIKit
import HandyJSON
/// 解析JSon
class NVJson: NSObject {
    /// 传入json进行解析
    /// - Parameter json: json字符串
    /// - Parameter type: 遵守 Codable 协议的Model.self
    /// - Returns: 解析完成的model
    class func decode<T: Codable>(json: String, type:T.Type) -> T? {
        return json.decode(type)
    }
}

extension String {
    /// json 解析
    /// - Parameter type: 遵守 Codable 协议的Model.self
    /// - Returns: 解析完成的model
    func decode<T: Codable>(_ type: T.Type) -> T? {
        let jsonData = data(using: .utf8)!
        do {
            let model: T? = try JSONDecoder().decode(type, from: jsonData)
            return model
        } catch {
            return nil
        }
    }

    /// json 解析 字典 解出data对应model
    /// - Parameter type: 遵守 Codable 协议的Model.self
    func ivDecode<T: Codable>(_ type: T.Type) -> T? {
        return decode(IVModel<T>.self)?.data
    }
    
    /// json 解析 数组 解出data对应model
    /// - Parameter type: 遵守 Codable 协议的Model.self
    func ivArrayDecode<T: Codable>(_ type: T.Type) -> [T?] {
        return decode(IVArrayModel<T>.self)?.data ?? []
    }
}


struct IVModel<T>: Codable where T: Codable {
    var msg: String?
    var code: Int = 0
    var data: T?
}

struct IVArrayModel<T: Codable>: Codable, HandyJSON {
    var data: [T?]?
    var code: Int = 0
    var msg: String?
}
