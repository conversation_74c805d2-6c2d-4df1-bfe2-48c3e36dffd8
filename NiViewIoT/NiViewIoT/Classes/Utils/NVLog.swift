//
//  NVLog.swift
//  NiView
//
//  Created by nice on 2020/8/6.
//  Copyright © 2020 nice. All rights reserved.
//

import UIKit
import IVDevTools

func NVLog<T>(file: String = #file,
              method: String = #function,
              line: Int = #line,
              _ msg: T...) {
    // TODO: 后期弃用
//    let format: DateFormatter = DateFormatter.init()
//    format.dateFormat = "HH:mm:ss.SSS"
//    let dateStr = format.string(from: Date())
//    let logStr = "[\(dateStr)]:\((file as NSString).lastPathComponent)[\(line)], \(method): \(message)"
//    //log写文件
//    print(logStr)
//    let dest = LogDestination()
//    dest.write(logStr)
    
    // DEBUG VERSION 1.4.11 启用
    NIotLogger.info(message(msg))
}

func NVNetworkLog<T>(_ message: T) {
    guard kISDebug else { return }
    let format = DateFormatter()
    format.dateFormat = "HH:mm:ss.SSS"
    let dateStr = format.string(from: Date())
    let logStr = "[\(dateStr)]:\(message)"
    //log写文件
    print(logStr)
    let dest = LogDestination()
    dest.write(logStr)
}

final class LogDestination: TextOutputStream {
    private let path: String
    init() {
        let paths = NSSearchPathForDirectoriesInDomains(.documentDirectory, .userDomainMask, true)
        path = paths.first! + "/log"
    }

    func write(_ string: String) {
        if let data = string.data(using: .utf8),
            let fileHandle = FileHandle(forWritingAtPath: path) {
            defer {
                fileHandle.closeFile()
            }
            fileHandle.seekToEndOfFile()
            fileHandle.write(data)
        }
    }
}

var devToolsAssistant: IVDevToolsAssistant {
    return IVDevToolsAssistant.shared
}

fileprivate func unwrap<T: Any>(_ any: T) -> T {
    let mi = Mirror(reflecting: any)
    if mi.displayStyle != .optional {
        return any
    }

    if mi.children.count == 0 { return NSNull() as! T }
    let (_, some) = mi.children.first!
    return some as! T
}

fileprivate func message<T: Collection>(_ items: T) -> String {
    return items.map { (item) -> String in
        let any = unwrap(item)
        return any as? String ?? "\(any)"
    }.joined(separator: " ")
}



/// NiView 日志输出
open class NIotLogger {

    open class var tag: String { "NiViewIoT" }
    
    public static func register(logLevel: Level = .debug) {
        IVLogger.register { (logger) in }
        IVLogger.logLevel = logLevel
        if let level = IVLogLevel(rawValue: UInt(logLevel.rawValue)) {
            IoTVideo.sharedInstance.logLevel = level
        } else {
            IoTVideo.sharedInstance.logLevel = .DEBUG
        }
    }
    
    public static func fatal(path: String = #file, function: String = #function, line: Int = #line, _ items: Any...) {
        IVLogger.log(tag, level: .fatal, path: path, function: function, line: line,  message: message(items))
    }
    
    public static func error(path: String = #file, function: String = #function, line: Int = #line, _ items: Any...) {
        IVLogger.log(tag, level: .error, path: path, function: function, line: line,  message: message(items))
    }
    
    public static func warning(path: String = #file, function: String = #function, line: Int = #line, _ items: Any...) {
        IVLogger.log(tag, level: .warning, path: path, function: function, line: line,  message: message(items))
    }
    
    public static func info(path: String = #file, function: String = #function, line: Int = #line, _ items: Any...) {
        IVLogger.log(tag, level: .info, path: path, function: function, line: line,  message: message(items))
    }
    
    public static func debug(path: String = #file, function: String = #function, line: Int = #line, _ items: Any...) {
        IVLogger.log(tag, level: .debug, path: path, function: function, line: line,  message: message(items))
    }
    
    public static func verbose(path: String = #file, function: String = #function, line: Int = #line, _ items: Any...) {
        IVLogger.log(tag, level: .verbose, path: path, function: function, line: line,  message: message(items))
    }
    
    
    public static func uploadLatestLogIfNecessary() {
        let allLogURLs = getAllFileURLs(pathExtension: ".log")
        guard allLogURLs.count > 1 else { return }
        let latestLogURL = allLogURLs[1]
        uploadlog(fileURL: latestLogURL, isShowHud: false) { progress in
            
        } completionHandler: { json, error in
            
        }
        
    }
    
    /// 所有历史log文件URL
    static func getAllFileURLs(pathExtension: String) -> [URL] {
        let subpaths = FileManager.default.subpaths(atPath: logDir().path)?.filter({ (subpath) -> Bool in
            subpath.hasSuffix(pathExtension)
        })
        let fullpaths = subpaths?.map{ logDir().appendingPathComponent($0) }
        let sortedFullpaths = fullpaths?.sorted { $0.absoluteString > $1.absoluteString } ?? []
        return Array(sortedFullpaths.prefix(30))
    }
    
    /// log文件目录
    static func logDir() -> URL {
        guard let cachesDir = FileManager.default.urls(for: .cachesDirectory, in: .userDomainMask).first else {
            fatalError("IVFileLogger couldn't find cachesDirectory")
        }

        // 创建日志目录
        let directoryURL = cachesDir.appendingPathComponent("com.jz.log")
        if !FileManager.default.fileExists(atPath: directoryURL.path) {
            do {
                try FileManager.default.createDirectory( at: directoryURL, withIntermediateDirectories: true)
            } catch {
                fatalError("IVFileLogger couldn't create logDirectory: \(directoryURL)")
            }
        }
        return directoryURL
    }

    
    public static func uploadlog(
        fileURL: URL,
        isShowHud:Bool,
        progressClosure: @escaping (_ progress:Double) -> Void,
        completionHandler: @escaping NIotNetworking.CompletionHandler
    ) {
        guard let data = FileManager.default.contents(atPath: fileURL.path) else {
            return
        }
        guard let uid = NVEnvironment.shared.user?.id else { return }
        NIotNetworking.shared.uploadLogFile(uid: uid, data: data, filename: fileURL.lastPathComponent, progressClosure: progressClosure, completionHandler: completionHandler)
    }
}
