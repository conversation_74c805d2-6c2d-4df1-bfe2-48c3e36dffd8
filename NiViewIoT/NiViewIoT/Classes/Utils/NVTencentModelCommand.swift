//
//  NVTencentModelCommand.swift
//  NiView
//
//  Created by nice on 2020/8/24.
//  Copyright © 2020 nice. All rights reserved.
//

import UIKit

/// MARK: 腾讯云物模型操作
public let kProWritable = "ProWritable."
public let kProReadonly = "ProReadonly."
public let kProConst = "ProConst."
public let kAction = "Action."

/// MARK: 各种命令
public let kProOnline           = "_online"
public let kProWifiInfo         = "WirelessModInfo"     //wifi
public let kProTimezone         = "timezone"            //时区
public let kProVersionInfo      = "_versionInfo"        //固件信息
public let kProResolution       = "resolution"          //分辨率
public let kProLedIndicator     = "ledIndicator"        //指示灯开关
public let kProVideoFlip        = "videoFlip"           //画面翻转
public let kProNightFillMode    = "nightFillMode"       //全彩/夜视
public let kProAntiFlicker      = "antiFlicker"         //抗闪烁频率
public let kProPirPostCfg       = "pirPostCfg"          //pir推送开关
public let kProKeyPostCfg       = "keyPostCfg"          //按键推送开关
public let kProLowpPwerPostCfg  = "lowpowerPostCfg"     //低电推送开关
public let kProPirSensit        = "pirSensit"           //pir灵敏度
public let kProBatteryInfo      = "batteryInfo"         //电池信息
public let kProOTAMode          = "_otaMode"            //OTA升级模式
public let kProOTAVersion       = "_otaVersion"         //获取OTA最新版本
public let kProOTAUpgrade       = "_otaUpgrade"         //开始OTA升级
public let kProReboot           = "dev_reboot"          //重启设备
public let kProStorage          = "storage"             //SD卡信息
public let kProAllTimeRecord    = "allDayRecord"        //全时录像
public let kProAlmEvtSetting    = "_almEvtSetting"      //云存缩略图
public let kProFuncList         = "funcList"            //设置项
public let kProModVersion       = "modVersion"          //mod版本
public let kProNid              = "NID"                 //nid
public let kProMCUVersion       = "mcuVersion"          //mcu版本
public let kProVolume           = "talkVolume"          //对讲音量
public let kProPresetPosInfo    = "presetPosInfo"       //摇头机预置位信息
public let kProPersonTrack      = "smdActive"           //人形追踪
public let kProHumanIndicator  = "humanIndicator"      //人形标识
public let kProEthernet         = "EthInforMation"      //以太网（4G)
public let kProSLMode           = "slMode"              //声光模式
public let kProLPProtection     = "lpProtect"           //低电保护
public let kProPowerMode        = "powerMode"           //功耗模式
public let kCsUpload            = "csUpload"           //云存上传功能开关
public let kMccInfo             = "MccInfo"            //MccInfo

/// MARK: 物模型取值对应的key
public let kProWritableKey = "setVal"
public let kProReadOnlyKey = "stVal"

///MARK - 物模型更新通知UserInfo的key
public let kNotiPropertyNameKey = "modelPropertyName"
public let kNotiPropertyValueKey = "modelPropertyValue"





