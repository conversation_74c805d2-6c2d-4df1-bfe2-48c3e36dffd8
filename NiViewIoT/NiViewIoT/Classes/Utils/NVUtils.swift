//
//  NVUtils.swift
//  NiView
//
//  Created by nice on 2020/6/24.
//  Copyright © 2020 nice. All rights reserved.
//

import UIKit
import AVFoundation
import Photos
import NiViewIoT.NIot_IoTVideo_Private

class NVUtilsManager: NSObject {
    static let share = NVUtilsManager()
    private override init() {}
    public var showMall: Bool = false
    public var IOTLinkStatus: IVLinkStatus {
        get { IoTVideo.sharedInstance.linkStatus }
    }
    public var addNewShareDevice: Bool = false
    public var singleAllowCellular = kCellularPlay //单次播放是否允许使用流量
    public var needRequestNewestShareMsg = true //是否需要请求最新的分享消息（点击推送栏进来不需要
}

// MARK: 系统相关
/// Info
public let kAppBundleInfoVersion = Bundle.main.infoDictionary ?? Dictionary()
/// plist:  AppStore 使用VersionCode 1.0.1
public let kAppBundleVersion = (kAppBundleInfoVersion["CFBundleShortVersionString" as String] as? String ) ?? ""
/// plist: 例如 1
public let kAppBundleBuild = (kAppBundleInfoVersion["CFBundleVersion"] as? String ) ?? ""
public let kAppDisplayName = (kAppBundleInfoVersion["CFBundleDisplayName"] as? String ) ?? ""
public let kAppBundleID = (kAppBundleInfoVersion["CFBundleIdentifier"] as? String) ?? ""
public let kAPPGroupKey = "group.com.niceviewer.nview"
public let kDeviceKey = "devicePlistDict"
public let kBrainTreeKey = "com.niceviewer.nview.payments"
public var kISDebug: Bool = false
 
//设备宽高、机型
public let kScreenHeight = UIScreen.main.bounds.size.height
public let kScreenWidth1 = UIScreen.main.bounds.size.width


//var kPrimaryVCWidth: CGFloat {
//   if UIDevice.isIPad {
//       if let splitVC = kWindow.rootViewController as? UISplitViewController,
//          let primaryVC = splitVC.viewControllers.first {
//               return primaryVC.view.frame.size.width
//       }
//   }
//   return UIScreen.main.bounds.size.width
//}
//
//var kDetailVCWidth: CGFloat {
//   if UIDevice.isIPad {
//       if let splitVC = kWindow.rootViewController as? UISplitViewController, splitVC.viewControllers.count > 1 {
//           let detailVC = splitVC.viewControllers[1]
//           return detailVC.view.frame.size.width
//       }
//   }
//   return UIScreen.main.bounds.size.width
//}
//public let kStatusBarheight = UIApplication.shared.statusBarFrame.size.height
//public let kNaviBarHeight: CGFloat = 44
//public let kSafeAreaBottom: CGFloat = UIDevice.isIPhoneXSeries ? 34 : 0
//public let kNavBarHeight_StatusHeight = kStatusBarheight + 44
//public let kTabbarHeight:CGFloat = 49 + kSafeAreaBottom

//屏幕比例适配
//public let kScale_W: ((CGFloat) -> CGFloat) = { (width: CGFloat) -> CGFloat in
//    return kScreenWidth1 / 414 * width
//}
//
//public let kScale_H: ((CGFloat) -> CGFloat) = { (height: CGFloat) -> CGFloat in
//    return kScreenHeight / 736 * height
//}
//
//let kAppDelegate: AppDelegate = UIApplication.shared.delegate as! AppDelegate
//public let kWindow = kAppDelegate.window!
public var kShouldAutorotate: Bool = false
public var kIsLandscape: Bool = false

public var kFirstLaunch: Bool {
    get { UserDefaults.standard.bool(forKey: "firstLaunch") }
}
 
// MARK: ============================================================================
// MARK: Dictory Array Strig Object 闭包方式
///过滤null对象
public let kFilterNullOfObj:((Any)->Any?) = {(obj: Any) -> Any? in
    if obj is NSNull {
        return nil
    }
    return obj
}
 
///过滤null的字符串，当nil时返回一个初始化的空字符串
public let kFilterNullOfString:((Any)->String) = {(obj: Any) -> String in
    if obj is String {
        return obj as! String
    }
    return ""
}
 
/// 过滤null的数组，当nil时返回一个初始化的空数组
public let kFilterNullOfArray:((Any)->Array<Any>) = {(obj: Any) -> Array<Any> in
    if obj is Array<Any> {
        return obj as! Array<Any>
    }
    return Array()
}
 
/// 过滤null的字典，当为nil时返回一个初始化的字典
public let kFilterNullOfDictionary:((Any) -> Dictionary<AnyHashable, Any>) = {( obj: Any) -> Dictionary<AnyHashable, Any> in
    if obj is Dictionary<AnyHashable, Any> {
        return obj as! Dictionary<AnyHashable, Any>
    }
    return Dictionary()
}
 
// MARK: ============================================================================
// MARK: 设置Nib、Stryboard、UIImage
 
/// 根据imageName创建一个UIImage
public let imageNamed:((String) -> UIImage?) = { (imageName : String) -> UIImage? in
    return UIImage.init(named: imageName)
}
 
/// 根据Main.storyboard建立ViewController
public let VC_From_Main_SB:((String)-> UIViewController? ) = {(SBID : String) -> UIViewController? in
    return UIStoryboard.init(name: "Main", bundle: nil).instantiateViewController(withIdentifier:SBID)
}

///MARK: Bundle
public let kAPPBundleName: String = Bundle.main.object(forInfoDictionaryKey: "CFBundleName") as! String

///MARK: 是否允许流量播放
public var kCellularPlay: Bool { get { UserDefaults.standard.bool(forKey: "cellularPlay") } }
 
/// MARK: 指定范围内的随机数
public let randomCustom:(_ min: Int, _ max: Int) -> Int = { (min: Int, max: Int) -> Int in
    let y = arc4random() % UInt32(max) + UInt32(min)
    return Int(y)
}

///MARK: APP国际化
public func NVLang(_ key: String, _ comment: String? = nil) -> String {
    return NSLocalizedString(key, comment: "")
}

///MARK: 获取当前时区
public func getTimeZone() -> String {
    let time = TimeZone.current.secondsFromGMT()
    if labs(time) > 14*60*60 {
        return "GMT00:00"
    }
    return String(format: "GMT%@%02ld:%02ld", time<0 ? "-" : "+",labs(time/60/60),labs(time/60%60))
}

///MARK: 用户头像图片写入本地
public func writeImageToDocuments(imageUrl: String) {
    let urlStr = imageUrl.urlEncoded()
    DispatchQueue.global(qos: .background).async {
        guard let url = URL(string: urlStr), var data = try? Data(contentsOf: url) else {
            return
        }
        let isEncrypted = urlStr.hasSuffix("encrypt")
        if isEncrypted {
            data = data.aesDecode() ?? data
        }
        let home = NSHomeDirectory() as NSString
        let docPath = home.appendingPathComponent("Documents") as NSString
        let filePath = docPath.appendingPathComponent("profilePhoto.jpg")
        let fileUrl = URL(fileURLWithPath: filePath)
        try? data.write(to: fileUrl)
    }
}

///MARK: 从本地读取用户头像
public func readWithFile() -> UIImage? {
    let home = NSHomeDirectory() as NSString
    let docPath = home.appendingPathComponent("Documents") as NSString
    /// 获取文本文件路径
    let filePath = docPath.appendingPathComponent("profilePhoto.jpg")
    let image = UIImage.init(contentsOfFile: filePath)
    if image == nil {
        return nil
    }else{
        return image!
    }
}

///MARK: 本地删除用户头像
public func removeFile() {
    let manager = FileManager.default
    let path = NSSearchPathForDirectoriesInDomains(.documentDirectory, .userDomainMask, true)[0] as NSString
    let uniquePath = path.appendingPathComponent("profilePhoto.jpg")
    if manager.fileExists(atPath: uniquePath) {
      do {
          try FileManager.default.removeItem(atPath: uniquePath)
      } catch {
          NVLog("error:\(error)")
      }
    }
}

///MARK: 获取手机的UUID
private var UUID: String?
func getUUID() -> String {
    var devUUID = NVKeychain.readData(kAppBundleBuild)
    guard devUUID is String else {
        devUUID = UIDevice.current.identifierForVendor!.uuidString
        UUID = (devUUID as! String).replacingOccurrences(of: "-", with: "")
        if NVKeychain.saveData(UUID!, with: kAppBundleBuild) {
            return UUID!
        } else {
            return UUID!
        }
    }
    UUID = (devUUID as! String)
    guard UUID!.length > 0 else {
        devUUID = UIDevice.current.identifierForVendor!.uuidString
        if NVKeychain.saveData(devUUID, with: kAppBundleBuild) {
            return devUUID as! String
        } else {
            return UUID!
        }
    }
    return UUID!
}

///MARK 系统权限
public func judgeCameraJurisdiction(viewController: UIViewController) -> Bool {
    let status = AVCaptureDevice.authorizationStatus(for: .video)
    if status == .denied {
        let alertController = UIAlertController.init(title: NVLang("account_photo_jurisdiction"), message: nil, preferredStyle: .alert)
        
        alertController.addAction(UIAlertAction.init(title: NVLang("common_confirm"), style: .default, handler: { (action) in
            
            UIApplication.shared.open(URL(string: UIApplication.openSettingsURLString)!, options: [:], completionHandler: nil)
            
        }))
        
        alertController.addAction(UIAlertAction.init(title: NVLang("common_cancel"), style: .cancel, handler: nil))
        viewController.present(alertController, animated: true, completion: nil)
        return false
    }
    return true
}

//保存到相册
public func creationRequestForAsset(
    _ fileURL: URL,
    target: UIViewController?,
    showHud: Bool = true
) {
    let fileTypeStr = fileURL.lastPathComponent.split(separator: ".").last
    let isImage = ["png", "jpeg", "jpg"].contains(fileTypeStr)
    var localIdentifier: String?
    PHPhotoLibrary.shared().performChanges({
        var createAssetRequest: PHAssetChangeRequest? = nil
        if isImage {
            createAssetRequest = PHAssetChangeRequest.creationRequestForAssetFromImage(atFileURL: fileURL)
        } else {
            createAssetRequest = PHAssetChangeRequest.creationRequestForAssetFromVideo(atFileURL: fileURL)
        }
        let placeholder = createAssetRequest?.placeholderForCreatedAsset
        localIdentifier = placeholder?.localIdentifier
    }) { (success, error) in
        if success, let id = localIdentifier {
            // [path: localIdentifier]，便于后续app 内删除系统相册的图片
            FileAlbumMapping.shared.update(id, key: fileURL.relativePath)
        }
        guard showHud else { return }
        DispatchQueue.main.async {
            let msg: String
            if success {
                msg = isImage ? NVLang("device_snap_success") : NVLang("device_recording_success")
            } else {
                msg = "\(NVLang("common_operating_fail")): \(error?.localizedDescription ?? "")"
            }
            if let target = target {
//                NVHud.showMessage(msg, toView: kWindow)
            } else {
//                NVHud.showMessage(msg, toView: kWindow)
            }
        }
    }
}

/// 下载文件与系统相册之间的映射管理
///
/// [filePath: PHAsset.localIdentifier]，便于后续 app 内删除文件时同步系统相册的图片
class FileAlbumMapping {
    static let shared = FileAlbumMapping()
    
    static let kMappingStoreKey = "kMappingStoreKey"
    
    private var mapping = [String: String]()
    
    init() {
        if let dict = UserDefaults.standard.dictionary(forKey: FileAlbumMapping.kMappingStoreKey) as? [String: String] {
            mapping = dict
        }
    }
    
    func value(for key: String) -> String? {
        return mapping[key]
    }
    
    func update(_ value: String, key: String) {
        mapping[key] = value
        synchronize()
    }
    
    func remove(key: String) {
        mapping.removeValue(forKey: key)
        synchronize()
    }
    
    func synchronize() {
        UserDefaults.standard.set(mapping, forKey: FileAlbumMapping.kMappingStoreKey)
        UserDefaults.standard.synchronize()
    }
    
}

///MARK: 网络请求
public func postAPNSToken() {
//    NIotLogger.info("[DEBUG] postAPNSToken")
//    if kAccountInfo != nil, kPushToken.length > 0 {
//        let urlStr = String(format: NV_User_PushToken, arguments: [NVEnvironment.shared.userId.aesEncode(), "{\"ios\":\"\(kPushToken)\"}".aesEncode()])
//        NIotLogger.info("[DEBUG] update push token: \(kPushToken)")
//        NVNetworkingManager.ShareInstance.putData(url: urlStr, params: nil, showLoading: false, showFailedHud: false, success: { (response) in
//            NIotLogger.info("[DEBUG] update push token success")
//        }) { (error) in
//            NIotLogger.error("[DEBUG] update push token failed")
//        }
//    }
}

///MARK: 切换分辨率操作
/*
public func changeResolution(deviceID: String, newValue: Int) {
    guard deviceID.length > 0 else {
        return
    }
    
    if var resolutionDataArr = UserDefaults.standard.value(forKey: "resolutionDataArr") as? [[String: String]] {
        var tempDict: [String: String] = [:]
        for resolutionDict in resolutionDataArr {
            if deviceID == resolutionDict["did"] {
                tempDict = resolutionDict
                tempDict["resolution"] = "\(newValue)"
                let index = resolutionDataArr.firstIndex(of: resolutionDict)
                resolutionDataArr[index!] = tempDict
                break
            }
        }
        if tempDict.keys.count == 0 {
            tempDict["did"] = deviceID
            tempDict["resolution"] = "\(newValue)"
            resolutionDataArr.append(tempDict)
        }
        UserDefaults.standard.setValue(resolutionDataArr, forKey: "resolutionDataArr")
        UserDefaults.standard.synchronize()
    } else {
        var resolutionDataArr = [[String: String]]()
        let resolutionDict = ["did": deviceID, "resolution": "\(newValue)"]
        resolutionDataArr.append(resolutionDict)
        UserDefaults.standard.setValue(resolutionDataArr, forKey: "resolutionDataArr")
        UserDefaults.standard.synchronize()
    }
}

func getResolution(deviceModel: NVDeviceModel) -> IVVideoDefinition? {
    guard deviceModel.device.id.length > 0 else {
        return nil
    }
    if let resolutionDataArr = UserDefaults.standard.value(forKey: "resolutionDataArr") as? [[String: String]] {
        for resolutionDict in resolutionDataArr {
            if deviceModel.device.id == resolutionDict["did"] {
                return resolutionDict["resolution"] == "0" ? .mid : .high
            }
        }
    }
    
    return deviceModel.is4gDevice ? .mid : .high
}
 */

/// MARK:初始化IOTsdk和获取设备配网的token
public func setupIOTSDKAndDeviceConfigToken() {
    guard let accessId = NVEnvironment.shared.user?.tAccessId,
          let accessToken = NVEnvironment.shared.user?.tAccessToken
    else { return }
    NIotLogger.info("[DEBUG] setupIOTSDKAndDeviceConfigToken")
    NIotManager.shared.register(accessId: String(accessId), accessToken: accessToken)
}

//判断系统选择的语言
func getCurrentLanguage() -> String {
    let preferredLang = Bundle.main.preferredLocalizations.first! as NSString
    switch String(describing: preferredLang) {
    case "en-US", "en-CN":
        return "en"//英文
    case "zh-Hans-US","zh-Hans-CN","zh-Hans":
        return "zh"//中文
    case "zh-Hant-TW","zh-Hant-HK","zh-Hant-CN","zh-TW","zh-HK","zh-Hant":
        return "zh-Hant"//中文
    case "pt-PT", "pt-BR":
        return "pt"//葡萄牙语
    case "es", "es-ES":
        return "es"//西班牙语
    case "ja":
        return "ja"//日语
    case "it":
        return "it"//意大利语
    case "fr":
        return "fr"//法语
    case "ar":
        return "ar"//阿拉伯语
    case "tr":
        return "tr"//土耳其语
    default:
        return "en"
    }
}

func deviceBindingLanguage() -> String {
    switch getCurrentLanguage() {
    case "zh", "zh-Hans-US", "zh-Hans-CN", "zh-Hans",  "zh-Hant-TW", "zh-Hant-HK", "zh-Hant-CN", "zh-TW", "zh-HK", "zh-Hant":
        return "2"//中文
    case "ja":
        return "3"//日语
    default:
        return "1"
    }
}

func isChineseLanguage() -> Bool {
    return getCurrentLanguage() == "zh" || getCurrentLanguage() == "zh-Hant"
}

///MARK - 跳转到App store
func gotoAPPStore() {
    let url = NSURL.init(string: "itms-apps://apps.apple.com/cn/app/niview/id1518203527")
    if UIApplication.shared.canOpenURL(url! as URL) {
        if #available(iOS 10.0, *) {
            UIApplication.shared.open(url! as URL, options: [:], completionHandler: nil)
        } else {
            UIApplication.shared.openURL(url! as URL)
        }
    }
}


class Utils {
    static func queryString(from parameters: [String: Any], needEncrypt: Bool = false) -> String {
        var parts: [String] = []
        
        for (key, value) in parameters {
            let keyString = key.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed)!
            var valueString = "\(value)".addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed)!
            if needEncrypt { valueString = valueString.aesEncode() }
            parts.append("\(keyString)=\(valueString)")
        }
        
        return parts.joined(separator: "&")
    }
    
    static func matchMQTTHostAndPort(from urlString: String) -> (String, UInt16)? {
        guard let regex = try? NSRegularExpression(pattern: #"^ssl://(\S+):(\d+)$"#, options: []) else { return nil }
        if let match = regex.firstMatch(in: urlString, options: [], range: NSRange(location: 0, length: urlString.utf16.count)),
           let range1 = Range(match.range(at: 1), in: urlString),
           let range2 = Range(match.range(at: 2), in: urlString) {
            let host = urlString[range1]
            let portStr = urlString[range2]
            if let port = UInt16(portStr) {
                return (String(host), port)
            }
        }
        return nil
    }
}

// MARK: 字典转字符串
public func dicValueString(_ dic:[String : Any]) -> String? {
    let data = try? JSONSerialization.data(withJSONObject: dic, options: [])
    let str = String(data: data!, encoding: .utf8)
    return str
}

public func stringValueArray(jsonString:String) -> NSArray? {
    if let jsonData:Data = jsonString.data(using: .utf8), let array = try? JSONSerialization.jsonObject(with: jsonData, options: .mutableContainers) as? NSArray {
        return array
    } else {
        return nil
    }
}
