//
//  XP2PDelegateForwarder.swift
//  NiView
//
//  Created by apple on 2023/9/30.
//  Copyright © 2023 nice. All rights reserved.
//

import Foundation
import TIoTLinkVideo

/// 用于包装弱引用的类
private class WeakDelegateWrapper {
    weak var delegate: TIoTCoreXP2PBridgeDelegate?
    
    init(_ delegate: TIoTCoreXP2PBridgeDelegate) {
        self.delegate = delegate
    }
}

class XP2PDelegateForwarder: NSObject {
    
    static let shared = XP2PDelegateForwarder()
    
    private override init() {
        super.init()
        TIoTCoreXP2PBridge.sharedInstance().delegate = self
    }
    
    // 修改为存储弱引用包装器的数组
    private var delegates: [String: [WeakDelegateWrapper]] = [:]
    
    func registerDelegate(_ delegate: TIoTCoreXP2PBridgeDelegate, forDeviceId deviceId: String) {
        // 清理可能存在的nil代理
        cleanNilDelegates(forDeviceId: deviceId)
        
        if delegates[deviceId] == nil {
            delegates[deviceId] = []
        }
        
        // 检查是否已经存在相同的代理（避免重复注册）
        for wrapper in delegates[deviceId] ?? [] {
            if wrapper.delegate === delegate {
                NIotLogger.info("[XP2PDelegateForwarder] 代理\(delegate)已注册，跳过重复注册: \(deviceId)")
                return
            }
        }
        
        delegates[deviceId]?.append(WeakDelegateWrapper(delegate))
        NIotLogger.info("[XP2PDelegateForwarder] 注册设备: \(deviceId) 代理: \(delegate)")
    }
    
    func unregisterSpecificDelegate(_ delegate: TIoTCoreXP2PBridgeDelegate, forDeviceId deviceId: String) {
        delegates[deviceId]?.removeAll(where: { $0.delegate === delegate })
        cleanNilDelegates(forDeviceId: deviceId)
        NIotLogger.info("[XP2PDelegateForwarder] 注销设备: \(deviceId) 代理: \(delegate)")
    }
    
    func unregisterDelegate(forDeviceId deviceId: String) {
        delegates.removeValue(forKey: deviceId)
        NIotLogger.info("[XP2PDelegateForwarder] 注销设备: \(deviceId) 所有代理")
    }
    
    /// 清理给定设备ID的nil代理
    private func cleanNilDelegates(forDeviceId deviceId: String) {
        delegates[deviceId]?.removeAll(where: { $0.delegate == nil })
        if delegates[deviceId]?.isEmpty == true {
            delegates.removeValue(forKey: deviceId)
        }
    }
    
    /// 清理所有设备的nil代理
    private func cleanAllNilDelegates() {
        for deviceId in delegates.keys {
            cleanNilDelegates(forDeviceId: deviceId)
        }
    }
}

extension XP2PDelegateForwarder: TIoTCoreXP2PBridgeDelegate {
    
    func getVideoPacket(withID dev_name: String, data: UnsafeMutablePointer<UInt8>, len: Int) {
        cleanNilDelegates(forDeviceId: dev_name)
        
        if let wrappers = delegates[dev_name] {
            for wrapper in wrappers {
                wrapper.delegate?.getVideoPacket(withID: dev_name, data: data, len: len)
            }
        }
    }
    
    func reviceDeviceMsg(withID dev_name: String, data: Data) -> String {
        if let jsonStr = data.string(with: .ascii) {
            NIotLogger.info("[XP2PDelegateForwarder] reviceDeviceMsg:\(jsonStr)")
        }
        
        cleanNilDelegates(forDeviceId: dev_name)
        
        if let wrappers = delegates[dev_name] {
            // 遍历所有代理，返回第一个非空响应
            for wrapper in wrappers {
                if let delegate = wrapper.delegate {
                    let response = delegate.reviceDeviceMsg(withID: dev_name, data: data)
                    if !response.isEmpty {
                        return response
                    }
                }
            }
        }
        return ""
    }
    
    func reviceEventMsg(withID dev_name: String, eventType: XP2PType, msg: UnsafePointer<CChar>) {
        NIotLogger.info("[XP2PDelegateForwarder]\(dev_name) reviceEventMsg: \(eventType.rawValue))")
        
        cleanNilDelegates(forDeviceId: dev_name)
        
        if let wrappers = delegates[dev_name] {
            for wrapper in wrappers {
                wrapper.delegate?.reviceEventMsg(withID: dev_name, eventType: eventType, msg: msg)
            }
        }
    }
}
