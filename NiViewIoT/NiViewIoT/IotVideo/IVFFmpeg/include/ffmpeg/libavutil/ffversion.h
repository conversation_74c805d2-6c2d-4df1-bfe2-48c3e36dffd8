/*
 * ffversion.h
 *
 * Copyright (c) 2013 Bilibili
 * Copyright (c) 2013 <PERSON> <<EMAIL>>
 *
 * This file is part of ijkPlayer.
 *
 * ijkPlayer is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * ijkPlayer is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with ijkPlayer; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301 USA
 */

#if   defined(__aarch64__)
#   include "arm64/ffversion.h"
#elif defined(__x86_64__)
#   include "x86_64/ffversion.h"
#elif defined(__arm__)

#   if   defined(__ARM_ARCH_7S__)
#       include "armv7s/ffversion.h"
#   elif defined(__ARM_ARCH_7__)
#       include "armv7/ffversion.h"
#   else
#       error Unsupport ARM architecture
#   endif

#elif defined(__i386__)
#   include "i386/ffversion.h"
#else
#   error Unsupport architecture
#endif
