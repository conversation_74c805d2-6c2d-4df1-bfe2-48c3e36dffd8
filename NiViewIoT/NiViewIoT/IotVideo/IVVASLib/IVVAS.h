//
//  IVVAS.h
//  IVVAS
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/4/2.
//  Copyright © 2020 Tencentcs. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "IVNetwork_p2p.h"

/* ⚠️Automatically generated by script, do not manually edit! */
#define kIVVASVersion "1.4(2032) Release" /* Commit:53246e7 */
#define kIVVASCompileDate "2024-03-26 15:01:26"


NS_ASSUME_NONNULL_BEGIN

//MARK: - 增值服务
@interface IVVAS : NSObject

/// 增值服务管理单例
+ (instancetype)shareInstance;
@property (class, nonatomic, strong, readonly) IVVAS *shared;

/// SDK版本
@property (class, nonatomic, strong, readonly) NSString *SDKVersion;

/// 云服务ID（仅用于匿名环境访问相关接口，开发者应先设置此参数后调用相关接口）
@property (nonatomic, strong, nullable) NSString *serviceId;

@end

//MARK: - 云存信息
@interface IVVAS (Service)

/// 查询多通道设备的云存详细信息
///
/// @param deviceId 设备id
/// @param sourceId 源ID
/// @param handler 结果回调
///     成功JSON:  {
///         "code":0,                                                   // 错误码   0    成功
///         "msg":"Success",
///         "data":{
///             "startTime":1606709335,                 // int64 云存服务开始时间。
///             "endTime":1611979735,                   // int64 云存服务失效时间。
///             "curOrderPkgType":1,                       // int64 当前订单类型。[1] 全时云存;  [2] 事件云存;  [3] 免费事件云存;
///             "curOrderStorageDays":3,                // int64 当前订单存储时长，单位天。
///             "curOrderStartTime":1606709335,   // int64 当前订单开始时间。
///             "curOrderEndTime":1606709335,    // int64 当前订单结束事件。
///             "playbackStartTime":1606709335,   // int64 当前云存服务，支持检索回放文件的最早时间。这个时间点之前的云存文件不支持检索。
///             "playbackSpeedSupport":[8, 16, 32]  // 当前支持倍速回放的倍数
///             "status":1,                                          // 云存服务状态      1    正常使用中。
///                                                     2    待续费。设备云存服务已到期，但是历史云存数据未过期。续费后仍可查看这些历史数据。
///                                                     3    已过期。查询不到设备保存在云端的数据。
///                                                     4    等待生效中（购买时，指定了EnableTime参数）。
///         }
///     }
///
///     失败JSON: {
///         "msg":"releation cehck fail.",
///         "code":2,             // 错误码  1    设备当前没人绑定主人
///                             2    校验关系链出错。只支持设备主人查询云存信息。
///                             3    云存服务已经过期
///                             4    当前主人没有给设备开通过云存服务
///     }
- (void)getServiceDetailInfoWithDeviceId:(NSString *)deviceId sourceId:(NSInteger)sourceId responseHandler:(IVNetworkResponseHandler _Nullable)handler;


/// 查询设备的云存详细信息
///
/// 等同于 [self getServiceDetailInfoWithDeviceId:deviceId sourceId:0 responseHandler:handler]
/// @param deviceId 设备id
/// @param handler 结果回调
- (void)getServiceDetailInfoWithDeviceId:(NSString *)deviceId responseHandler:(IVNetworkResponseHandler _Nullable)handler;

@end


//MARK: - 云回放
@interface IVVAS (Playback)

/// 查询有云存视频的日期列表（可匿名访问）
///
/// 用于对云存服务时间范围内（通常是一个月）的日期进行标注，描述哪些日期有云存视频文件
/// @param deviceId 设备id
/// @param timezone 相对于0时区的秒数，例如东八区是28800
/// @param handler 结果回调
/// 	JOSN：{
///         "requestId":"xxxxxx",
///         "code":0,
///         "msg":"Success",
///         "data":{
///             "list":[
///                1610208000,      // int64 UTC时间的日期，单位：秒；下同。
///                1610380800
///             ]
///         }
///     }
- (void)getVideoDateListWithDeviceId:(NSString *)deviceId timezone:(NSInteger)timezone responseHandler:(IVNetworkResponseHandler _Nullable)handler;


/// 查询云存视频文件时间分布表（可匿名访问）
///
/// 用于描述给定时间范围内（通常是一天）云存视频文件的分布信息，单个文件时长上限为1小时, 常用于时间轴渲染。
/// @param deviceId 设备id
/// @param startTime 开始UTC时间,单位秒
/// @param endTime 结束UTC时间,单位秒 超过一天只返回一天
/// @param handler 结果回调
///     JSON：{
///         "requestId":"xxxxxx",
///         "code":0,
///         "msg":"Success",
///         "data":{
///             "list":[{                                         // array 文件时间信息数组，单个文件不超过1小时, 单位：秒；
///                "startTime":1610208001,
///                "endTime":1610208032
///              }, {
///                "startTime":1610208043,
///                "endTime":1610208084
///              },
///             ]
///         }
///     }
- (void)getVideoTimeListWithDeviceId:(NSString *)deviceId startTime:(NSTimeInterval)startTime endTime:(NSTimeInterval)endTime responseHandler:(IVNetworkResponseHandler _Nullable)handler;


/// 获取云存视频文件的播放地址列表（可匿名访问）
///
/// 用于获取给定时间范围内（通常是若干小时）云存视频文件的播放地址列表。1.单个URL可播放时长最多1小时；2.遇到时间不连续之处拆分URL；3.单次返回最多100个URL。
/// @param deviceId 设备id
/// @param startTime 开始UTC时间,单位秒
/// @param endTime 结束UTC时间, 单位秒，填 0 则默认为开始时间后一小时，单次最多只会返回一天的数据
/// @param handler 结果回调
///     JSON：{
///       "requestId":"xxxxxx",
///       "code":0,
///       "msg":"Success",
///       "data":{
///           "endflag":true，                 //  bool 结束标记，表示给定时间范围内的数据是否已全部返回，否则需以返回的最大endTime为起始时间再次请求。
///           "list":[{                                // array m3u8地址数组, 单次最多返回100个URL，单个URL可播放时长最多1小时，单位：秒；
///               "url":"xxxx",
///               "startTime":1600653454,
///               "endTime":1600653464
///           },{
///               "url":"xxxx",
///               "startTime":1600653474,
///               "endTime":1600653484
///           }],
///       }
///    }
- (void)getVideoPlayURLWithDeviceId:(NSString *)deviceId startTime:(NSTimeInterval)startTime endTime:(NSTimeInterval)endTime responseHandler:(IVNetworkResponseHandler _Nullable)handler;


/// 获取云存视频文件的高倍速播放起始地址（可匿名访问）
///
/// 用于获取给定时间范围内（通常小于1天）云存视频文件的高倍速播放起始地址，由云端实时抽帧实现，类似于直播，不支持pause/seek控制，但可通过修改startTime/endTime重新请求播放地址达到类似效果；
/// 低倍速播放（小于8x）可使用普通播放URL+播放器自带倍速功能实现。
/// @param deviceId 设备id
/// @param startTime 开始UTC时间,单位秒（⚠️特别要求：请求区间R=[startTime,endTime]，R区间有效视频片段总时长为T，要求T除以speed大于4秒)
/// @param endTime 结束UTC时间, 单位秒，填 0 则默认为开始时间后一小时，单次最多只会返回一天的数据
/// @param speed 播放倍速，目前支持8/16/32
/// @param handler 结果回调
///     JSON：{
///       "requestId":"xxxxxx",
///       "code":0,
///       "msg":"Success",
///       "data":{
///           "url":"xxxx",        // string 高倍速播放m3u8起始地址，不支持pause/seek控制，但可通过修改startTime/endTime重新请求播放地址达到类似效果；
///           "videoDuration": 287 // 预估视频(倍速后)时长
///       }
///    }
- (void)getVideoPlayURLWithDeviceId:(NSString *)deviceId startTime:(NSTimeInterval)startTime endTime:(NSTimeInterval)endTime speed:(NSUInteger)speed responseHandler:(IVNetworkResponseHandler _Nullable)handler;

/// 根据时间范围删除云存视频文件（可匿名访问）
///
/// @param deviceId 设备id
/// @param startTime 开始UTC时间, 单位秒
/// @param endTime 结束UTC时间, 单位秒，时间范围不能超过一天
/// @param incEvts 是否删除范围内的所有事件
/// @param handler 结果回调 incEvts
///   JSON：{
///        "requestId":"xxxxxx",
///        "code":0,
///        "msg":"Success"
///    }
- (void)deleteVideoFilesWithDeviceId:(NSString *)deviceId startTime:(NSTimeInterval)startTime endTime:(NSTimeInterval)endTime incEvts:(BOOL)incEvts responseHandler:(IVNetworkResponseHandler _Nullable)handler;

@end


//MARK: - 云事件
@interface IVVAS (Events)

/// 获取有事件的日期列表（可匿名访问）
///
/// 用于终端用户在云存页面中对日期进行标注，区分出是否有事件。
/// @param deviceId 设备id
/// @param timezone 相对于0时区的秒数，例如东八区28800
/// @param handler 结果回调
///     JSON: {
///         "requestId": "860186b00000001c-faf764a3-61515e79",
///         "code": 0,
///         "msg": "Success",
///         "data": {
///             "list": [
///                 1632326400,  // int64 UTC时间的日期，单位：秒。下同。
///                 1632412800,
///                 1632499200,
///             ]
///         }
///     }
- (void)getEventDateListWithDeviceId:(NSString *)deviceId timezone:(NSInteger)timezone responseHandler:(IVNetworkResponseHandler _Nullable)handler;


/// 获取事件列表（可匿名访问）
///
/// @param deviceId 设备id
/// @param startTime 事件告警开始UTC时间, 一般为当天开始时间， 单位秒
/// @param endTime 事件告警结束UTC时间，获取更多应传入当前列表的最后一个事件的开始时间(事件列表按时间逆序排列)；
/// @param pageSize 本次最多查询多少条记录，取值范围 [1 - 50]
/// @param typeMasks 筛选指定类型的事件掩码数组：Array<Int64>，过滤规则如下：
///     - bit[0-15]为SDK内置, 其中bit15(即 0x8000)是标记为有视频的事件;  bit[16-32]为开发者可自定义类型；
///     - 对于数组中的掩码，掩码值自身用**按位或**逻辑，掩码值之间用**按位与**逻辑，例如：
///       `almTypeMasks = [3]`, 其中`3`等于`bit0 | bit1`， 此时获取到的事件为 **包含bit0 或 bit1类型的事件**；
///       `almTypeMasks = [1, 2]`, 其中`1` 等于 `bit0` ，`2` 等于 `bit1`， 此时获取到的事件为 **同时包含bit0 和 bit1类型的事件**；
///       `almTypeMasks = [0x8000, 3], 其中`0x8000`等于有视频的事件, `3`等于事件`bit0`或事件`bit1`,  此时获取到的事件为 **有视频并且是bit0或bit1类型的事件**；
/// @param validCloudStorage 是否只返回有效云存期内的事件
/// @param handler 结果回调
///     JSON: {
///         "requestId": "860186b00000001c-faf764aa-61515f4c",
///         "code": 0,
///         "msg": "Success",
///         "data": {
///             "imgUrlPrefix": "xxxxxx",                                                   // string 原图及缩略图下载地址前缀
///             "thumbUrlSuffix": "&imageMogr2/thumbnail/213x120",    // string 缩略图下载地址后缀
///             "validVideoStartTime": 1632390457,                                // int64 云存未过期视频的开始时间，为0代表未查询到云存记录
///             "pageEnd": false,                                                             // bool 分页结束标志
///             "list": [                                                                              // array 云存事件列表，返回顺序以起始时间按降序排列
///                {
///                    "alarmId": "xxxxxx",                                           // string 事件id
///                    "firstAlarmType": 2147483648,                         // int64 告警触发时的告警类型
///                    "alarmType": 2281738496,                               // int64 告警时间范围内触发过的告警类型
///                    "startTime": 1632585320                                  // int64 告警触发时间, utc时间，单位秒
///                    "endTime": 1632585380,                                  // int64 告警结束时间, utc时间，单位秒
///                    "imgUrlSuffix": "xxxxxx",                                   // string 告警图片下载地址后缀.  原图    = imgUrlPrefix + imgUrlSuffix
///                                                                            缩略图 = imgUrlPrefix + imgUrlSuffix + thumbUrlSuffix
///                },
///                ...
///             ],
///         }
///     }
///
///  使用示例：
///    /// 上拉加载更多
///    func getMoreEvents() {
///        let endTime = eventList.last?.startTime ?? currDate + 86400
///        IVVAS.shared.getEventList(withDeviceId: deviceID, startTime: currDate, endTime: endTime, pageSize: 50, filterTypeMask: 0) { [weak self](json, error) in
///            /* get more data here */
///        }
///    }
///
///    /// 下拉刷新
///    func refreshEvents() {
///        let endTime = currDate + 86400
///        IVVAS.shared.getEventList(withDeviceId: deviceID, startTime: currDate, endTime: endTime, pageSize: 50, filterTypeMask: 0) { [weak self](json, error) in
///            /* new data here */
///        }
///    }
- (void)getEventListWithDeviceId:(NSString *)deviceId startTime:(NSTimeInterval)startTime endTime:(NSTimeInterval)endTime pageSize:(NSInteger)pageSize filterTypeMask:(NSArray<NSNumber *> * _Nullable)typeMasks validCloudStorage:(BOOL)validCloudStorage responseHandler:(IVNetworkResponseHandler _Nullable)handler;


/// 获取完整事件列表（可匿名访问）
/// 本接口根据过滤类型返回给定时间范围内并且在云存套餐有效期内的所有事件，与【获取事件列表】接口相比，本接口返回的事件参数是简化的
///
/// @param deviceId 设备id
/// @param startTime 开始时间, utc时间，单位秒
/// @param endTime 结束时间，utc时间，单位秒,  时间范围不能超过一天
/// @param typeMasks 筛选指定类型的事件掩码数组：Array<Int64>，过滤规则参考【获取事件列表】
/// @param handler 结果回调
///     JSON: {
///         "requestId": "860186b00000001c-faf764aa-61515f4c",
///         "code": 0,
///         "msg": "Success",
///         "data":  {
///            "list":[ {
///                   "t":1,                        // int64 告警时间范围内触发过的告警类型
///                   "s":1632585320,     // int64 事件开始时间, utc时间，单位秒
///                   "e":1632585380      // int64 事件结束时间, utc时间，单位秒
///                 }, ... ]
///         }
///     }
- (void)getFullEventListWithDeviceId:(NSString *)deviceId startTime:(NSTimeInterval)startTime endTime:(NSTimeInterval)endTime filterTypeMask:(NSArray<NSNumber *> * _Nullable)typeMasks responseHandler:(IVNetworkResponseHandler _Nullable)handler;


/// 查询事件详情
///
/// @param deviceId 设备id
/// @param alarmId 报警事件id
/// @param handler 结果回调
///    JSON：{
///        "requestId":"xxxxxx",
///        "code":0,
///        "msg":"Success",
///        "data":{
///            "alarmId": "xxxxxx",                                           // string 事件id
///            "deviceId":123,                                                  // int64 设备ID
///            "firstAlarmType": 2147483648,                         // int64 告警触发时的告警类型
///            "alarmType": 2281738496,                               // int64 告警有效时间内触发过的告警类型
///            "startTime": 1632585320                                  // int64 告警触发时间, utc时间，单位秒
///            "endTime": 1632585380,                                  // int64 告警结束时间, utc时间，单位秒
///            "imgUrl":"xxxxx",                                               // string 告警图片下载地址
///            "thumbUrlSuffix": "xxxxxx",                               // string 缩略图下载地址后缀.  缩略图 = imgUrl + thumbUrlSuffix
///        }
///    }
- (void)getEventInfoWithDeviceId:(NSString *)deviceId alarmId:(NSString *)alarmId responseHandler:(IVNetworkResponseHandler _Nullable)handler;


/// 根据ID删除事件
///
/// @param deviceId 设备id
/// @param alarmIds 事件ID数组
/// @param handler 结果回调
///    JSON：{
///        "requestId":"xxxxxx",
///        "code":0,
///        "msg":"Success"
///    }
- (void)deleteEventsWithDeviceId:(NSString *)deviceId alarmIds:(NSArray<NSString *> *)alarmIds responseHandler:(IVNetworkResponseHandler _Nullable)handler;


/// 根据时间删除事件
///
/// @param deviceId 设备id
/// @param alarmTimes 事件开始时间(startTime)的数组 [uint64_t], 单位秒
/// @param handler 结果回调
///   JSON：{
///        "requestId":"xxxxxx",
///        "code":0,
///        "msg":"Success"
///    }
- (void)deleteEventsWithDeviceId:(NSString *)deviceId alarmTimes:(NSArray<NSNumber *> *)alarmTimes responseHandler:(IVNetworkResponseHandler _Nullable)handler;
@end



//MARK: - 过期的方法
@interface IVVAS (Deprecated)

/// 获取回放 m3u8 播放地址（可匿名访问）
/// 尽可能多的将给定时间范围内的ts视频片段合并到一个m3u8文件里, 因此m3u8中的ts时间可能不连续, 播放器时间可能跳跃
- (void)getVideoPlayAddressWithDeviceId:(NSString *)deviceId startTime:(NSTimeInterval)startTime endTime:(NSTimeInterval)endTime responseHandler:(IVNetworkResponseHandler _Nullable)responseHandler API_DEPRECATED("Use `-getVideoPlayURLWithDeviceId:startTime:endTime:responseHandler:` or `-getVideoPlayURLWithDeviceId:startTime:endTime:speed:responseHandler:` instead", ios(2.0,9.0));

/// 获取回放文件列表（已过期）
- (void)getVideoPlayListWithDeviceId:(NSString *)deviceId startTime:(NSTimeInterval)startTime endTime:(NSTimeInterval)endTime responseHandler:(IVNetworkResponseHandler _Nullable)responseHandler API_DEPRECATED("Use -getVideoTimeListWithDeviceId:startTime:endTime:responseHandler: instead", ios(2.0,9.0));

/// 获取事件列表（已过期）
- (void)getEventListWithDeviceId:(NSString *)deviceId startTime:(NSTimeInterval)startTime endTime:(NSTimeInterval)endTime pageNum:(NSInteger)pageNum pageSize:(NSInteger)pageSize responseHandler:(IVNetworkResponseHandler _Nullable)responseHandler API_DEPRECATED("Use -getEventListWithDeviceId:startTime:endTime:pageSize:filterTypeMask:responseHandler: instead", ios(2.0,9.0));

/// 事件删除（已过期）
- (void)deleteEventsWithDeviceId:(NSString *)deviceId eventIds:(NSArray<NSString *> *)eventIds responseHandler:(IVNetworkResponseHandler _Nullable)responseHandler API_DEPRECATED("Use  -deleteEventsWithDeviceId:alarmIds:responseHandler: instead", ios(2.0,9.0));

@end

NS_ASSUME_NONNULL_END


