framework module NiViewIoT {
    umbrella header "/Users/<USER>/Desktop/NiViewIoT/NiViewIoT/NiViewIoT/NiViewIoT.h"

    export *
    module * { export * }
     
    link framework "MediaToolBox"
    link framework "VideoToolBox"
    link framework "CoreMedia"
    link "libc++"
    link "libiconv"
    link "bz2"
    link "z"
     

     explicit module NIot_IoTVideo_Private {
         umbrella header "IoTVideo.h"
         export *

     }

     explicit module NIot_IVVAS_Private {
         header "IVVAS.h"
         export *
       
     }
//    explicit module NIot_IoTVideo_Private {
//        umbrella header "/Users/<USER>/Desktop/NiViewIoT/NiViewIoT/NiViewIoT/IotVideo/IoTLib/IoTVideo.h"
//        export *
//
//    }
//
//    explicit module NIot_IVVAS_Private {
//        header "/Users/<USER>/Desktop/NiViewIoT/NiViewIoT/NiViewIoT/IotVideo/IVVASLib/IVVAS.h"
//        export *
//      
//    }
    
    explicit module NIot_TIoTLinkKit_Private {
        umbrella header "/Users/<USER>/Desktop/NiViewIoT/NiViewIoT/NiViewIoT/TIoTLinkKit/Headers/TIoTLinkKit-umbrella.h"
        export *
    }

//    explicit module NIot_TIoTLinkVideo_Private {
//        umbrella header "/Users/<USER>/Desktop/niview_sdk/Pods/Target Support Files/TIoTLinkVideo/TIoTLinkVideo-umbrella.h"
//        export *
//        explicit module TIoTLinkKit_XP2P {
//             header "/Users/<USER>/Desktop/niview_sdk/Pods/TIoTLinkKit_XP2P/Source/XP2P-iOS/Classes/AppWrapper.h"
//             export *
//         }
//    }
    
//
//    explicit module NIot_TIoTLinkVideo_Private {
//        umbrella header "/Users/<USER>/Desktop/niview_sdk/NIot/IoT_2/TIoTLinkVideo/Headers/TIoTLinkVideo-umbrella.h"
//        export *
//        explicit module TIoTLinkKit_XP2P {
//             header "/Users/<USER>/Desktop/niview_sdk/Pods/TIoTLinkKit_XP2P/Source/XP2P-iOS/Classes/AppWrapper.h"
//             export *
//         }
//    }

//    explicit module NIot_TIoTLinkKit_Private {
//        umbrella header "/Users/<USER>/Desktop/niview_sdk/Pods/Target Support Files/TIoTLinkKit/TIoTLinkKit-umbrella.h"
//        export *
//    }
}
