//
//  QCAPISets.h
//  QCAPISets
//
//

#import <Foundation/Foundation.h>

//! Project version number for QCAPISets.
FOUNDATION_EXPORT double QCAPISetsVersionNumber;

//! Project version string for QCAPISets.
FOUNDATION_EXPORT const unsigned char QCAPISetsVersionString[];

// In this header, you should import all the public headers of your framework using statements like #import <QCAPISets/PublicHeader.h>

#import "TIoTCoreAccountSet.h"
#import "TIoTCoreDeviceSet.h"
#import "TIoTCoreFamilySet.h"
#import "TIoTCoreMessageSet.h"

FOUNDATION_EXPORT const NSString *TIoTLinkKitShortVersionString;
