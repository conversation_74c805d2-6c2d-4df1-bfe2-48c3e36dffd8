//
//  QCFoundation.h
//  QCFoundation
//
//

#import <Foundation/Foundation.h>

//! Project version number for QCFoundation.
FOUNDATION_EXPORT double QCFoundationVersionNumber;

//! Project version string for QCFoundation.
FOUNDATION_EXPORT const unsigned char QCFoundationVersionString[];

// In this header, you should import all the public headers of your framework using statements like #import <QCFoundation/PublicHeader.h>

#import "TIoTCoreServices.h"
#import "TIoTCoreRequestBuilder.h"
#import "TIoTCoreRequestClient.h"
#import "TIoTCoreSocketManager.h"
#import "TIoTCoreUserManage.h"
#import "NSString+Extension.h"
