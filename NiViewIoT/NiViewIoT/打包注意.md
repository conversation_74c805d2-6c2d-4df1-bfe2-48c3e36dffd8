/Users/<USER>/Desktop/niview_sdk/NIot/IotVideo/IVP2P/libmbedtls1.a
与
/Users/<USER>/Desktop/niview_sdk/Pods/TIoTLinkKit_XP2P/Source/XP2P-iOS/libmbedtls.a
冲突
TIoTLinkKit_XP2P的静态库会覆盖掉IotVideo的静态库，但是两个静态库版本不一致，所以覆盖后会导致IotVideo在 demo 中无法编译成功，所以需要将IotVideo中的libmbedtls.a重命名

报错 
Q: 'HandyJSON' is not a member type of protocol 'HandyJSON.HandyJSON'
A: 工程 target → Install Generated Header → Yes


SDK BuildSettings需要设置一下选项：
Mach-O Type → Static Library
Build Libraries for Distribution → Yes
⚠️Enable Module Verifier → No
⚠️Install Generated Header → Yes
检查：
Module Map File 
Framework Search Paths
Header Search Paths
Library Search Paths

HandyJSON target Optimization Level 设置为 None及 No Optimization
