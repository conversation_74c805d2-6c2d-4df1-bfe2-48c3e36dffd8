# 网络请求 HUD 处理解决方案

## 问题分析

在 iOS SDK 开发中，我们面临以下挑战：
1. **SDK 职责边界**：SDK 应该专注于业务逻辑，不应直接处理 UI
2. **重复代码**：每个网络请求都需要手动处理 HUD 显示/隐藏
3. **错误处理**：通用错误（如 Token 过期、网络错误）需要统一处理
4. **用户体验**：需要提供一致的加载提示和错误反馈

## 解决方案对比

### 方案1: UIViewController 扩展 + 网络请求包装器

**特点：**
- 在应用层创建网络请求包装器
- 通过 UIViewController 扩展提供便利方法
- 统一处理 HUD 和错误逻辑

**优点：**
- 简单易用，学习成本低
- 保持 SDK 纯净性
- 统一的错误处理逻辑

**缺点：**
- 需要为每个接口创建包装方法
- 不够灵活，难以处理复杂场景

**使用示例：**
```swift
// 发送验证码
NetworkManager.shared.sendVerifyCode(email: email, from: self) { success in
    // 处理成功回调
}

// 登录
NetworkManager.shared.login(email: email, verifyCode: code, from: self) { userData in
    self.loginSucceed(userData)
}
```

### 方案2: 基于协议的网络拦截器

**特点：**
- 使用拦截器模式处理网络请求生命周期
- 支持多个拦截器，可扩展性强
- 职责分离，便于测试和维护

**优点：**
- 高度可扩展，支持插件化
- 职责分离，便于单元测试
- 支持日志记录、错误上报等功能

**缺点：**
- 实现复杂度较高
- 学习成本相对较高

**使用示例：**
```swift
// 添加自定义拦截器
InterceptableNetworkManager.shared.addInterceptor(LoggingNetworkInterceptor())

// 使用
InterceptableNetworkManager.shared.sendVerifyCode(email: email, from: self) { success in
    // 处理结果
}
```

### 方案3: 响应式网络管理器（推荐）

**特点：**
- 支持链式调用，代码更优雅
- 自动处理 HUD 和错误
- 支持复杂的异步操作组合

**优点：**
- 代码简洁优雅，可读性强
- 支持链式调用，便于组合操作
- 自动处理常见场景
- 灵活的配置选项

**缺点：**
- 初期实现成本较高
- 需要团队熟悉响应式编程概念

**使用示例：**
```swift
// 简单调用
ReactiveNetworkManager.shared
    .sendVerifyCode(email: email, from: self)
    .onSuccess { _ in
        self.showMsg("Verification code sent successfully")
    }

// 链式调用
ReactiveNetworkManager.shared
    .login(email: email, verifyCode: code, from: self)
    .onSuccess { userData in
        self.loginSucceed(userData)
        
        // 登录成功后自动获取设备列表
        guard let uid = Environment.shared.uid else { return }
        ReactiveNetworkManager.shared
            .getDeviceList(uid: uid, from: self, silently: true)
            .onSuccess { devices in
                self.updateDeviceList(devices)
            }
    }
```

## 统一错误处理

所有方案都支持以下统一错误处理：

1. **Token 过期 (-401)**：自动跳转登录页
2. **服务繁忙 (-429)**：显示友好提示
3. **账号密码错误 (-4019)**：显示具体错误信息
4. **设备已绑定 (-4026)**：显示设备冲突提示
5. **网络错误**：显示通用网络错误提示

## 配置选项

所有方案都支持灵活的配置：

```swift
struct NetworkRequestConfig {
    let loadingMessage: String      // 加载提示文字
    let showLoading: Bool          // 是否显示加载 HUD
    let showErrorAlert: Bool       // 是否显示错误弹窗
    let autoHandleCommonErrors: Bool // 是否自动处理通用错误
}
```

## 推荐使用

**对于新项目**：推荐使用 **方案3（响应式网络管理器）**
- 代码优雅，可读性强
- 支持复杂的异步操作组合
- 便于后续维护和扩展

**对于现有项目**：推荐使用 **方案1（网络请求包装器）**
- 改动成本最小
- 快速解决重复代码问题
- 易于团队理解和使用

**对于大型项目**：可以考虑 **方案2（拦截器模式）**
- 高度可扩展
- 支持复杂的业务需求
- 便于集成监控和分析功能

## 最佳实践

1. **保持 SDK 纯净**：不在 SDK 中直接处理 UI 逻辑
2. **统一错误处理**：在应用层统一处理常见错误
3. **配置化**：提供灵活的配置选项满足不同场景
4. **链式调用**：支持复杂的异步操作组合
5. **职责分离**：网络请求、UI 处理、错误处理分离

## 总结

通过以上方案，我们可以在保持 SDK 纯净性的同时，为应用开发者提供便利的网络请求处理方式，有效减少重复代码，提升开发效率和用户体验。
