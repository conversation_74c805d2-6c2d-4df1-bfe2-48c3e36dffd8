// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		184DB5E32C2D590A00234BEB /* BindDeviceViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 184DB5D92C2D590A00234BEB /* BindDeviceViewController.swift */; };
		184DB5E42C2D590A00234BEB /* LoginViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 184DB5DA2C2D590A00234BEB /* LoginViewController.swift */; };
		184DB5E52C2D590A00234BEB /* Device.swift in Sources */ = {isa = PBXBuildFile; fileRef = 184DB5DB2C2D590A00234BEB /* Device.swift */; };
		184DB5E62C2D590A00234BEB /* DevicesViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 184DB5DD2C2D590A00234BEB /* DevicesViewController.swift */; };
		184DB5E72C2D590A00234BEB /* Environment.swift in Sources */ = {isa = PBXBuildFile; fileRef = 184DB5DE2C2D590A00234BEB /* Environment.swift */; };
		184DB5E82C2D590A00234BEB /* NVWifiTool.swift in Sources */ = {isa = PBXBuildFile; fileRef = 184DB5DF2C2D590A00234BEB /* NVWifiTool.swift */; };
		184DB5E92C2D590A00234BEB /* UIViewController+HUD.swift in Sources */ = {isa = PBXBuildFile; fileRef = 184DB5E02C2D590A00234BEB /* UIViewController+HUD.swift */; };
		184DB5EA2C2D590A00234BEB /* LiveViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 184DB5E22C2D590A00234BEB /* LiveViewController.swift */; };
		187BA4B52C2BEC1600AC4A65 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 187BA4B42C2BEC1600AC4A65 /* AppDelegate.swift */; };
		187BA4B72C2BEC1600AC4A65 /* SceneDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 187BA4B62C2BEC1600AC4A65 /* SceneDelegate.swift */; };
		187BA4B92C2BEC1600AC4A65 /* ViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 187BA4B82C2BEC1600AC4A65 /* ViewController.swift */; };
		187BA4BC2C2BEC1600AC4A65 /* Base in Resources */ = {isa = PBXBuildFile; fileRef = 187BA4BB2C2BEC1600AC4A65 /* Base */; };
		187BA4BE2C2BEC1700AC4A65 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 187BA4BD2C2BEC1700AC4A65 /* Assets.xcassets */; };
		187BA4C12C2BEC1700AC4A65 /* Base in Resources */ = {isa = PBXBuildFile; fileRef = 187BA4C02C2BEC1700AC4A65 /* Base */; };
		187BA4DF2C2BEEBC00AC4A65 /* NiViewIoT.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 187BA4DE2C2BEEBC00AC4A65 /* NiViewIoT.framework */; };
		187BA4E02C2BEEBC00AC4A65 /* NiViewIoT.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 187BA4DE2C2BEEBC00AC4A65 /* NiViewIoT.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		48A3ED3F8B75B4D72390C6DA /* Pods_NiViewIoTDemo.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 40C247B40861BDF2CD3E1BF4 /* Pods_NiViewIoTDemo.framework */; };
/* End PBXBuildFile section */

/* Begin PBXCopyFilesBuildPhase section */
		187BA4E12C2BEEBC00AC4A65 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				187BA4E02C2BEEBC00AC4A65 /* NiViewIoT.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		184DB5D92C2D590A00234BEB /* BindDeviceViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BindDeviceViewController.swift; sourceTree = "<group>"; };
		184DB5DA2C2D590A00234BEB /* LoginViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LoginViewController.swift; sourceTree = "<group>"; };
		184DB5DB2C2D590A00234BEB /* Device.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Device.swift; sourceTree = "<group>"; };
		184DB5DD2C2D590A00234BEB /* DevicesViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = DevicesViewController.swift; sourceTree = "<group>"; };
		184DB5DE2C2D590A00234BEB /* Environment.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Environment.swift; sourceTree = "<group>"; };
		184DB5DF2C2D590A00234BEB /* NVWifiTool.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NVWifiTool.swift; sourceTree = "<group>"; };
		184DB5E02C2D590A00234BEB /* UIViewController+HUD.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "UIViewController+HUD.swift"; sourceTree = "<group>"; };
		184DB5E22C2D590A00234BEB /* LiveViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LiveViewController.swift; sourceTree = "<group>"; };
		187BA4B12C2BEC1600AC4A65 /* NiViewIoTDemo.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = NiViewIoTDemo.app; sourceTree = BUILT_PRODUCTS_DIR; };
		187BA4B42C2BEC1600AC4A65 /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		187BA4B62C2BEC1600AC4A65 /* SceneDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SceneDelegate.swift; sourceTree = "<group>"; };
		187BA4B82C2BEC1600AC4A65 /* ViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ViewController.swift; sourceTree = "<group>"; };
		187BA4BB2C2BEC1600AC4A65 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		187BA4BD2C2BEC1700AC4A65 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		187BA4C02C2BEC1700AC4A65 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		187BA4C22C2BEC1700AC4A65 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		187BA4DE2C2BEEBC00AC4A65 /* NiViewIoT.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = NiViewIoT.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		40C247B40861BDF2CD3E1BF4 /* Pods_NiViewIoTDemo.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_NiViewIoTDemo.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		5EAC9E06F4F75F0535E8E9C7 /* Pods-NiViewIoTDemo.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-NiViewIoTDemo.debug.xcconfig"; path = "Target Support Files/Pods-NiViewIoTDemo/Pods-NiViewIoTDemo.debug.xcconfig"; sourceTree = "<group>"; };
		5F86437B2BAB5EDC1EF6197E /* Pods-NiViewIoTDemo.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-NiViewIoTDemo.release.xcconfig"; path = "Target Support Files/Pods-NiViewIoTDemo/Pods-NiViewIoTDemo.release.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		187BA4AE2C2BEC1600AC4A65 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				48A3ED3F8B75B4D72390C6DA /* Pods_NiViewIoTDemo.framework in Frameworks */,
				187BA4DF2C2BEEBC00AC4A65 /* NiViewIoT.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		184DB5DC2C2D590A00234BEB /* Model */ = {
			isa = PBXGroup;
			children = (
				184DB5DB2C2D590A00234BEB /* Device.swift */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		184DB5E12C2D590A00234BEB /* Utils */ = {
			isa = PBXGroup;
			children = (
				184DB5DE2C2D590A00234BEB /* Environment.swift */,
				184DB5DF2C2D590A00234BEB /* NVWifiTool.swift */,
				184DB5E02C2D590A00234BEB /* UIViewController+HUD.swift */,
			);
			path = Utils;
			sourceTree = "<group>";
		};
		187BA4A82C2BEC1600AC4A65 = {
			isa = PBXGroup;
			children = (
				187BA4B32C2BEC1600AC4A65 /* NiViewIoTDemo */,
				187BA4B22C2BEC1600AC4A65 /* Products */,
				87436DBB437B1815631E4A38 /* Pods */,
				C966F52803DE78CCA48733BF /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		187BA4B22C2BEC1600AC4A65 /* Products */ = {
			isa = PBXGroup;
			children = (
				187BA4B12C2BEC1600AC4A65 /* NiViewIoTDemo.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		187BA4B32C2BEC1600AC4A65 /* NiViewIoTDemo */ = {
			isa = PBXGroup;
			children = (
				187BA4C22C2BEC1700AC4A65 /* Info.plist */,
				187BA4B42C2BEC1600AC4A65 /* AppDelegate.swift */,
				184DB5D92C2D590A00234BEB /* BindDeviceViewController.swift */,
				184DB5DD2C2D590A00234BEB /* DevicesViewController.swift */,
				184DB5E22C2D590A00234BEB /* LiveViewController.swift */,
				184DB5DA2C2D590A00234BEB /* LoginViewController.swift */,
				187BA4B62C2BEC1600AC4A65 /* SceneDelegate.swift */,
				187BA4B82C2BEC1600AC4A65 /* ViewController.swift */,
				187BA4BD2C2BEC1700AC4A65 /* Assets.xcassets */,
				187BA4BF2C2BEC1700AC4A65 /* LaunchScreen.storyboard */,
				187BA4BA2C2BEC1600AC4A65 /* Main.storyboard */,
				184DB5DC2C2D590A00234BEB /* Model */,
				184DB5E12C2D590A00234BEB /* Utils */,
			);
			path = NiViewIoTDemo;
			sourceTree = "<group>";
		};
		87436DBB437B1815631E4A38 /* Pods */ = {
			isa = PBXGroup;
			children = (
				5EAC9E06F4F75F0535E8E9C7 /* Pods-NiViewIoTDemo.debug.xcconfig */,
				5F86437B2BAB5EDC1EF6197E /* Pods-NiViewIoTDemo.release.xcconfig */,
			);
			name = Pods;
			path = ../Pods;
			sourceTree = "<group>";
		};
		C966F52803DE78CCA48733BF /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				187BA4DE2C2BEEBC00AC4A65 /* NiViewIoT.framework */,
				40C247B40861BDF2CD3E1BF4 /* Pods_NiViewIoTDemo.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		187BA4B02C2BEC1600AC4A65 /* NiViewIoTDemo */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 187BA4C52C2BEC1700AC4A65 /* Build configuration list for PBXNativeTarget "NiViewIoTDemo" */;
			buildPhases = (
				AD330B4290D08A13B671C1A9 /* [CP] Check Pods Manifest.lock */,
				187BA4AD2C2BEC1600AC4A65 /* Sources */,
				187BA4AE2C2BEC1600AC4A65 /* Frameworks */,
				187BA4AF2C2BEC1600AC4A65 /* Resources */,
				60EE1B8306BB7BF2860A360E /* [CP] Embed Pods Frameworks */,
				906ADC317C32BCEC509A6F95 /* [CP] Copy Pods Resources */,
				187BA4E12C2BEEBC00AC4A65 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = NiViewIoTDemo;
			productName = NiViewIoTDemo;
			productReference = 187BA4B12C2BEC1600AC4A65 /* NiViewIoTDemo.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		187BA4A92C2BEC1600AC4A65 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1530;
				LastUpgradeCheck = 1530;
				TargetAttributes = {
					187BA4B02C2BEC1600AC4A65 = {
						CreatedOnToolsVersion = 15.3;
					};
				};
			};
			buildConfigurationList = 187BA4AC2C2BEC1600AC4A65 /* Build configuration list for PBXProject "NiViewIoTDemo" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 187BA4A82C2BEC1600AC4A65;
			productRefGroup = 187BA4B22C2BEC1600AC4A65 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				187BA4B02C2BEC1600AC4A65 /* NiViewIoTDemo */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		187BA4AF2C2BEC1600AC4A65 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				187BA4BE2C2BEC1700AC4A65 /* Assets.xcassets in Resources */,
				187BA4C12C2BEC1700AC4A65 /* Base in Resources */,
				187BA4BC2C2BEC1600AC4A65 /* Base in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		60EE1B8306BB7BF2860A360E /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-NiViewIoTDemo/Pods-NiViewIoTDemo-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-NiViewIoTDemo/Pods-NiViewIoTDemo-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-NiViewIoTDemo/Pods-NiViewIoTDemo-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		906ADC317C32BCEC509A6F95 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-NiViewIoTDemo/Pods-NiViewIoTDemo-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-NiViewIoTDemo/Pods-NiViewIoTDemo-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-NiViewIoTDemo/Pods-NiViewIoTDemo-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		AD330B4290D08A13B671C1A9 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-NiViewIoTDemo-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		187BA4AD2C2BEC1600AC4A65 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				184DB5E92C2D590A00234BEB /* UIViewController+HUD.swift in Sources */,
				187BA4B92C2BEC1600AC4A65 /* ViewController.swift in Sources */,
				184DB5E72C2D590A00234BEB /* Environment.swift in Sources */,
				184DB5E62C2D590A00234BEB /* DevicesViewController.swift in Sources */,
				184DB5E82C2D590A00234BEB /* NVWifiTool.swift in Sources */,
				184DB5E32C2D590A00234BEB /* BindDeviceViewController.swift in Sources */,
				187BA4B52C2BEC1600AC4A65 /* AppDelegate.swift in Sources */,
				184DB5E52C2D590A00234BEB /* Device.swift in Sources */,
				184DB5E42C2D590A00234BEB /* LoginViewController.swift in Sources */,
				187BA4B72C2BEC1600AC4A65 /* SceneDelegate.swift in Sources */,
				184DB5EA2C2D590A00234BEB /* LiveViewController.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		187BA4BA2C2BEC1600AC4A65 /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				187BA4BB2C2BEC1600AC4A65 /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		187BA4BF2C2BEC1700AC4A65 /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				187BA4C02C2BEC1700AC4A65 /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		187BA4C32C2BEC1700AC4A65 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.4;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		187BA4C42C2BEC1700AC4A65 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.4;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		187BA4C62C2BEC1700AC4A65 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 5EAC9E06F4F75F0535E8E9C7 /* Pods-NiViewIoTDemo.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 8UK3729HJ2;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GENERATE_INFOPLIST_FILE = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/AFNetworking/AFNetworking.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Alamofire/Alamofire.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/CocoaAsyncSocket/CocoaAsyncSocket.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/CocoaLumberjack/CocoaLumberjack.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/CocoaMQTT/CocoaMQTT.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/CryptoSwift/CryptoSwift.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/HandyJSON/HandyJSON.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/IVDevTools/IVDevTools.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/MBProgressHUD/MBProgressHUD.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/MqttCocoaAsyncSocket/MqttCocoaAsyncSocket.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/TIoTLinkVideo/TIoTLinkVideo.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/TPCircularBuffer/TPCircularBuffer.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/YYModel/YYModel.framework/Headers\"",
					"\"${PODS_ROOT}/Headers/Public\"",
					"\"${PODS_ROOT}/Headers/Public/TIoTLinkKit_FLV\"",
					"\"${PODS_ROOT}/Headers/Public/TIoTLinkKit_XP2P\"",
					"${PODS_ROOT}/TXLiteAVSDK_TRTC/TXLiteAVSDK_TRTC/TXLiteAVSDK_TRTC.framework/Headers/",
				);
				INFOPLIST_FILE = NiViewIoTDemo/Info.plist;
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.niceviewer.NiViewIoTDemo;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		187BA4C72C2BEC1700AC4A65 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 5F86437B2BAB5EDC1EF6197E /* Pods-NiViewIoTDemo.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 8UK3729HJ2;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GENERATE_INFOPLIST_FILE = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/AFNetworking/AFNetworking.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Alamofire/Alamofire.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/CocoaAsyncSocket/CocoaAsyncSocket.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/CocoaLumberjack/CocoaLumberjack.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/CocoaMQTT/CocoaMQTT.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/CryptoSwift/CryptoSwift.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/HandyJSON/HandyJSON.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/IVDevTools/IVDevTools.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/MBProgressHUD/MBProgressHUD.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/MqttCocoaAsyncSocket/MqttCocoaAsyncSocket.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/TIoTLinkVideo/TIoTLinkVideo.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/TPCircularBuffer/TPCircularBuffer.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/YYModel/YYModel.framework/Headers\"",
					"\"${PODS_ROOT}/Headers/Public\"",
					"\"${PODS_ROOT}/Headers/Public/TIoTLinkKit_FLV\"",
					"\"${PODS_ROOT}/Headers/Public/TIoTLinkKit_XP2P\"",
					"${PODS_ROOT}/TXLiteAVSDK_TRTC/TXLiteAVSDK_TRTC/TXLiteAVSDK_TRTC.framework/Headers/",
				);
				INFOPLIST_FILE = NiViewIoTDemo/Info.plist;
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.niceviewer.NiViewIoTDemo;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		187BA4AC2C2BEC1600AC4A65 /* Build configuration list for PBXProject "NiViewIoTDemo" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				187BA4C32C2BEC1700AC4A65 /* Debug */,
				187BA4C42C2BEC1700AC4A65 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		187BA4C52C2BEC1700AC4A65 /* Build configuration list for PBXNativeTarget "NiViewIoTDemo" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				187BA4C62C2BEC1700AC4A65 /* Debug */,
				187BA4C72C2BEC1700AC4A65 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 187BA4A92C2BEC1600AC4A65 /* Project object */;
}
