//
//  AppDelegate.swift
//  NIotDemo
//
//  Created by apple on 2021/11/24.
//

import UIKit
import CryptoSwift
import NiViewIoT
//import TIoTLinkKit
//import TIoTLinkVideo

@main
class AppDelegate: UIResponder, UIApplicationDelegate {

    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bool {
        
        NIotNetworking.shared.setup(appName: "niview", appLanguage: .English, appVersion: kAppBundleVersion, secretId: "7M71I2hwj1vEmnTId6qwc4FY", secretKey: "sqMwQyJfLdnnAVBtxIFdWuGb1f5ktrqL")
        NIotManager.shared.setup(launchOptions: launchOptions)
        NIotManager.shared.debugMode = true
        NIotManager.shared.delegate = self
//        NIotNetworking.shared.update(appLanguage: .English)
        
        return true
    }

    // MARK: UISceneSession Lifecycle

    func application(_ application: UIApplication, configurationForConnecting connectingSceneSession: UISceneSession, options: UIScene.ConnectionOptions) -> UISceneConfiguration {
        // Called when a new scene session is being created.
        // Use this method to select a configuration to create the new scene with.
        return UISceneConfiguration(name: "Default Configuration", sessionRole: connectingSceneSession.role)
    }

    func application(_ application: UIApplication, didDiscardSceneSessions sceneSessions: Set<UISceneSession>) {
        // Called when the user discards a scene session.
        // If any sessions were discarded while the application was not running, this will be called shortly after application:didFinishLaunchingWithOptions.
        // Use this method to release any resources that were specific to the discarded scenes, as they will not return.
    }

    
    func application(_ application: UIApplication, didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data) {
        NIotManager.shared.registerRemoteNotifications(deviceToken)
    }
}


extension AppDelegate: NIotManagerDelegate {
    
    func didUpdate(_ linkStatus: NIotLinkStatus) {
        
    }

    func didOutputPrettyLogMessage(_ message: String) {
        
    }
    
}
