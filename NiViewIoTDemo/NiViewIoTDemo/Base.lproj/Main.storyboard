<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="32700.99.1234" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES" initialViewController="tC7-B3-BOg">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22684"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--Navigation Controller-->
        <scene sceneID="ueh-Xc-0R0">
            <objects>
                <navigationController automaticallyAdjustsScrollViewInsets="NO" id="tC7-B3-BOg" sceneMemberID="viewController">
                    <toolbarItems/>
                    <navigationBar key="navigationBar" contentMode="scaleToFill" id="6XV-7R-tQk">
                        <rect key="frame" x="0.0" y="59" width="393" height="44"/>
                        <autoresizingMask key="autoresizingMask"/>
                    </navigationBar>
                    <nil name="viewControllers"/>
                    <connections>
                        <segue destination="QsR-EB-j8K" kind="relationship" relationship="rootViewController" id="Lla-4d-hWb"/>
                    </connections>
                </navigationController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="Ssb-rr-3CY" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="124.6376811594203" y="83.705357142857139"/>
        </scene>
        <!--NIotDemo-->
        <scene sceneID="6PD-U7-xWd">
            <objects>
                <viewController storyboardIdentifier="loginVc" id="QsR-EB-j8K" customClass="LoginViewController" customModule="NIotDemo" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="HhQ-di-4Mj">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" text="<EMAIL>" borderStyle="roundedRect" placeholder="email" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="ORo-bT-fLP">
                                <rect key="frame" x="20" y="128" width="353" height="40"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="40" id="RBX-fC-NEE"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                <textInputTraits key="textInputTraits" returnKeyType="next"/>
                            </textField>
                            <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" borderStyle="roundedRect" placeholder="verify code" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="Odi-Q9-GmS">
                                <rect key="frame" x="20" y="193" width="213" height="40"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="40" id="OC3-sY-eGu"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                <textInputTraits key="textInputTraits"/>
                            </textField>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="characterWrap" translatesAutoresizingMaskIntoConstraints="NO" id="Bfi-6b-S0B">
                                <rect key="frame" x="253" y="193" width="120" height="40"/>
                                <color key="backgroundColor" systemColor="systemBlueColor"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="120" id="b94-Nc-BAC"/>
                                    <constraint firstAttribute="height" constant="40" id="y6N-pc-7Qu"/>
                                </constraints>
                                <buttonConfiguration key="configuration" style="plain" title="Send Verify">
                                    <color key="baseForegroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                </buttonConfiguration>
                                <connections>
                                    <action selector="sendVerifyCodeAction:" destination="QsR-EB-j8K" eventType="touchUpInside" id="uGd-wK-egr"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="wordWrap" translatesAutoresizingMaskIntoConstraints="NO" id="m99-hA-41g">
                                <rect key="frame" x="121.66666666666669" y="263" width="150" height="40"/>
                                <color key="backgroundColor" systemColor="systemBlueColor"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="40" id="3jC-yg-Q8E"/>
                                    <constraint firstAttribute="width" constant="150" id="eQU-dl-sSB"/>
                                </constraints>
                                <state key="normal" title="Button"/>
                                <buttonConfiguration key="configuration" style="plain" title="Register/Login">
                                    <color key="baseForegroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                </buttonConfiguration>
                                <connections>
                                    <action selector="loginOrRegisterAction:" destination="QsR-EB-j8K" eventType="touchUpInside" id="lzU-H4-4uL"/>
                                </connections>
                            </button>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="uyS-gG-piY"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="Odi-Q9-GmS" firstAttribute="leading" secondItem="uyS-gG-piY" secondAttribute="leading" constant="20" id="Bwa-9h-qOx"/>
                            <constraint firstItem="Bfi-6b-S0B" firstAttribute="leading" secondItem="Odi-Q9-GmS" secondAttribute="trailing" constant="20" id="C86-5D-r7E"/>
                            <constraint firstItem="Bfi-6b-S0B" firstAttribute="top" secondItem="ORo-bT-fLP" secondAttribute="bottom" constant="25" id="I7z-2Z-Kn8"/>
                            <constraint firstItem="m99-hA-41g" firstAttribute="centerX" secondItem="HhQ-di-4Mj" secondAttribute="centerX" id="Upu-At-Zsz"/>
                            <constraint firstItem="Odi-Q9-GmS" firstAttribute="centerY" secondItem="Bfi-6b-S0B" secondAttribute="centerY" id="dKs-uN-YUa"/>
                            <constraint firstItem="uyS-gG-piY" firstAttribute="trailing" secondItem="ORo-bT-fLP" secondAttribute="trailing" constant="20" id="hy6-Pv-D0I"/>
                            <constraint firstItem="ORo-bT-fLP" firstAttribute="leading" secondItem="uyS-gG-piY" secondAttribute="leading" constant="20" id="ihZ-J5-VeV"/>
                            <constraint firstItem="uyS-gG-piY" firstAttribute="trailing" secondItem="Bfi-6b-S0B" secondAttribute="trailing" constant="20" id="kvh-eY-mpA"/>
                            <constraint firstItem="ORo-bT-fLP" firstAttribute="top" secondItem="uyS-gG-piY" secondAttribute="top" constant="25" id="oma-KJ-M4y"/>
                            <constraint firstItem="m99-hA-41g" firstAttribute="top" secondItem="Odi-Q9-GmS" secondAttribute="bottom" constant="30" id="pRQ-oy-6II"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" title="NIotDemo" id="DHa-kt-aCa"/>
                    <connections>
                        <outlet property="codeTF" destination="Odi-Q9-GmS" id="mzR-BJ-9rv"/>
                        <outlet property="emailTF" destination="ORo-bT-fLP" id="zJx-lD-55W"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="K2p-Po-rAd" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1034.7826086956522" y="83.705357142857139"/>
        </scene>
        <!--Devices-->
        <scene sceneID="cDw-zO-a36">
            <objects>
                <viewController storyboardIdentifier="devicesVc" id="gQr-RU-I9O" customClass="DevicesViewController" customModule="NIotDemo" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="a1D-wO-hOV">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="D3L-FU-ust">
                                <rect key="frame" x="139.33333333333334" y="733.66666666666663" width="114.33333333333334" height="34.333333333333371"/>
                                <color key="backgroundColor" systemColor="systemBlueColor"/>
                                <state key="normal" title="Button"/>
                                <buttonConfiguration key="configuration" style="plain" title="Bind Device">
                                    <color key="baseForegroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                </buttonConfiguration>
                                <connections>
                                    <segue destination="OQ0-cz-w46" kind="show" id="K5y-3q-eB8"/>
                                </connections>
                            </button>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" separatorStyle="default" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="-1" estimatedSectionHeaderHeight="-1" sectionFooterHeight="-1" estimatedSectionFooterHeight="-1" translatesAutoresizingMaskIntoConstraints="NO" id="rpF-Zu-VyQ">
                                <rect key="frame" x="0.0" y="59" width="393" height="674.66666666666663"/>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <connections>
                                    <outlet property="dataSource" destination="gQr-RU-I9O" id="GbU-F6-o8P"/>
                                    <outlet property="delegate" destination="gQr-RU-I9O" id="0NO-Uo-par"/>
                                </connections>
                            </tableView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="oDQ-NU-hGX"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="rpF-Zu-VyQ" firstAttribute="top" secondItem="oDQ-NU-hGX" secondAttribute="top" id="GYV-Id-FMI"/>
                            <constraint firstItem="rpF-Zu-VyQ" firstAttribute="leading" secondItem="oDQ-NU-hGX" secondAttribute="leading" id="TYR-vP-pMa"/>
                            <constraint firstItem="oDQ-NU-hGX" firstAttribute="bottom" secondItem="D3L-FU-ust" secondAttribute="bottom" constant="50" id="cwk-Ja-l9G"/>
                            <constraint firstItem="D3L-FU-ust" firstAttribute="top" secondItem="rpF-Zu-VyQ" secondAttribute="bottom" id="eBi-TO-Zj8"/>
                            <constraint firstItem="D3L-FU-ust" firstAttribute="centerX" secondItem="a1D-wO-hOV" secondAttribute="centerX" id="hNR-iH-Uqd"/>
                            <constraint firstItem="oDQ-NU-hGX" firstAttribute="trailing" secondItem="rpF-Zu-VyQ" secondAttribute="trailing" id="lYr-Lv-VsB"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" title="Devices" id="xKq-uN-s7a">
                        <barButtonItem key="rightBarButtonItem" title="Logout" id="UAK-lR-KB3">
                            <connections>
                                <action selector="logoutAction:" destination="gQr-RU-I9O" id="UoQ-Wa-xlh"/>
                            </connections>
                        </barButtonItem>
                    </navigationItem>
                    <connections>
                        <outlet property="tableView" destination="rpF-Zu-VyQ" id="0iJ-fj-5ok"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="YDb-II-rxB" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1891" y="88"/>
        </scene>
        <!--Bind Device-->
        <scene sceneID="kg0-4f-NuU">
            <objects>
                <viewController id="OQ0-cz-w46" customClass="BindDeviceViewController" customModule="NIotDemo" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="fnw-RR-cm6">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="842"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="BdQ-c6-918">
                                <rect key="frame" x="115.33333333333333" y="145" width="162.66666666666669" height="168.33333333333337"/>
                                <color key="backgroundColor" systemColor="systemBlueColor"/>
                                <state key="normal" title="Button"/>
                                <buttonConfiguration key="configuration" style="plain" title="Generate QRCode">
                                    <color key="baseForegroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                </buttonConfiguration>
                                <connections>
                                    <action selector="generateQRCodeAction:" destination="OQ0-cz-w46" eventType="touchUpInside" id="5u8-Oi-Gus"/>
                                </connections>
                            </button>
                            <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" borderStyle="roundedRect" placeholder="WiFi Name" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="5z3-bz-Ue8">
                                <rect key="frame" x="20" y="25" width="353" height="40"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="40" id="XaK-b5-kht"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                <textInputTraits key="textInputTraits" returnKeyType="next"/>
                            </textField>
                            <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" borderStyle="roundedRect" placeholder="WiFi Password" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="yls-Oj-cjE">
                                <rect key="frame" x="20" y="85" width="353" height="40"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="40" id="juE-VX-IRn"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                <textInputTraits key="textInputTraits"/>
                            </textField>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="pHZ-Bd-f1f">
                                <rect key="frame" x="90.000000000000014" y="757.66666666666663" width="213.33333333333337" height="34.333333333333371"/>
                                <color key="backgroundColor" systemColor="systemBlueColor"/>
                                <state key="normal" title="Button"/>
                                <buttonConfiguration key="configuration" style="plain" title="Heard the device prompt">
                                    <color key="baseForegroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                </buttonConfiguration>
                                <connections>
                                    <action selector="connectDeviceAction:" destination="OQ0-cz-w46" eventType="touchUpInside" id="iZc-5k-yTQ"/>
                                </connections>
                            </button>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="10" translatesAutoresizingMaskIntoConstraints="NO" id="CMH-a0-GVQ">
                                <rect key="frame" x="50" y="333.33333333333337" width="293" height="404.33333333333337"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Please aim the QR code at 20cm-30cm of the device lens and wait for the device to scan the code" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="cmf-fz-uiC">
                                        <rect key="frame" x="0.0" y="0.0" width="293" height="61"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                        <nil key="textColor"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="Wyw-zQ-4Qa">
                                        <rect key="frame" x="0.0" y="71" width="293" height="303"/>
                                        <color key="backgroundColor" white="0.66666666669999997" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    </imageView>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="👇🏻" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="3e5-uy-hJV">
                                        <rect key="frame" x="0.0" y="384.00000000000006" width="293" height="20.333333333333314"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                        <nil key="textColor"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                            </stackView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="4b1-CL-wDm"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="yls-Oj-cjE" firstAttribute="leading" secondItem="4b1-CL-wDm" secondAttribute="leading" constant="20" id="0da-yR-HNU"/>
                            <constraint firstItem="5z3-bz-Ue8" firstAttribute="top" secondItem="4b1-CL-wDm" secondAttribute="top" constant="25" id="2pg-hb-Qbc"/>
                            <constraint firstItem="4b1-CL-wDm" firstAttribute="trailing" secondItem="yls-Oj-cjE" secondAttribute="trailing" constant="20" id="8ua-wi-QXb"/>
                            <constraint firstItem="pHZ-Bd-f1f" firstAttribute="centerX" secondItem="fnw-RR-cm6" secondAttribute="centerX" id="99Z-aG-YyU"/>
                            <constraint firstItem="4b1-CL-wDm" firstAttribute="bottom" secondItem="pHZ-Bd-f1f" secondAttribute="bottom" constant="50" id="BIV-dm-3qU"/>
                            <constraint firstAttribute="trailing" secondItem="CMH-a0-GVQ" secondAttribute="trailing" constant="50" id="JWh-5U-mjy"/>
                            <constraint firstItem="yls-Oj-cjE" firstAttribute="top" secondItem="5z3-bz-Ue8" secondAttribute="bottom" constant="20" id="WSw-RP-Gpa"/>
                            <constraint firstItem="4b1-CL-wDm" firstAttribute="trailing" secondItem="5z3-bz-Ue8" secondAttribute="trailing" constant="20" id="b2X-r9-HLa"/>
                            <constraint firstItem="5z3-bz-Ue8" firstAttribute="leading" secondItem="4b1-CL-wDm" secondAttribute="leading" constant="20" id="dhX-J5-zYI"/>
                            <constraint firstItem="pHZ-Bd-f1f" firstAttribute="top" secondItem="CMH-a0-GVQ" secondAttribute="bottom" constant="20" id="e8A-WG-2Wa"/>
                            <constraint firstItem="BdQ-c6-918" firstAttribute="centerX" secondItem="fnw-RR-cm6" secondAttribute="centerX" id="fwE-OV-Dg3"/>
                            <constraint firstItem="CMH-a0-GVQ" firstAttribute="top" secondItem="BdQ-c6-918" secondAttribute="bottom" constant="20" id="r8M-Ri-Z7v"/>
                            <constraint firstItem="BdQ-c6-918" firstAttribute="top" secondItem="yls-Oj-cjE" secondAttribute="bottom" constant="20" id="tic-fy-W46"/>
                            <constraint firstItem="CMH-a0-GVQ" firstAttribute="leading" secondItem="4b1-CL-wDm" secondAttribute="leading" constant="50" id="zMV-TG-bjj"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" title="Bind Device" id="TXa-ok-WDT"/>
                    <connections>
                        <outlet property="QRCodeImageView" destination="Wyw-zQ-4Qa" id="fF8-FL-d0o"/>
                        <outlet property="nameTF" destination="5z3-bz-Ue8" id="c2o-OT-VjQ"/>
                        <outlet property="pwdTF" destination="yls-Oj-cjE" id="MHz-3s-uDR"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="qOl-Mw-Hqd" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1890" y="856"/>
        </scene>
        <!--Live View Controller-->
        <scene sceneID="Ysi-dW-NQL">
            <objects>
                <viewController storyboardIdentifier="liveVc" id="54u-4c-NH3" customClass="LiveViewController" customModule="NIotDemo" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="KuQ-La-jca">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="10" translatesAutoresizingMaskIntoConstraints="NO" id="qv0-sC-vMq">
                                <rect key="frame" x="20" y="733.66666666666663" width="353" height="34.333333333333371"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="omf-hy-JT5">
                                        <rect key="frame" x="0.0" y="0.0" width="111" height="34.333333333333336"/>
                                        <color key="backgroundColor" systemColor="systemBlueColor"/>
                                        <state key="normal" title="Button"/>
                                        <buttonConfiguration key="configuration" style="plain" title="Play">
                                            <color key="baseForegroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        </buttonConfiguration>
                                        <connections>
                                            <action selector="connectDeviceAction:" destination="OQ0-cz-w46" eventType="touchUpInside" id="6ow-ik-SUc"/>
                                            <action selector="playAction:" destination="54u-4c-NH3" eventType="touchUpInside" id="krA-il-NbZ"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="H2E-0r-jgG">
                                        <rect key="frame" x="121" y="0.0" width="111" height="34.333333333333336"/>
                                        <color key="backgroundColor" systemColor="systemBlueColor"/>
                                        <state key="normal" title="Button"/>
                                        <buttonConfiguration key="configuration" style="plain" title="Stop">
                                            <color key="baseForegroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        </buttonConfiguration>
                                        <connections>
                                            <action selector="connectDeviceAction:" destination="OQ0-cz-w46" eventType="touchUpInside" id="rXK-8Z-bds"/>
                                            <action selector="stopAction:" destination="54u-4c-NH3" eventType="touchUpInside" id="hju-fu-7mO"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="TeV-jG-y5l">
                                        <rect key="frame" x="242" y="0.0" width="111" height="34.333333333333336"/>
                                        <color key="backgroundColor" systemColor="systemBlueColor"/>
                                        <state key="normal" title="Button"/>
                                        <buttonConfiguration key="configuration" style="plain" title="Intercom">
                                            <color key="baseForegroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        </buttonConfiguration>
                                        <connections>
                                            <action selector="connectDeviceAction:" destination="OQ0-cz-w46" eventType="touchUpInside" id="vV2-iy-Qkd"/>
                                            <action selector="intercomAction:" destination="54u-4c-NH3" eventType="touchUpInside" id="65V-gy-D60"/>
                                        </connections>
                                    </button>
                                </subviews>
                            </stackView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="wDq-hK-eMh"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="qv0-sC-vMq" firstAttribute="leading" secondItem="KuQ-La-jca" secondAttribute="leading" constant="20" id="HlJ-00-WcD"/>
                            <constraint firstAttribute="trailing" secondItem="qv0-sC-vMq" secondAttribute="trailing" constant="20" id="a4V-hU-Tkr"/>
                            <constraint firstItem="wDq-hK-eMh" firstAttribute="bottom" secondItem="qv0-sC-vMq" secondAttribute="bottom" constant="50" id="kTJ-aT-DKM"/>
                            <constraint firstItem="qv0-sC-vMq" firstAttribute="centerX" secondItem="KuQ-La-jca" secondAttribute="centerX" id="kaR-RS-NCL"/>
                        </constraints>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="LbT-24-r56" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="2665" y="88"/>
        </scene>
    </scenes>
    <resources>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
        <systemColor name="systemBlueColor">
            <color red="0.0" green="0.47843137254901963" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
    </resources>
</document>
