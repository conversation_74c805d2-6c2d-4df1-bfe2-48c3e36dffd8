//
//  BindDeviceViewController.swift
//  NIotDemo
//
//  Created by apple on 2021/11/24.
//

import UIKit
import NiViewIoT

class BindDeviceViewController: UIViewController {

    @IBOutlet weak var nameTF: UITextField!
    @IBOutlet weak var pwdTF: UITextField!
    @IBOutlet weak var QRCodeImageView: UIImageView!
    
    private let timestamp = Int64(round(Date().timeIntervalSince1970*1000))
    
    override func viewDidLoad() {
        super.viewDidLoad()
        nameTF.text = NVWifiTool.currentSSID 
        
        // test
        nameTF.text = "NiceOffice"
        pwdTF.text = "Nice111111"
    }
    
 
    @IBAction func generateQRCodeAction(_ sender: Any) {
        guard let name = nameTF.text, !name.isEmpty,
                let pwd = pwdTF.text, !pwd.isEmpty else {
            showError("invalid wifi name or password")
            return
        }
        guard let uid = Environment.shared.uid else {
            assertionFailure("invalid uid")
            return
        }
        nameTF.resignFirstResponder()
        pwdTF.resignFirstResponder()
        
        let hud = showLoading()
        let qrCodeImage = NIotNetworking.shared.getQrCodeBitmap(uid: uid, timestamp: timestamp, wifiName: name, wifiPassword: pwd, imgSize: QRCodeImageView.frame.size)
        self.QRCodeImageView.image = qrCodeImage

        hud.hide()
    }
    
    @IBAction func connectDeviceAction(_ sender: Any) {
        guard let uid = Environment.shared.uid else {
            assertionFailure("invalid uid")
            return
        }
        let hud = showLoading()
        NIotNetworking.shared.bindDevice(config: DeviceBindingConfig(uid: String(uid), time: String(timestamp))) { json, error in
            hud.hide()
            guard error == nil else {
                self.showError("request failed: \(error!)")
                return
            }
            self.navigationController?.popViewController(animated: true)
        }
    }
}
