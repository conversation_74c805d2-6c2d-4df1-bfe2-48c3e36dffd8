//
//  DevicesViewController.swift
//  NIotDemo
//
//  Created by apple on 2021/11/24.
//

import UIKit
import HandyJSON
import NiViewIoT

struct Property {
    static let online = "ProReadonly._online"
    // ...
}

class DevicesViewController: UIViewController {
     
    @IBOutlet weak var tableView: UITableView!
    
    private var devices: [NVDeviceModel] = []
    
    override func viewDidLoad() {
        super.viewDidLoad() 
        
        NotificationCenter.default.addObserver(forName: .NIot_DevicePropertyDidUpdate, object: nil, queue: nil) { noti in
            guard let userInfo = noti.userInfo, let json = userInfo["json"], let path = userInfo["path"], let deviceId = userInfo["deviceId"] else { return }
            print("📱 didUpdateProperty -> json: \(json), path: \(path), deviceId: \(deviceId)")
        }
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        getDevicesList { [weak self] devices in
            guard let self = self else { return }
            self.devices = devices
            self.tableView.reloadData()
            self.updateDeviceStatus()
        }
    }
    
    @IBAction func logoutAction(_ sender: Any) {
        guard let uid = Environment.shared.uid else {
            assertionFailure("invalid uid")
            return
        }

        NetworkManager.shared.logout(uid: uid, from: self) { success in
            let sb = UIStoryboard.init(name: "Main", bundle: nil)
            let loginVc = sb.instantiateViewController(withIdentifier: "loginVc")
            UIApplication.shared.keyWindow?.rootViewController = loginVc
            // remove token
            Environment.shared.update(token: "")
        }
    }
}



extension DevicesViewController: UITableViewDelegate, UITableViewDataSource {
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return devices.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        var cell = tableView.dequeueReusableCell(withIdentifier: "cell")
        if cell == nil {
            cell = UITableViewCell.init(style: .subtitle, reuseIdentifier: "cell")
            let label = UILabel()
            label.frame = CGRect(x: 0, y: 0, width: 100, height: 30)
            label.textAlignment = .right
            cell?.accessoryView = label
        }
        let device = devices[indexPath.row]
        cell!.textLabel?.text = "Name: \(device.device?.devName ?? "")"
        cell!.detailTextLabel?.text = "ID: \(device.device?.id ?? 0)"
        let onlineStatusLabel = cell!.accessoryView as! UILabel
//        onlineStatusLabel.text = device.status?.presentText
        return cell!
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        let device = devices[indexPath.row]
        let sb = UIStoryboard.init(name: "Main", bundle: nil)
        let vc = sb.instantiateViewController(withIdentifier: "liveVc") as! LiveViewController
        vc.deviceModel = device
        self.navigationController?.pushViewController(vc, animated: true)
    }
    
    func tableView(_ tableView: UITableView, trailingSwipeActionsConfigurationForRowAt indexPath: IndexPath) -> UISwipeActionsConfiguration? {
        let action = UIContextualAction.init(style: .destructive, title: "delete") { action, view, completionHandler in
            
            let alert = UIAlertController.init(title: "Delete Device", message: "Are you sure to delete this device?", preferredStyle: .alert)
            
            alert.addAction(UIAlertAction.init(title: "Cancel", style: .default, handler: { _ in
                completionHandler(false)
            }))

            alert.addAction(UIAlertAction.init(title: "Delete", style: .destructive, handler: { _ in
                // delete devices
                let device = self.devices[indexPath.row]
                self.deleteDevice(device) { [weak self] result in
                    guard let self = self else { return }
                    self.devices.remove(at: indexPath.row)
                    self.tableView.reloadData()
                }
            }))
            self.present(alert, animated: true, completion: nil)
        }
        return UISwipeActionsConfiguration.init(actions: [action])
    }
}

extension DevicesViewController {
    private func parseJson(_ json: String?) -> [String: Any]? {
        
        guard let jsonData = json?.data(using: .utf8),
              let dict = try? JSONSerialization.jsonObject(with: jsonData, options: .fragmentsAllowed) as? [String: Any] else {
            return nil
        }
        return dict
    }
    
}

private extension DevicesViewController {
    
    func getDevicesList(_ completionHandler: @escaping ([NVDeviceModel]) -> Void) {
        guard let uid = Environment.shared.uid else {
            assertionFailure("invalid uid")
            return
        }
        NIotNetworking.shared.getDeviceList(uid: uid) { json, error in
            var deviceArr = [NVDeviceModel]()
            if let devices = json as? [Any] {
                for i in 0..<devices.count {
                    if let nsDict = devices[i] as? NSDictionary,
                       let model = NVDeviceModel.deserialize(from: nsDict) {
                        deviceArr.append(model)
                    }
                }
            }
            completionHandler(json?.compactMap{$0} ?? [])
            
        }
    }
    
    func deleteDevice(_ device: NVDeviceModel, completionHandler: @escaping (Bool) -> Void) {
        guard let uid = Environment.shared.uid,
              let accessId = Environment.shared.accessId,
              let d = device.device else {
            assertionFailure("invalid params")
            return
        }

        NetworkManager.shared.unbindDevice(
            uid: uid,
            accessId: accessId,
            deviceModel: device,
            from: self
        ) { success in
            completionHandler(success)
        }
    }
    
    func updateDeviceStatus() {
        for (index, device) in devices.enumerated() {
            guard let tid = device.device?.tid else {
                continue
            }
//            device.service.getOnlineStatus(ofDevice: tid) { status, error in
//                
//            }
        }
    }
}
