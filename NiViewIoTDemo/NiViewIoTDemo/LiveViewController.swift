//
//  LiveViewController.swift
//  NIotDemo
//
//  Created by apple on 2021/11/24.
//

import UIKit
import NiViewIoT
import MBProgressHUD

class LiveViewController: UIViewController {
    
    var deviceModel: NVDeviceModel?

    private var _player: NIotMonitorPlayer?
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        DispatchQueue.main.async {
            if let deviceModel = self.deviceModel, let uid = Environment.shared.uid {
                self._player = NIotMonitorPlayer(deviceModel: deviceModel, uid: uid)
            }
            self.setupPlayerView()
        }
        
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        _player?.stopTalking()
        _player?.stop()
    }
    
    private func setupPlayerView() {
        _player?.delegate = self
        guard let videoView = _player?.videoView else { return }
        videoView.backgroundColor = .gray
        view.addSubview(videoView)
        videoView.frame = CGRect(x: 0, y: 100, width: UIScreen.main.bounds.width, height: UIScreen.main.bounds.width*3/4)
        videoView.autoresizingMask = [.flexibleWidth, .flexibleHeight]
    }

    
    @IBAction func playAction(_ sender: Any) {
        _player?.play()
    }
    
    @IBAction func stopAction(_ sender: Any) {
        _player?.stop()
    }
    
    @IBAction func intercomAction(_ sender: UIButton) {
        let isTalking = sender.isSelected
        if isTalking {
            _player?.stopTalking { [weak self] error in
                guard let self = self else { return }
                if let error = error {
                    self.showError(error.localizedDescription)
                }
            }
        } else {
            _player?.startTalking { [weak self] error in
                guard let self = self else { return }
                if let error = error {
                    self.showError(error.localizedDescription)
                }
            }
        }
    }
}

extension LiveViewController: NIotPlayerDelegate, NIotConnectionDelegate {
    func player(_ player: NIotPlayer, didUpdateStatus status: NIotPlayerStatus) {
        print("⭐️ -player:didUpdateStatus: \(status.rawValue)")
    }
    
    func player(_ player: NIotPlayer, didReceiveError error: Error) {
        print("⭐️ -player:didReceiveError: \(error.localizedDescription)")
    }
    
    func connection(_ connection: NIotConnection, didUpdateStatus status: NIotConnStatus) {
        print("⭐️ -connection:didUpdateStatus: \(status.rawValue)")
    }
    
    func connection(_ connection: NIotConnection, didReceiveError error: Error) {
        print("⭐️ -connection:didReceiveError: \(error.localizedDescription)")
    }
}
