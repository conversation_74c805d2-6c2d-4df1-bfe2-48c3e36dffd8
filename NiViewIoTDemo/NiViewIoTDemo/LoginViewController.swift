//
//  LoginViewController.swift
//  NIotDemo
//
//  Created by apple on 2021/11/24.
//

import UIKit
import NiViewIoT

class LoginViewController: UIViewController {

    @IBOutlet weak var emailTF: UITextField!
    @IBOutlet weak var codeTF: UITextField!
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        DispatchQueue.main.asyncAfter(deadline: .now()+0.5) {
//            self.autoLoginWithTokenIfPossible()
        }
        let hud = showLoading("auto login with token...")
        NIotNetworking.shared.login(phone: "19925314639", password: "qqqqqqqq") { json, error in
            hud.hide()
            guard error == nil else {
                self.showMsg("request failed: \(error!)")
                return
            }
            self.loginSucceed(json)
        }
    }
    
    func autoLoginWithTokenIfPossible() {
        guard let token = Environment.shared.token, !token.isEmpty else {
            return
        }
        let hud = showLoading("auto login with token...")
        NIotNetworking.shared.loginWithToken { json, error in
            hud.hide()
            guard error == nil else {
                self.showMsg("request failed: \(error!)")
                return
            }
            self.loginSucceed(json)
        }
    }

    @IBAction func sendVerifyCodeAction(_ sender: Any) {
        guard let email = emailTF.text, !email.isEmpty else {
            showMsg("invalid email")
            return
        }

        // 方案1: 使用 NetworkManager
        // NetworkManager.shared.sendVerifyCode(email: email, from: self) { success in
        //     // 成功回调已经在 NetworkManager 中处理了 HUD 显示
        // }

        // 方案2: 使用响应式网络管理器 - 链式调用
        ReactiveNetworkManager.shared
            .sendVerifyCode(email: email, from: self)
            .onSuccess { _ in
                self.showMsg("Verification code sent successfully")
            }
    }
    
    @IBAction func loginOrRegisterAction(_ sender: Any) {
        guard let email = emailTF.text, !email.isEmpty,
                let code = codeTF.text, code.count == 6 else {
            showMsg("invalid email or code")
            return
        }

        // 方案1: 使用 NetworkManager
        // NetworkManager.shared.login(email: email, verifyCode: code, from: self) { userData in
        //     self.loginSucceed(userData as AnyObject)
        // }

        // 方案2: 使用响应式网络管理器 - 支持链式调用
        ReactiveNetworkManager.shared
            .login(email: email, verifyCode: code, from: self)
            .onSuccess { userData in
                self.loginSucceed(userData)

                // 登录成功后可以继续链式调用其他接口
                guard let uid = Environment.shared.uid else { return }
                ReactiveNetworkManager.shared
                    .getDeviceList(uid: uid, from: self, silently: true)
                    .onSuccess { devices in
                        print("Auto loaded \(devices.count) devices after login")
                    }
            }
    }
    
    private func loginSucceed(_ json: AnyObject?) {
        if let json = json {
            Environment.shared.update(userData: json as! [String : Any])
        }
        let sb = UIStoryboard.init(name: "Main", bundle: nil)
        let devicesVc = sb.instantiateViewController(withIdentifier: "devicesVc")
        let nav = UINavigationController(rootViewController: devicesVc)
        UIApplication.shared.keyWindow?.rootViewController = nav
        
        registerIotManager()
        putPushToken()
        
    }
    
    private func registerIotManager() {
        guard let accessId = Environment.shared.accessId,
                let accessToken = Environment.shared.accessToken else {
            assertionFailure("invalid accessId or accessToken")
            return
        }
        NIotManager.shared.register(accessId: String(accessId), accessToken: accessToken)
    }
    
    private func putPushToken() {
        guard let uid = Environment.shared.uid,
              let token  = Environment.shared.devicePushToken else {
            return
        }
        NIotNetworking.shared.putPushToken(uid: uid, pushToken: token) { json, error in
            
        }
    }

}

 
