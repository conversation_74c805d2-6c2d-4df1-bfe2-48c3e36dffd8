//
//  LoginViewController.swift
//  NIotDemo
//
//  Created by apple on 2021/11/24.
//

import UIKit
import NiViewIoT

class LoginViewController: UIViewController {

    @IBOutlet weak var emailTF: UITextField!
    @IBOutlet weak var codeTF: UITextField!
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        DispatchQueue.main.asyncAfter(deadline: .now()+0.5) {
//            self.autoLoginWithTokenIfPossible()
        }
        let hud = showLoading("auto login with token...")
        NIotNetworking.shared.login(phone: "19925314639", password: "qqqqqqqq") { json, error in
            hud.hide()
            guard error == nil else {
                self.showMsg("request failed: \(error!)")
                return
            }
            self.loginSucceed(json)
        }
    }
    
    func autoLoginWithTokenIfPossible() {
        guard let token = Environment.shared.token, !token.isEmpty else {
            return
        }
        let hud = showLoading("auto login with token...")
        NIotNetworking.shared.loginWithToken { json, error in
            hud.hide()
            guard error == nil else {
                self.showMsg("request failed: \(error!)")
                return
            }
            self.loginSucceed(json)
        }
    }

    @IBAction func sendVerifyCodeAction(_ sender: Any) {
        guard let email = emailTF.text, !email.isEmpty else {
            showMsg("invalid email")
            return
        }
        let hud = showLoading()
        NIotNetworking.shared.sendVerifyCodeFor(email: email) { json, error in
            hud.hide()
            guard error == nil else {
                self.showMsg("request failed: \(error!)")
                return
            }
            self.showMsg("sendVerifyCode success")
        }
    }
    
    @IBAction func loginOrRegisterAction(_ sender: Any) {
        guard let email = emailTF.text, !email.isEmpty,
                let code = codeTF.text, code.count == 6 else {
            showMsg("invalid email or code")
            return
        }
        let hud = showLoading()
        NIotNetworking.shared.login(email: email, verifyCode: code) { json, error in
            hud.hide()
            guard error == nil else {
                self.showMsg("request failed: \(error!)")
                return
            }
            self.loginSucceed(json)
        }
    }
    
    private func loginSucceed(_ json: AnyObject?) {
        if let json = json {
            Environment.shared.update(userData: json as! [String : Any])
        }
        let sb = UIStoryboard.init(name: "Main", bundle: nil)
        let devicesVc = sb.instantiateViewController(withIdentifier: "devicesVc")
        let nav = UINavigationController(rootViewController: devicesVc)
        UIApplication.shared.keyWindow?.rootViewController = nav
        
        registerIotManager()
        putPushToken()
        
    }
    
    private func registerIotManager() {
        guard let accessId = Environment.shared.accessId,
                let accessToken = Environment.shared.accessToken else {
            assertionFailure("invalid accessId or accessToken")
            return
        }
        NIotManager.shared.register(accessId: String(accessId), accessToken: accessToken)
    }
    
    private func putPushToken() {
        guard let uid = Environment.shared.uid,
              let token  = Environment.shared.devicePushToken else {
            return
        }
        NIotNetworking.shared.putPushToken(uid: uid, pushToken: token) { json, error in
            
        }
    }

}

 
