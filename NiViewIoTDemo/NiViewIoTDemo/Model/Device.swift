//
//  DeviceInfo.swift
//  NIotDemo
//
//  Created by apple on 2021/11/25.
//

import UIKit

// 设备状态
enum DeviceStatus: Int, Codable {
    case offline, online, dormant
    
    var presentText: String {
        switch self {
        case .offline: return "offline"
        case .online: return "online"
        case .dormant: return "dormant"
        }
    }
}

class DeviceModel: Codable {
    // bind id
    var id: Int64?
    var role: Int64?
    
    var device: DeviceInfo?
    
    var did: Int64?
    var uid: Int64?
    
    
    // for UI
    var status: DeviceStatus?
}

class DeviceInfo: Codable {
    var devName: String?
    var devType: String?
    var freeCloud: Int64?
    var id: Int64?
    var lastCloudEndTime: Int64?
    var logPush: Bool?
    var nid: Int64?
    var tid: String?
}
