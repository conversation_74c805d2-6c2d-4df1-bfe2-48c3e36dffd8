//
//  Environment.swift
//  NIotDemo
//
//  Created by apple on 2021/11/24.
//

import UIKit

 
class Environment {
    
    static let shared = Environment()
      
    private var _userData = [String: Any]()
    
    private(set) var token: String?
    var devicePushToken: String?
    
    
    init() {
        if let token = UserDefaults.standard.string(forKey: "NIotDemo-token") {
            self.token = token
        }
    }
    
    var uid: Int64? { _userData["id"] as? Int64 }
    var accessId: Int64? { _userData["tAccessId"] as? Int64 }
    var accessToken: String? { _userData["tAccessToken"] as? String }
     
    func update(userData: [String: Any]) {
        _userData = userData
        if let token = _userData["token"] as? String {
            update(token: token)
        }
    }
    
    func update(token: String) {
        self.token = token
        self.persistToken()
    }
    
    private func persistToken() {
        UserDefaults.standard.set(token, forKey: "NIotDemo-token")
        UserDefaults.standard.synchronize()
    }
     
    
}
