//
//  NVWifiTool.swift
//  NiView
//
//  Created by nice on 2020/6/23.
//  Copyright © 2020 nice. All rights reserved.
//

import UIKit
import SystemConfiguration.CaptiveNetwork

struct NVWifiTool {
    private static let kWifiList = "niview_wifi_list"
    
    static func isSameNetwork(_ IP1: String?, _ IP2: String?) -> Bool {
        guard let IP1 = IP1, let IP2 = IP2 else { return false }
        let ip1 = IP1.split(separator: ".").prefix(upTo: 3)
        let ip2 = IP2.split(separator: ".").prefix(upTo: 3)
        return ip1 == ip2
    }
    
    static var currentSSID: String? {
        guard let wifiInterfaces = CNCopySupportedInterfaces() else { return nil }
        if let interfaceArr = CFBridgingRetain(wifiInterfaces) as? Array<String>, interfaceArr.count > 0 {
            let interfaceName = interfaceArr[0] as CFString
            let ussafeInterfaceData = CNCopyCurrentNetworkInfo(interfaceName)
            if (ussafeInterfaceData != nil) {
                let interfaceData = ussafeInterfaceData as! Dictionary<String, Any>
                return interfaceData["SSID"] as? String
            }
        }
        return nil
    }
    
    /// 前往Wi-Fi设置页面
    static func gotoSettings() {
        let urlStr:String = UIApplication.openSettingsURLString
        let url = NSURL.init(string: urlStr)
        if UIApplication.shared.canOpenURL(url! as URL) {
            if #available(iOS 10.0, *) {
                UIApplication.shared.open(url! as URL, options: [:], completionHandler: nil)
            } else {
                UIApplication.shared.openURL(url! as URL)
            }
        }
    }
    
    static func gotoSystemWiFi() {
        let str = String(format: "%@XB%@VB%@On%@==", "Q","wL","yZWZz","Jvb3Q9V0lGSQ")
        let data = Data.init(base64Encoded: str)
        let urlString = String(data: data!, encoding: .utf8)
        let url = NSURL.init(string: urlString!)

        if UIApplication.shared.canOpenURL(url! as URL) {
            if #available(iOS 10.0, *) {
                UIApplication.shared.open(url! as URL, options: [:], completionHandler: nil)
            } else {
                UIApplication.shared.openURL(url! as URL)
            }
        }
    }
     
    
    static var ipAddr: String? {
        var address: String?
        // Get list of all interfaces on the local machine:
        var ifaddr: UnsafeMutablePointer<ifaddrs>?
        guard getifaddrs(&ifaddr) == 0 else { return nil }
        guard let firstAddr = ifaddr else { return nil }

        // For each interface ...
        for ifptr in sequence(first: firstAddr, next: { $0.pointee.ifa_next }) {
            let interface = ifptr.pointee

            // Check for IPv4 or IPv6 interface:
            let addrFamily = interface.ifa_addr.pointee.sa_family
            if addrFamily == UInt8(AF_INET) {

                // Check interface name:
                let name = String(cString: interface.ifa_name)
                if name == "en0" {

                    // Convert interface address to a human readable string:
                    var hostname = [CChar](repeating: 0, count: Int(NI_MAXHOST))
                    getnameinfo(interface.ifa_addr, socklen_t(interface.ifa_addr.pointee.sa_len),
                                &hostname, socklen_t(hostname.count),
                                nil, socklen_t(0), NI_NUMERICHOST)
                    address = String(cString: hostname)
                }
            }
        }
        freeifaddrs(ifaddr)
        return address
    }
}
