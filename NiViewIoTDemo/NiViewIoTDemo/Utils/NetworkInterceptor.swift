//
//  NetworkInterceptor.swift
//  NIotDemo
//
//  Created by apple on 2024/12/19.
//

import UIKit
import NiViewIoT
import MBProgressHUD

/// 网络请求拦截器协议
protocol NetworkInterceptor {
    func willSendRequest(config: NetworkRequestConfig)
    func didReceiveResponse(config: NetworkRequestConfig, result: NetworkResult)
    func didReceiveError(config: NetworkRequestConfig, error: Error)
}

/// 网络请求配置
struct NetworkRequestConfig {
    let identifier: String
    let loadingMessage: String
    let showLoading: Bool
    let showErrorAlert: Bool
    let autoHandleCommonErrors: Bool
    let viewController: UIViewController?
    
    init(
        identifier: String = UUID().uuidString,
        loadingMessage: String = "Loading...",
        showLoading: Bool = true,
        showErrorAlert: Bool = true,
        autoHandleCommonErrors: Bool = true,
        viewController: UIViewController? = nil
    ) {
        self.identifier = identifier
        self.loadingMessage = loadingMessage
        self.showLoading = showLoading
        self.showErrorAlert = showErrorAlert
        self.autoHandleCommonErrors = autoHandleCommonErrors
        self.viewController = viewController
    }
}

/// 网络请求结果
enum NetworkResult {
    case success(Any?)
    case failure(Error)
}

/// 默认的网络拦截器实现
class DefaultNetworkInterceptor: NetworkInterceptor {
    
    private var activeHUDs: [String: MBProgressHUD] = [:]
    
    func willSendRequest(config: NetworkRequestConfig) {
        guard config.showLoading, let viewController = config.viewController else { return }
        
        DispatchQueue.main.async { [weak self] in
            let hud = viewController.showLoading(config.loadingMessage)
            self?.activeHUDs[config.identifier] = hud
        }
    }
    
    func didReceiveResponse(config: NetworkRequestConfig, result: NetworkResult) {
        DispatchQueue.main.async { [weak self] in
            self?.hideHUD(for: config.identifier)
            
            switch result {
            case .success:
                break // 成功情况通常不需要额外处理
            case .failure(let error):
                self?.didReceiveError(config: config, error: error)
            }
        }
    }
    
    func didReceiveError(config: NetworkRequestConfig, error: Error) {
        guard config.autoHandleCommonErrors else { return }
        
        DispatchQueue.main.async { [weak self] in
            self?.handleCommonErrors(error, config: config)
        }
    }
    
    private func hideHUD(for identifier: String) {
        activeHUDs[identifier]?.hide()
        activeHUDs.removeValue(forKey: identifier)
    }
    
    private func handleCommonErrors(_ error: Error, config: NetworkRequestConfig) {
        guard let viewController = config.viewController else { return }
        
        if let nvError = error as? NVError {
            switch nvError.code {
            case -401: // Token 失效
                handleTokenExpired(from: viewController)
                return
            case -429: // 服务繁忙
                if config.showErrorAlert {
                    viewController.showError("Service is busy, please try again later")
                }
                return
            case -4019: // 账号或密码错误
                if config.showErrorAlert {
                    viewController.showError("Account or password error")
                }
                return
            case -4026: // 设备已被其他用户绑定
                if config.showErrorAlert {
                    viewController.showError("Device has been bound by another user")
                }
                return
            default:
                break
            }
        }
        
        // 显示通用错误
        if config.showErrorAlert {
            let message = error.localizedDescription
            viewController.showError("Request failed: \(message)")
        }
    }
    
    private func handleTokenExpired(from viewController: UIViewController) {
        viewController.showError("Session expired, please login again") {
            DispatchQueue.main.async {
                let sb = UIStoryboard(name: "Main", bundle: nil)
                let loginVc = sb.instantiateViewController(withIdentifier: "loginVc")
                UIApplication.shared.keyWindow?.rootViewController = loginVc
                Environment.shared.update(token: "")
            }
        }
    }
}

/// 网络请求管理器 - 支持拦截器
class InterceptableNetworkManager {
    
    static let shared = InterceptableNetworkManager()
    
    private var interceptors: [NetworkInterceptor] = [DefaultNetworkInterceptor()]
    
    private init() {}
    
    /// 添加拦截器
    func addInterceptor(_ interceptor: NetworkInterceptor) {
        interceptors.append(interceptor)
    }
    
    /// 移除所有拦截器
    func clearInterceptors() {
        interceptors.removeAll()
    }
    
    /// 执行网络请求
    func performRequest<T>(
        config: NetworkRequestConfig,
        request: @escaping (@escaping (T?, Error?) -> Void) -> Void,
        completion: @escaping (T?) -> Void
    ) {
        // 通知拦截器请求开始
        interceptors.forEach { $0.willSendRequest(config: config) }
        
        request { [weak self] result, error in
            if let error = error {
                let networkResult = NetworkResult.failure(error)
                self?.interceptors.forEach { $0.didReceiveResponse(config: config, result: networkResult) }
            } else {
                let networkResult = NetworkResult.success(result)
                self?.interceptors.forEach { $0.didReceiveResponse(config: config, result: networkResult) }
                completion(result)
            }
        }
    }
}

// MARK: - 便利方法扩展
extension InterceptableNetworkManager {
    
    /// 发送验证码
    func sendVerifyCode(
        email: String,
        continent: String? = nil,
        from viewController: UIViewController,
        completion: @escaping (Bool) -> Void
    ) {
        let config = NetworkRequestConfig(
            loadingMessage: "Sending verification code...",
            viewController: viewController
        )
        
        performRequest(config: config) { handler in
            NIotNetworking.shared.sendVerifyCodeFor(email: email, continent: continent) { data, error in
                handler(data, error)
            }
        } completion: { _ in
            viewController.showMsg("Verification code sent successfully")
            completion(true)
        }
    }
    
    /// 登录
    func login(
        email: String,
        verifyCode: String,
        from viewController: UIViewController,
        completion: @escaping ([String: Any]?) -> Void
    ) {
        let config = NetworkRequestConfig(
            loadingMessage: "Logging in...",
            viewController: viewController
        )
        
        performRequest(config: config) { handler in
            NIotNetworking.shared.login(email: email, verifyCode: verifyCode) { data, error in
                handler(data, error)
            }
        } completion: { data in
            completion(data as? [String: Any])
        }
    }
    
    /// 获取设备列表
    func getDeviceList(
        uid: Int64,
        from viewController: UIViewController,
        silently: Bool = false,
        completion: @escaping ([NVDeviceModel]) -> Void
    ) {
        let config = NetworkRequestConfig(
            loadingMessage: "Loading devices...",
            showLoading: !silently,
            showErrorAlert: !silently,
            viewController: viewController
        )
        
        performRequest(config: config) { handler in
            NIotNetworking.shared.getDeviceList(uid: uid) { devices, error in
                handler(devices, error)
            }
        } completion: { devices in
            completion(devices as? [NVDeviceModel] ?? [])
        }
    }
}

// MARK: - 自定义拦截器示例
class LoggingNetworkInterceptor: NetworkInterceptor {
    
    func willSendRequest(config: NetworkRequestConfig) {
        print("🟡 [Network] Starting request: \(config.identifier)")
    }
    
    func didReceiveResponse(config: NetworkRequestConfig, result: NetworkResult) {
        switch result {
        case .success:
            print("🟢 [Network] Request succeeded: \(config.identifier)")
        case .failure(let error):
            print("🔴 [Network] Request failed: \(config.identifier), error: \(error)")
        }
    }
    
    func didReceiveError(config: NetworkRequestConfig, error: Error) {
        // 这里可以添加错误上报逻辑
        print("📊 [Analytics] Error reported: \(error)")
    }
}

// MARK: - 使用示例
/*
// 在 AppDelegate 中设置
InterceptableNetworkManager.shared.addInterceptor(LoggingNetworkInterceptor())

// 在 ViewController 中使用
InterceptableNetworkManager.shared.sendVerifyCode(email: "<EMAIL>", from: self) { success in
    // 处理结果
}
*/
