//
//  NetworkManager.swift
//  NIotDemo
//
//  Created by apple on 2024/12/19.
//

import UIKit
import NiViewIoT
import MBProgressHUD

/// 网络请求管理器 - 统一处理 HUD 和错误逻辑
class NetworkManager {
    
    static let shared = NetworkManager()
    private init() {}
    
    /// 网络请求配置
    struct RequestConfig {
        let loadingMessage: String
        let showLoading: Bool
        let showErrorAlert: Bool
        let autoHandleCommonErrors: Bool
        
        static let `default` = RequestConfig(
            loadingMessage: "Loading...",
            showLoading: true,
            showErrorAlert: true,
            autoHandleCommonErrors: true
        )
        
        static let silent = RequestConfig(
            loadingMessage: "",
            showLoading: false,
            showErrorAlert: false,
            autoHandleCommonErrors: true
        )
    }
}

// MARK: - 用户相关接口
extension NetworkManager {
    
    /// 发送验证码
    func sendVerifyCode(
        email: String,
        continent: String? = nil,
        from viewController: UIViewController,
        config: RequestConfig = .default,
        completion: @escaping (Bool) -> Void
    ) {
        performRequest(
            from: viewController,
            config: config.with(loadingMessage: "Sending verification code...")
        ) { handler in
            NIotNetworking.shared.sendVerifyCodeFor(email: email, continent: continent) { data, error in
                handler(data, error)
            }
        } completion: { _ in
            viewController.showMsg("Verification code sent successfully")
            completion(true)
        }
    }
    
    /// 邮箱验证码登录
    func login(
        email: String,
        verifyCode: String,
        from viewController: UIViewController,
        config: RequestConfig = .default,
        completion: @escaping ([String: Any]?) -> Void
    ) {
        performRequest(
            from: viewController,
            config: config.with(loadingMessage: "Logging in...")
        ) { handler in
            NIotNetworking.shared.login(email: email, verifyCode: verifyCode) { data, error in
                handler(data, error)
            }
        } completion: { data in
            completion(data as? [String: Any])
        }
    }
    
    /// 退出登录
    func logout(
        uid: Int64,
        from viewController: UIViewController,
        config: RequestConfig = .default,
        completion: @escaping (Bool) -> Void
    ) {
        performRequest(
            from: viewController,
            config: config.with(loadingMessage: "Logging out...")
        ) { handler in
            NIotNetworking.shared.loginOut(uid: uid) { data, error in
                handler(data, error)
            }
        } completion: { _ in
            completion(true)
        }
    }
}

// MARK: - 设备相关接口
extension NetworkManager {
    
    /// 获取设备列表
    func getDeviceList(
        uid: Int64,
        from viewController: UIViewController,
        config: RequestConfig = .default,
        completion: @escaping ([NVDeviceModel]) -> Void
    ) {
        performRequest(
            from: viewController,
            config: config.with(loadingMessage: "Loading devices...")
        ) { handler in
            NIotNetworking.shared.getDeviceList(uid: uid) { devices, error in
                handler(devices, error)
            }
        } completion: { devices in
            completion(devices as? [NVDeviceModel] ?? [])
        }
    }
    
    /// 绑定设备
    func bindDevice(
        config: DeviceBindingConfig,
        from viewController: UIViewController,
        requestConfig: RequestConfig = .default,
        completion: @escaping (Bool) -> Void
    ) {
        performRequest(
            from: viewController,
            config: requestConfig.with(loadingMessage: "Binding device...")
        ) { handler in
            NIotNetworking.shared.bindDevice(config: config) { data, error in
                handler(data, error)
            }
        } completion: { _ in
            viewController.showMsg("Device bound successfully")
            completion(true)
        }
    }
    
    /// 解绑设备
    func unbindDevice(
        uid: Int64,
        accessId: Int64,
        deviceModel: NVDeviceModel,
        from viewController: UIViewController,
        config: RequestConfig = .default,
        completion: @escaping (Bool) -> Void
    ) {
        performRequest(
            from: viewController,
            config: config.with(loadingMessage: "Unbinding device...")
        ) { handler in
            NIotNetworking.shared.unbindDevice(uid: uid, accessId: accessId, deviceModel: deviceModel) { error in
                handler(nil, error)
            }
        } completion: { _ in
            viewController.showMsg("Device unbound successfully")
            completion(true)
        }
    }
}

// MARK: - 核心请求方法
extension NetworkManager {
    
    /// 执行网络请求的核心方法
    private func performRequest<T>(
        from viewController: UIViewController,
        config: RequestConfig,
        request: @escaping (@escaping (T?, Error?) -> Void) -> Void,
        completion: @escaping (T) -> Void
    ) {
        var hud: MBProgressHUD?
        
        // 显示加载 HUD
        if config.showLoading {
            hud = viewController.showLoading(config.loadingMessage)
        }
        
        request { [weak viewController] result, error in
            DispatchQueue.main.async {
                // 隐藏 HUD
                hud?.hide()
                
                // 处理错误
                if let error = error {
                    if config.autoHandleCommonErrors {
                        self.handleCommonErrors(error, from: viewController, showAlert: config.showErrorAlert)
                    } else if config.showErrorAlert {
                        viewController?.showError("Request failed: \(error.localizedDescription)")
                    }
                    return
                }
                
                // 处理成功结果
                guard let result = result else {
                    if config.showErrorAlert {
                        viewController?.showError("Request failed")
                    }
                    return
                }
                
                completion(result)
            }
        }
    }
    
    /// 处理通用错误
    private func handleCommonErrors(_ error: Error, from viewController: UIViewController?, showAlert: Bool) {
        guard let viewController = viewController else { return }
        
        if let nvError = error as? NVError {
            switch nvError.code {
            case -401: // Token 失效
                handleTokenExpired(from: viewController)
                return
            case -429: // 服务繁忙
                if showAlert {
                    viewController.showError("Service is busy, please try again later")
                }
                return
            case -4019: // 账号或密码错误
                if showAlert {
                    viewController.showError("Account or password error")
                }
                return
            case -4026: // 设备已被其他用户绑定
                if showAlert {
                    viewController.showError("Device has been bound by another user")
                }
                return
            default:
                break
            }
        }
        
        // 显示通用错误
        if showAlert {
            let message = error.localizedDescription
            viewController.showError("Request failed: \(message)")
        }
    }
    
    /// 处理 Token 过期
    private func handleTokenExpired(from viewController: UIViewController) {
        viewController.showError("Session expired, please login again") {
            DispatchQueue.main.async {
                let sb = UIStoryboard(name: "Main", bundle: nil)
                let loginVc = sb.instantiateViewController(withIdentifier: "loginVc")
                UIApplication.shared.keyWindow?.rootViewController = loginVc
                Environment.shared.update(token: "")
            }
        }
    }
}

// MARK: - RequestConfig 扩展
extension NetworkManager.RequestConfig {
    func with(loadingMessage: String) -> NetworkManager.RequestConfig {
        return NetworkManager.RequestConfig(
            loadingMessage: loadingMessage,
            showLoading: showLoading,
            showErrorAlert: showErrorAlert,
            autoHandleCommonErrors: autoHandleCommonErrors
        )
    }
}
