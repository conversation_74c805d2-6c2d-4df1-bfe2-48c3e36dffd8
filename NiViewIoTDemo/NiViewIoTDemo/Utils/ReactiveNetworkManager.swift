//
//  ReactiveNetworkManager.swift
//  NIotDemo
//
//  Created by apple on 2024/12/19.
//

import UIKit
import NiViewIoT
import MBProgressHUD

/// 网络请求结果
enum NetworkRequestResult<T> {
    case success(T)
    case failure(Error)
}

/// 网络请求状态
enum NetworkRequestState {
    case idle
    case loading
    case success
    case failure(Error)
}

/// 响应式网络请求包装器
class ReactiveNetworkRequest<T> {
    
    private let requestBlock: (@escaping (T?, Error?) -> Void) -> Void
    private let config: NetworkRequestConfig
    
    private var state: NetworkRequestState = .idle {
        didSet {
            DispatchQueue.main.async { [weak self] in
                self?.handleStateChange()
            }
        }
    }
    
    private var hud: MBProgressHUD?
    
    init(
        config: NetworkRequestConfig,
        request: @escaping (@escaping (T?, Error?) -> Void) -> Void
    ) {
        self.config = config
        self.requestBlock = request
    }
    
    /// 执行请求
    @discardableResult
    func execute(completion: @escaping (NetworkRequestResult<T>) -> Void) -> Self {
        state = .loading
        
        requestBlock { [weak self] result, error in
            DispatchQueue.main.async {
                if let error = error {
                    self?.state = .failure(error)
                    completion(.failure(error))
                } else if let result = result {
                    self?.state = .success
                    completion(.success(result))
                } else {
                    let unknownError = NSError(domain: "NetworkError", code: -1, userInfo: [NSLocalizedDescriptionKey: "Unknown error"])
                    self?.state = .failure(unknownError)
                    completion(.failure(unknownError))
                }
            }
        }
        
        return self
    }
    
    /// 链式调用 - 成功回调
    @discardableResult
    func onSuccess(_ handler: @escaping (T) -> Void) -> Self {
        execute { result in
            if case .success(let data) = result {
                handler(data)
            }
        }
        return self
    }
    
    /// 链式调用 - 失败回调
    @discardableResult
    func onFailure(_ handler: @escaping (Error) -> Void) -> Self {
        execute { result in
            if case .failure(let error) = result {
                handler(error)
            }
        }
        return self
    }
    
    /// 链式调用 - 完成回调（无论成功失败）
    @discardableResult
    func onComplete(_ handler: @escaping () -> Void) -> Self {
        execute { _ in
            handler()
        }
        return self
    }
    
    private func handleStateChange() {
        switch state {
        case .idle:
            break
        case .loading:
            showLoadingIfNeeded()
        case .success:
            hideLoadingIfNeeded()
        case .failure(let error):
            hideLoadingIfNeeded()
            handleErrorIfNeeded(error)
        }
    }
    
    private func showLoadingIfNeeded() {
        guard config.showLoading, let viewController = config.viewController else { return }
        hud = viewController.showLoading(config.loadingMessage)
    }
    
    private func hideLoadingIfNeeded() {
        hud?.hide()
        hud = nil
    }
    
    private func handleErrorIfNeeded(_ error: Error) {
        guard config.autoHandleCommonErrors, let viewController = config.viewController else { return }
        
        if let nvError = error as? NVError {
            switch nvError.code {
            case -401: // Token 失效
                handleTokenExpired(from: viewController)
                return
            case -429: // 服务繁忙
                if config.showErrorAlert {
                    viewController.showError("Service is busy, please try again later")
                }
                return
            default:
                break
            }
        }
        
        if config.showErrorAlert {
            viewController.showError("Request failed: \(error.localizedDescription)")
        }
    }
    
    private func handleTokenExpired(from viewController: UIViewController) {
        viewController.showError("Session expired, please login again") {
            DispatchQueue.main.async {
                let sb = UIStoryboard(name: "Main", bundle: nil)
                let loginVc = sb.instantiateViewController(withIdentifier: "loginVc")
                UIApplication.shared.keyWindow?.rootViewController = loginVc
                Environment.shared.update(token: "")
            }
        }
    }
}

/// 响应式网络管理器
class ReactiveNetworkManager {
    
    static let shared = ReactiveNetworkManager()
    private init() {}
    
    /// 创建网络请求
    func createRequest<T>(
        from viewController: UIViewController,
        loadingMessage: String = "Loading...",
        showLoading: Bool = true,
        showErrorAlert: Bool = true,
        autoHandleCommonErrors: Bool = true,
        request: @escaping (@escaping (T?, Error?) -> Void) -> Void
    ) -> ReactiveNetworkRequest<T> {
        
        let config = NetworkRequestConfig(
            loadingMessage: loadingMessage,
            showLoading: showLoading,
            showErrorAlert: showErrorAlert,
            autoHandleCommonErrors: autoHandleCommonErrors,
            viewController: viewController
        )
        
        return ReactiveNetworkRequest(config: config, request: request)
    }
}

// MARK: - 便利方法扩展
extension ReactiveNetworkManager {
    
    /// 发送验证码
    func sendVerifyCode(
        email: String,
        continent: String? = nil,
        from viewController: UIViewController
    ) -> ReactiveNetworkRequest<AnyObject> {
        
        return createRequest(
            from: viewController,
            loadingMessage: "Sending verification code..."
        ) { handler in
            NIotNetworking.shared.sendVerifyCodeFor(email: email, continent: continent) { data, error in
                handler(data, error)
            }
        }
    }
    
    /// 登录
    func login(
        email: String,
        verifyCode: String,
        from viewController: UIViewController
    ) -> ReactiveNetworkRequest<AnyObject> {
        
        return createRequest(
            from: viewController,
            loadingMessage: "Logging in..."
        ) { handler in
            NIotNetworking.shared.login(email: email, verifyCode: verifyCode) { data, error in
                handler(data, error)
            }
        }
    }
    
    /// 获取设备列表
    func getDeviceList(
        uid: Int64,
        from viewController: UIViewController,
        silently: Bool = false
    ) -> ReactiveNetworkRequest<[NVDeviceModel]> {
        
        return createRequest(
            from: viewController,
            loadingMessage: "Loading devices...",
            showLoading: !silently,
            showErrorAlert: !silently
        ) { handler in
            NIotNetworking.shared.getDeviceList(uid: uid) { devices, error in
                handler(devices, error)
            }
        }
    }
    
    /// 绑定设备
    func bindDevice(
        config: DeviceBindingConfig,
        from viewController: UIViewController
    ) -> ReactiveNetworkRequest<AnyObject> {
        
        return createRequest(
            from: viewController,
            loadingMessage: "Binding device..."
        ) { handler in
            NIotNetworking.shared.bindDevice(config: config) { data, error in
                handler(data, error)
            }
        }
    }
    
    /// 解绑设备
    func unbindDevice(
        uid: Int64,
        accessId: Int64,
        deviceModel: NVDeviceModel,
        from viewController: UIViewController
    ) -> ReactiveNetworkRequest<Void> {
        
        return createRequest(
            from: viewController,
            loadingMessage: "Unbinding device..."
        ) { handler in
            NIotNetworking.shared.unbindDevice(uid: uid, accessId: accessId, deviceModel: deviceModel) { error in
                if let error = error {
                    handler(nil, error)
                } else {
                    handler((), nil)
                }
            }
        }
    }
}

// MARK: - 使用示例
/*
// 链式调用示例
ReactiveNetworkManager.shared
    .sendVerifyCode(email: "<EMAIL>", from: self)
    .onSuccess { data in
        print("验证码发送成功")
        self.showMsg("Verification code sent successfully")
    }
    .onFailure { error in
        print("发送失败: \(error)")
    }

// 复杂的链式调用
ReactiveNetworkManager.shared
    .login(email: email, verifyCode: code, from: self)
    .onSuccess { userData in
        self.loginSucceed(userData)
        
        // 登录成功后获取设备列表
        guard let uid = Environment.shared.uid else { return }
        ReactiveNetworkManager.shared
            .getDeviceList(uid: uid, from: self)
            .onSuccess { devices in
                self.updateDeviceList(devices)
            }
    }
    .onFailure { error in
        print("登录失败: \(error)")
    }

// 静默请求（不显示 HUD）
ReactiveNetworkManager.shared
    .getDeviceList(uid: uid, from: self, silently: true)
    .onSuccess { devices in
        self.refreshDeviceList(devices)
    }
*/
