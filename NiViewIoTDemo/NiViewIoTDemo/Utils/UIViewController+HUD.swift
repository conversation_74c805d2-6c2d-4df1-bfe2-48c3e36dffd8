//
//  UIViewController+HUD.swift
//  NIotDemo
//
//  Created by apple on 2021/11/25.
//

import UIKit
import MBProgressHUD
import NiViewIoT


extension UIViewController {

    @discardableResult
    func showLoading(_ msg: String = "") -> MBProgressHUD {
        let hud = MBProgressHUD.showAdded(to: view, animated: true)
        hud.mode = .annularDeterminate
        hud.label.text = msg
        return hud
    }

    @discardableResult
    func showMsg(_ msg: String, delay: TimeInterval = 1) -> MBProgressHUD {
        let hud = MBProgressHUD.showAdded(to: view, animated: true)
        hud.mode = .text
        hud.label.text = msg
        hud.label.numberOfLines = 0
        hud.hide(animated: true, afterDelay: delay)
        return hud
    }

    @discardableResult
    func showError(_ error: String, delay: TimeInterval = 1.5) -> MBProgressHUD {
        let hud = MBProgressHUD.showAdded(to: view, animated: true)
        hud.mode = .text
        hud.label.text = error
        hud.hide(animated: true, afterDelay: delay)
        return hud
    }
}

extension MBProgressHUD {
    func hide() {
        hide(animated: false)
    }
}

// MARK: - 网络请求包装器
extension UIViewController {

    /// 带 HUD 的网络请求包装器
    /// - Parameters:
    ///   - loadingMessage: 加载提示文字
    ///   - showErrorAlert: 是否显示错误弹窗
    ///   - request: 网络请求闭包
    ///   - completion: 成功回调
    func performNetworkRequest<T>(
        loadingMessage: String = "Loading...",
        showErrorAlert: Bool = true,
        request: @escaping (@escaping (T?, Error?) -> Void) -> Void,
        completion: @escaping (T) -> Void
    ) {
        let hud = showLoading(loadingMessage)

        request { [weak self] result, error in
            DispatchQueue.main.async {
                hud.hide()

                if let error = error {
                    self?.handleNetworkError(error, showAlert: showErrorAlert)
                    return
                }

                guard let result = result else {
                    if showErrorAlert {
                        self?.showError("Request failed")
                    }
                    return
                }

                completion(result)
            }
        }
    }

    /// 统一处理网络错误
    private func handleNetworkError(_ error: Error, showAlert: Bool) {
        // 处理特殊错误码
        if let nvError = error as? NVError {
            switch nvError.code {
            case -401: // Token 失效
                handleTokenExpired()
                return
            case -429: // 服务繁忙
                if showAlert {
                    showError("Service is busy, please try again later")
                }
                return
            default:
                break
            }
        }

        // 显示通用错误
        if showAlert {
            let message = error.localizedDescription
            showError("Request failed: \(message)")
        }
    }

    /// 处理 Token 过期
    private func handleTokenExpired() {
        showError("Session expired, please login again") { [weak self] in
            // 跳转到登录页面
            self?.navigateToLogin()
        }
    }

    /// 跳转到登录页面
    private func navigateToLogin() {
        DispatchQueue.main.async {
            let sb = UIStoryboard(name: "Main", bundle: nil)
            let loginVc = sb.instantiateViewController(withIdentifier: "loginVc")
            UIApplication.shared.keyWindow?.rootViewController = loginVc
            Environment.shared.update(token: "")
        }
    }
}

// MARK: - 扩展 showError 支持回调
extension UIViewController {
    @discardableResult
    func showError(_ error: String, delay: TimeInterval = 1.5, completion: (() -> Void)? = nil) -> MBProgressHUD {
        let hud = MBProgressHUD.showAdded(to: view, animated: true)
        hud.mode = .text
        hud.label.text = error
        hud.hide(animated: true, afterDelay: delay)

        if let completion = completion {
            DispatchQueue.main.asyncAfter(deadline: .now() + delay) {
                completion()
            }
        }

        return hud
    }
}
