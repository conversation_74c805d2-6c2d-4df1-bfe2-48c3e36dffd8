//
//  UIViewController+HUD.swift
//  NIotDemo
//
//  Created by apple on 2021/11/25.
//

import UIKit
import MBProgressHUD
 
 
extension UIViewController {
    
    @discardableResult
    func showLoading(_ msg: String = "") -> MBProgressHUD {
        let hud = MBProgressHUD.showAdded(to: view, animated: true)
        hud.mode = .annularDeterminate
        hud.label.text = msg
        return hud
    }
    
    @discardableResult
    func showMsg(_ msg: String, delay: TimeInterval = 1) -> MBProgressHUD {
        let hud = MBProgressHUD.showAdded(to: view, animated: true)
        hud.mode = .text
        hud.label.text = msg
        hud.label.numberOfLines = 0
        hud.hide(animated: true, afterDelay: delay)
        return hud
    }
    
    @discardableResult
    func showError(_ error: String, delay: TimeInterval = 1.5) -> MBProgressHUD {
        let hud = MBProgressHUD.showAdded(to: view, animated: true)
        hud.mode = .text
        hud.label.text = error
        hud.hide(animated: true, afterDelay: delay)
        return hud
    }
}

extension MBProgressHUD {
    func hide() {
        hide(animated: false)
    }
}
