# Uncomment the next line to define a global platform for your project
# platform :ios, '9.0'

platform :ios, '13.0'
use_frameworks!

workspace 'NiViewIoT.xcworkspace'

def common_pods
  pod 'Alamofire',              '~> 5.4.4'
  pod 'CryptoSwift',            '~> 1.0'
  pod 'AFNetworking'
  pod 'MBProgressHUD', '1.1.0'
  pod 'HandyJSON'
  pod 'CocoaMQTT'
  pod 'IVDevTools'
  
  pod 'TIoTLinkVideo', '2.4.40'
  pod 'TIoTLinkKit_GVoiceSE'
  pod 'TIoTLinkKit_IJKPlayer', '2.0.13'
  pod 'CocoaAsyncSocket', '7.6.5'
  pod 'CocoaLumberjack', '3.7.2'
  pod 'TXLiteAVSDK_TRTC', '8.0.9644'
  pod 'YYModel', '1.0.4'
end

target 'NiViewIoTDemo' do
  project 'NiViewIoTDemo/NiViewIoTDemo.xcodeproj'
  common_pods
end

target 'NiViewIoT' do
  use_frameworks!
  project 'NiViewIoT/NiViewIoT.xcodeproj'
  common_pods
  
end

post_install do |installer|
  installer.pods_project.build_configurations.each do |config|
    config.build_settings["BUILD_LIBRARY_FOR_DISTRIBUTION"] = "NO"
  end

  installer.pods_project.targets.each do |t|
    t.build_configurations.each do |config|
      config.build_settings["BUILD_LIBRARY_FOR_DISTRIBUTION"] = "NO"
     end
  end
  ############# 2.0 #############
  
  # [SDK 2.0] 往 TIoTLinkKit-umbrella.h 里添加声明
  replace1 = <<-STRING
    #import <MBProgressHUD/MBProgressHUD.h>
    #import <TXLiteAVSDK_TRTC/TRTCCloud.h>

    #import "NSObject+additions.h"
  STRING
  
  find_and_replace("Pods/Target\\ Support\\ Files/TIoTLinkKit/TIoTLinkKit-umbrella.h", "#import \"NSObject+additions.h\"", replace1)
  
  # [SDK 2.0] 往 TIoTLinkVideo.modulemap 里添加 TIoTLinkKit_XP2P 子模块
  
  replace2 = <<-STRING
    module * { export * }
    explicit module TIoTLinkKit_XP2P {
        header "/Users/<USER>/Desktop/NiViewIoT/Pods/TIoTLinkKit_XP2P/Source/XP2P-iOS/Classes/AppWrapper.h"
        export *
    }
  STRING
  
  find_and_replace("Pods/Target\\ Support\\ Files/TIoTLinkVideo/TIoTLinkVideo.modulemap", "module * { export * }", replace2)
  
  ############# 2.0 #############
end


def find_and_replace(dir, findstr, replacestr)
  Dir[dir].each do |name|
      text = File.read(name)
      replace = text.gsub(findstr,replacestr)
      if text != replace
          puts "Fix: " + name
          File.open(name, "w") { |file| file.puts replace }
          STDOUT.flush
      end
  end
  Dir[dir + '*/'].each(&method(:find_and_replace))
end


#  解决 Xcode 15.3编译 HandyJSON 失败问题
#  搜索“for i in 0..<self.numberOfFields”， Metadata.swift，290行
#  替换为以下代码
#  for i in 0..<self.numberOfFields where fieldRecords[i].mangledTypeName != nil{
#      let name = fieldRecords[i].fieldName
#      let cMangledTypeName = fieldRecords[i].mangledTypeName!
#
#      let functionMap: [String: () -> Any.Type?] = [
#          "function": { _getTypeByMangledNameInContext(cMangledTypeName, UInt(getMangledTypeNameSize(cMangledTypeName)), genericContext: self.contextDescriptorPointer, genericArguments: self.genericArgumentVector) }
#      ]
#      if let function = functionMap["function"],let fieldType  = function() {
#          result.append(Property.Description(key: name, type: fieldType, offset: fieldOffsets[i]))
#      }
#  }
#  return result
